import {
  ActivityDecorationDatRequest,
  ActivityDecorationDatResponse,
  ActivityShopGradeResponse,
  ActivityTemplateRequest,
  ActivityTemplateResponse,
  ActivityTypeResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 活动基础信息管理
 * @summary 查询所有活动类型
 * @request POST:/common/activity/getAllActivityTypes
 */
export const activityGetAllActivityTypes = (): Promise<ActivityTypeResponse[]> => {
  return httpRequest({
    url: '/common/activity/getAllActivityTypes',
    method: 'post',
  });
};

/**
 * @tags 活动基础信息管理
 * @summary 查询装修数据
 * @request POST:/common/activity/getDecorationData
 */
export const activityGetDecorationData = (
  activityDecorationDatRequest: ActivityDecorationDatRequest,
): Promise<ActivityDecorationDatResponse> => {
  return httpRequest({
    url: '/common/activity/getDecorationData',
    method: 'post',
    data: activityDecorationDatRequest,
  });
};

/**
 * @tags 商家信息
 * @summary getMemberLevel
 * @request POST:/common/shop/getMemberLevel
 */
export const shopGetMemberLevel = (): Promise<ActivityShopGradeResponse[]> => {
  return httpRequest({
    url: '/common/shop/getMemberLevel',
    method: 'post',
  });
};

/**
 * @tags 活动模板
 * @summary 获取活动模板
 * @request POST:/common/template/getActivityTemplate
 */
export const templateGetActivityTemplate = (
  activityTemplateRequest: ActivityTemplateRequest,
): Promise<ActivityTemplateResponse[]> => {
  return httpRequest({
    url: '/common/template/getActivityTemplate',
    method: 'post',
    data: activityTemplateRequest,
  });
};
