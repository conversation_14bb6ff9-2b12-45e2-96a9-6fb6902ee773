import { Activity10002CreateOrUpdateRequest, Activity10002CreateOrUpdateResponse } from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 大转盘抽奖
 * @summary 创建大转盘抽奖活动
 * @request POST:/10002/createActivity
 */
export const createActivity = (
  activity10002CreateOrUpdateRequest: Activity10002CreateOrUpdateRequest,
): Promise<Activity10002CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/10002/createActivity',
    method: 'post',
    data: activity10002CreateOrUpdateRequest,
  });
};

/**
 * @tags 大转盘抽奖
 * @summary 修改满额有礼活动
 * @request POST:/10002/updateActivity
 */
export const updateActivity = (
  activity10002CreateOrUpdateRequest: Activity10002CreateOrUpdateRequest,
): Promise<Activity10002CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/10002/updateActivity',
    method: 'post',
    data: activity10002CreateOrUpdateRequest,
  });
};
