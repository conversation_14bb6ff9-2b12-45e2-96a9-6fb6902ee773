import {
  IPageRoleListResponse,
  RoleAddRequest,
  RoleBindMenuRequest,
  RoleDelRequest,
  RoleListRequest,
  RoleUpdateRequest,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 角色接口
 * @summary getRoleList
 * @request POST:/role/getRoleList
 */
export const getRoleList = (roleListRequest: RoleListRequest): Promise<IPageRoleListResponse> => {
  return httpRequest({
    url: '/role/getRoleList',
    method: 'post',
    data: roleListRequest,
  });
};

/**
 * @tags 角色接口
 * @summary roleAdd
 * @request POST:/role/roleAdd
 */
export const roleAdd = (roleAddRequest: RoleAddRequest): Promise<void> => {
  return httpRequest({
    url: '/role/roleAdd',
    method: 'post',
    data: roleAddRequest,
  });
};

/**
 * @tags 角色接口
 * @summary roleBindMenu
 * @request POST:/role/roleBindMenu
 */
export const roleBindMenu = (roleBindMenuRequest: RoleBindMenuRequest): Promise<void> => {
  return httpRequest({
    url: '/role/roleBindMenu',
    method: 'post',
    data: roleBindMenuRequest,
  });
};

/**
 * @tags 角色接口
 * @summary roleDel
 * @request POST:/role/roleDel
 */
export const roleDel = (roleDelRequest: RoleDelRequest): Promise<void> => {
  return httpRequest({
    url: '/role/roleDel',
    method: 'post',
    data: roleDelRequest,
  });
};

/**
 * @tags 角色接口
 * @summary roleUpdate
 * @request POST:/role/roleUpdate
 */
export const roleUpdate = (roleUpdateRequest: RoleUpdateRequest): Promise<void> => {
  return httpRequest({
    url: '/role/roleUpdate',
    method: 'post',
    data: roleUpdateRequest,
  });
};
