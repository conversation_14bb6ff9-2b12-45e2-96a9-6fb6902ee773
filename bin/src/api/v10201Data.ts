import {
  Activity10201UserPrizeRecordPageRequest,
  Activity10201UserWinningLogRequest,
  CollectingTankDTO,
  CollectingTankNewResponse,
  CollectingTankRequest,
  IPageActivity10201UserPotRecordPageResponse,
  IPageActivity10201UserWinningLogResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 集罐有礼数据
 * @summary export
 * @request POST:/10201Data/data/export
 */
export const dataExport = (collectingTankRequest: CollectingTankRequest): Promise<void> => {
  return httpRequest({
    url: '/10201Data/data/export',
    method: 'post',
    data: collectingTankRequest,
  });
};

/**
 * @tags 集罐有礼数据
 * @summary getReportTaskPage
 * @request POST:/10201Data/reportPage
 */
export const reportPage = (collectingTankRequest: CollectingTankRequest): Promise<CollectingTankDTO> => {
  return httpRequest({
    url: '/10201Data/reportPage',
    method: 'post',
    data: collectingTankRequest,
  });
};

/**
 * @tags 集罐有礼数据
 * @summary getReportTaskPageNew
 * @request POST:/10201Data/reportPageNew
 */
export const reportPageNew = (collectingTankRequest: CollectingTankRequest): Promise<CollectingTankNewResponse[]> => {
  return httpRequest({
    url: '/10201Data/reportPageNew',
    method: 'post',
    data: collectingTankRequest,
  });
};

/**
 * @tags 集罐有礼数据
 * @summary reportPageNewExport
 * @request POST:/10201Data/reportPageNew/export
 */
export const reportPageNewExport = (collectingTankRequest: CollectingTankRequest): Promise<void> => {
  return httpRequest({
    url: '/10201Data/reportPageNew/export',
    method: 'post',
    data: collectingTankRequest,
  });
};

/**
 * @tags 集罐有礼数据
 * @summary 用户罐数记录列表
 * @request POST:/10201Data/userPotRecordPage
 */
export const userPotRecordPage = (
  request: Activity10201UserPrizeRecordPageRequest,
): Promise<IPageActivity10201UserPotRecordPageResponse> => {
  return httpRequest({
    url: '/10201Data/userPotRecordPage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 集罐有礼数据
 * @summary 用户罐数记录列表导出
 * @request POST:/10201Data/userPotRecordPage/export
 */
export const userPotRecordPageExport = (request: Activity10201UserPrizeRecordPageRequest): Promise<void> => {
  return httpRequest({
    url: '/10201Data/userPotRecordPage/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 集罐有礼数据
 * @summary 奖品领取记录
 * @request POST:/10201Data/winningLog
 */
export const winningLog = (
  activity10201UserWinningLogRequest: Activity10201UserWinningLogRequest,
): Promise<IPageActivity10201UserWinningLogResponse> => {
  return httpRequest({
    url: '/10201Data/winningLog',
    method: 'post',
    data: activity10201UserWinningLogRequest,
  });
};

/**
 * @tags 集罐有礼数据
 * @summary 奖品领取记录导出
 * @request POST:/10201Data/winningLog/export
 */
export const winningLogExport = (
  activity10201UserWinningLogRequest: Activity10201UserWinningLogRequest,
): Promise<void> => {
  return httpRequest({
    url: '/10201Data/winningLog/export',
    method: 'post',
    data: activity10201UserWinningLogRequest,
  });
};
