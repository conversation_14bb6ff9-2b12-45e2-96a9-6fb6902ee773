import {
  AlibabaBenefitCenterVO,
  IPageAlibabaBenefitCenterVO,
  IPagePrizeSkuOrderPageResponseVO,
  IPagePrizeSkuRelationActivityPageResponseVO,
  PrizeCouponRequest,
  PrizeCouponResponse,
  PrizeSkuAddRequestVO,
  PrizeSkuAddStockRequestVO,
  PrizeSkuDealRequestVO,
  PrizeSkuDeleteRequestVO,
  PrizeSkuOrderExportResponseVO,
  PrizeSkuOrderRequestVO,
  PrizeSkuPageRequestVO,
  PrizeSkuRelationActivityPageRequestVO,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 实物奖品接口
 * @summary 新增实物奖品
 * @request POST:/vf/prize/addSku
 */
export const prizeAddSku = (request: PrizeSkuAddRequestVO): Promise<void> => {
  return httpRequest({
    url: '/vf/prize/addSku',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 实物奖品接口
 * @summary 追加库存
 * @request POST:/vf/prize/addStock
 */
export const prizeAddStock = (request: PrizeSkuAddStockRequestVO): Promise<void> => {
  return httpRequest({
    url: '/vf/prize/addStock',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 实物奖品接口
 * @summary 删除实物奖品
 * @request POST:/vf/prize/deleteSku
 */
export const prizeDeleteSku = (request: PrizeSkuDeleteRequestVO): Promise<void> => {
  return httpRequest({
    url: '/vf/prize/deleteSku',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 实物奖品接口
 * @summary 分页查询绑定的活动
 * @request POST:/vf/prize/getBindActivityPage
 */
export const prizeGetBindActivityPage = (
  request: PrizeSkuRelationActivityPageRequestVO,
): Promise<IPagePrizeSkuRelationActivityPageResponseVO> => {
  return httpRequest({
    url: '/vf/prize/getBindActivityPage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 实物奖品接口
 * @summary 查询优惠券
 * @request POST:/vf/prize/getCouponList
 */
export const prizeGetCouponList = (request: PrizeCouponRequest): Promise<PrizeCouponResponse> => {
  return httpRequest({
    url: '/vf/prize/getCouponList',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 实物奖品接口
 * @summary 获取实物奖品列表
 * @request POST:/vf/prize/getPrizeSkuPage
 */
export const prizeGetPrizeSkuPage = (request: PrizeSkuPageRequestVO): Promise<IPageAlibabaBenefitCenterVO> => {
  return httpRequest({
    url: '/vf/prize/getPrizeSkuPage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 实物奖品接口
 * @summary 获取实物奖品详情
 * @request POST:/vf/prize/getSkuInfo
 */
export const prizeGetSkuInfo = (request: PrizeSkuDeleteRequestVO): Promise<AlibabaBenefitCenterVO> => {
  return httpRequest({
    url: '/vf/prize/getSkuInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 实物奖品接口
 * @summary 批量导入订单发货
 * @request POST:/vf/prize/importSendCodeExcel
 */
export const prizeImportSendCodeExcel = (file: any): Promise<PrizeSkuOrderExportResponseVO> => {
  return httpRequest({
    url: '/vf/prize/importSendCodeExcel',
    method: 'post',
    data: file,
  });
};

/**
 * @tags 实物奖品接口
 * @summary 发货
 * @request POST:/vf/prize/prizeSkuDeal
 */
export const prizePrizeSkuDeal = (requestVO: PrizeSkuDealRequestVO): Promise<void> => {
  return httpRequest({
    url: '/vf/prize/prizeSkuDeal',
    method: 'post',
    data: requestVO,
  });
};

/**
 * @tags 实物奖品接口
 * @summary 下载发货单模板
 * @request POST:/vf/prize/skuPrizeOrder/download
 */
export const prizeSkuPrizeOrderDownload = (): Promise<void> => {
  return httpRequest({
    url: '/vf/prize/skuPrizeOrder/download',
    method: 'post',
  });
};

/**
 * @tags 实物奖品接口
 * @summary 导出发货单
 * @request POST:/vf/prize/skuPrizeOrder/export
 */
export const prizeSkuPrizeOrderExport = (requestVO: PrizeSkuOrderRequestVO): Promise<void> => {
  return httpRequest({
    url: '/vf/prize/skuPrizeOrder/export',
    method: 'post',
    data: requestVO,
  });
};

/**
 * @tags 实物奖品接口
 * @summary 分页查询发货单
 * @request POST:/vf/prize/skuPrizeOrderPage
 */
export const prizeSkuPrizeOrderPage = (
  requestVO: PrizeSkuOrderRequestVO,
): Promise<IPagePrizeSkuOrderPageResponseVO> => {
  return httpRequest({
    url: '/vf/prize/skuPrizeOrderPage',
    method: 'post',
    data: requestVO,
  });
};
