import { MenuListRequest, MenuListResponse, MenuRequest } from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 系统管理-菜单接口
 * @summary getMenuList
 * @request POST:/menu/getMenuList
 */
export const getMenuList = (menuListRequest: MenuListRequest): Promise<MenuListResponse> => {
  return httpRequest({
    url: '/menu/getMenuList',
    method: 'post',
    data: menuListRequest,
  });
};

/**
 * @tags 系统管理-菜单接口
 * @summary menuAdd
 * @request POST:/menu/menuAdd
 */
export const menuAdd = (menuRequest: MenuRequest): Promise<void> => {
  return httpRequest({
    url: '/menu/menuAdd',
    method: 'post',
    data: menuRequest,
  });
};
