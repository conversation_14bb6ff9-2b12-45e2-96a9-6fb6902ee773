import {
  AddLotteryNumRequest,
  BizActivityDataLotteryRecordRequest,
  BizActivityDataOrderItemRequestVO,
  BizActivityDataOrderRecordRequest,
  BizActivityDataOrderRecordRequestVO,
  BizActivityDataPhysicalPrizeInfoRequest,
  BizCreateActivityPlanRequest,
  BizDelStairsLotteryRequest,
  BizExportRequest,
  BizGetActivityInfoRequest,
  BizGetActivityListRequest,
  BizGetActivityPlanRequest,
  BizGetLotteryRecordRequest,
  BizSaveOrUpdateRequest,
  BizUpdateActivityPlanRequest,
  GetAchieveDetailRequest,
  GetEchartsDataRequestVO,
  GetStyleListRequest,
  GetTemplateListRequest,
  LiveRoomConfigResponse,
  LiveRoomConfigUpdateRequest,
  RActivityData,
  RBizCustomizeActivityInfoResponse,
  RGetAchieveInfoData,
  RGetEchartsDataResponse,
  RIPageBizActivityDataLotteryRecordResponse,
  RIPageBizActivityDataOrderItemResponseVO,
  RIPageBizActivityDataOrderRecordResponse,
  RIPageBizActivityDataPhysicalPrizeInfoResponse,
  RIPageWarningInfoResponseVO,
  RListDzCrowdConfig,
  RListDzShopGrade,
  RListGetDbDataVO,
  RListMapStringObject,
  RListPromotionVO,
  RMapStringObject,
  RObject,
  RString,
  WarningInfoRequestVO,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags biz-activity-controller
 * @summary delLottery
 * @request POST:/b/activity/customizeActivity/delLottery
 */
export const activityCustomizeActivityDelLottery = (request: BizDelStairsLotteryRequest): Promise<RString> => {
  return httpRequest({
    url: '/b/activity/customizeActivity/delLottery',
    method: 'post',
    data: request,
  });
};

/**
 * @tags biz-activity-controller
 * @summary delStairs
 * @request POST:/b/activity/customizeActivity/delStairs
 */
export const activityCustomizeActivityDelStairs = (request: BizDelStairsLotteryRequest): Promise<RString> => {
  return httpRequest({
    url: '/b/activity/customizeActivity/delStairs',
    method: 'post',
    data: request,
  });
};

/**
 * @tags biz-activity-controller
 * @summary getActivityInfo
 * @request POST:/b/activity/customizeActivity/getActivityInfo
 */
export const activityCustomizeActivityGetActivityInfo = (
  request: BizGetActivityInfoRequest,
): Promise<RBizCustomizeActivityInfoResponse> => {
  return httpRequest({
    url: '/b/activity/customizeActivity/getActivityInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags biz-activity-controller
 * @summary minusLotteryNum
 * @request POST:/b/activity/customizeActivity/minusLotteryNum
 */
export const activityCustomizeActivityMinusLotteryNum = (request: BizDelStairsLotteryRequest): Promise<RString> => {
  return httpRequest({
    url: '/b/activity/customizeActivity/minusLotteryNum',
    method: 'post',
    data: request,
  });
};

/**
 * @tags biz-activity-controller
 * @summary saveOrUpdate
 * @request POST:/b/activity/customizeActivity/saveOrUpdate
 */
export const activityCustomizeActivitySaveOrUpdate = (request: BizSaveOrUpdateRequest): Promise<RObject> => {
  return httpRequest({
    url: '/b/activity/customizeActivity/saveOrUpdate',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 活动数据-控制器
 * @summary 导出领奖记录
 * @request POST:/b/activity/data/exportLotteryRecord/export
 */
export const activityDataExportLotteryRecordExport = (request: BizActivityDataLotteryRecordRequest): Promise<void> => {
  return httpRequest({
    url: '/b/activity/data/exportLotteryRecord/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 活动数据-控制器
 * @summary 导出订单记录
 * @request POST:/b/activity/data/exportOrderRecord/export
 */
export const activityDataExportOrderRecordExport = (request: BizActivityDataOrderRecordRequest): Promise<void> => {
  return httpRequest({
    url: '/b/activity/data/exportOrderRecord/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 活动数据-控制器
 * @summary 导出实物中奖信息
 * @request POST:/b/activity/data/exportPhysicalPrizeInfo/export
 */
export const activityDataExportPhysicalPrizeInfoExport = (
  request: BizActivityDataPhysicalPrizeInfoRequest,
): Promise<void> => {
  return httpRequest({
    url: '/b/activity/data/exportPhysicalPrizeInfo/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 活动数据-控制器
 * @summary 领奖记录
 * @request POST:/b/activity/data/getLotteryRecordPage
 */
export const activityDataGetLotteryRecordPage = (
  request: BizActivityDataLotteryRecordRequest,
): Promise<RIPageBizActivityDataLotteryRecordResponse> => {
  return httpRequest({
    url: '/b/activity/data/getLotteryRecordPage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 活动数据-控制器
 * @summary 查看商品明细
 * @request POST:/b/activity/data/getOrderItemPage
 */
export const activityDataGetOrderItemPage = (
  request: BizActivityDataOrderItemRequestVO,
): Promise<RIPageBizActivityDataOrderItemResponseVO> => {
  return httpRequest({
    url: '/b/activity/data/getOrderItemPage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 活动数据-控制器
 * @summary 订单记录
 * @request POST:/b/activity/data/getOrderRecordPage
 */
export const activityDataGetOrderRecordPage = (
  request: BizActivityDataOrderRecordRequestVO,
): Promise<RIPageBizActivityDataOrderRecordResponse> => {
  return httpRequest({
    url: '/b/activity/data/getOrderRecordPage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 活动数据-控制器
 * @summary 实物中奖信息
 * @request POST:/b/activity/data/getPhysicalPrizeInfoPage
 */
export const activityDataGetPhysicalPrizeInfoPage = (
  request: BizActivityDataPhysicalPrizeInfoRequest,
): Promise<RIPageBizActivityDataPhysicalPrizeInfoResponse> => {
  return httpRequest({
    url: '/b/activity/data/getPhysicalPrizeInfoPage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 活动数据-控制器
 * @summary 导出预警提醒记录
 * @request POST:/b/activity/data/warningExportList/export
 */
export const activityDataWarningExportListExport = (request: WarningInfoRequestVO): Promise<void> => {
  return httpRequest({
    url: '/b/activity/data/warningExportList/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 活动数据-控制器
 * @summary 预警提醒记录
 * @request POST:/b/activity/data/warningPageList
 */
export const activityDataWarningPageList = (request: WarningInfoRequestVO): Promise<RIPageWarningInfoResponseVO> => {
  return httpRequest({
    url: '/b/activity/data/warningPageList',
    method: 'post',
    data: request,
  });
};

/**
 * @tags biz-activity-controller
 * @summary delAct
 * @request POST:/b/activity/delAct
 */
export const activityDelAct = (request: BizGetActivityInfoRequest): Promise<RString> => {
  return httpRequest({
    url: '/b/activity/delAct',
    method: 'post',
    data: request,
  });
};

/**
 * @tags biz-activity-controller
 * @summary endAct
 * @request POST:/b/activity/endAct
 */
export const activityEndAct = (request: BizGetActivityInfoRequest): Promise<RString> => {
  return httpRequest({
    url: '/b/activity/endAct',
    method: 'post',
    data: request,
  });
};

/**
 * @tags biz-activity-controller
 * @summary getActivityList
 * @request POST:/b/activity/getActivityList
 */
export const activityGetActivityList = (request: BizGetActivityListRequest): Promise<RObject> => {
  return httpRequest({
    url: '/b/activity/getActivityList',
    method: 'post',
    data: request,
  });
};

/**
 * @tags biz-activity-controller
 * @summary getActivityType
 * @request POST:/b/activity/getActivityType
 */
export const activityGetActivityType = (): Promise<RListMapStringObject> => {
  return httpRequest({
    url: '/b/activity/getActivityType',
    method: 'post',
  });
};

/**
 * @tags biz-activity-controller
 * @summary getCrowdConfigList
 * @request POST:/b/activity/getCrowdConfigList
 */
export const activityGetCrowdConfigList = (request: BizGetActivityInfoRequest): Promise<RListDzCrowdConfig> => {
  return httpRequest({
    url: '/b/activity/getCrowdConfigList',
    method: 'post',
    data: request,
  });
};

/**
 * @tags biz-activity-controller
 * @summary getPromotionEnum
 * @request POST:/b/activity/getPromotionEnum
 */
export const activityGetPromotionEnum = (): Promise<RListPromotionVO> => {
  return httpRequest({
    url: '/b/activity/getPromotionEnum',
    method: 'post',
  });
};

/**
 * @tags biz-activity-controller
 * @summary getShopGrade
 * @request POST:/b/activity/getShopGrade
 */
export const activityGetShopGrade = (): Promise<RListDzShopGrade> => {
  return httpRequest({
    url: '/b/activity/getShopGrade',
    method: 'post',
  });
};

/**
 * @tags biz-activity-controller
 * @summary updateTjImg
 * @request POST:/b/activity/updateTjImg
 */
export const activityUpdateTjImg = (request: BizGetActivityInfoRequest): Promise<RString> => {
  return httpRequest({
    url: '/b/activity/updateTjImg',
    method: 'post',
    data: request,
  });
};

/**
 * @tags echarts-data-controller
 * @summary addLotteryNum
 * @request POST:/b/echarts/addLotteryNum
 */
export const echartsAddLotteryNum = (request: AddLotteryNumRequest): Promise<RString> => {
  return httpRequest({
    url: '/b/echarts/addLotteryNum',
    method: 'post',
    data: request,
  });
};

/**
 * @tags echarts-data-controller
 * @summary getAchieveDetail
 * @request POST:/b/echarts/getAchieveDetail
 */
export const echartsGetAchieveDetail = (request: GetAchieveDetailRequest): Promise<RMapStringObject> => {
  return httpRequest({
    url: '/b/echarts/getAchieveDetail',
    method: 'post',
    data: request,
  });
};

/**
 * @tags echarts-data-controller
 * @summary getAchieveInfo
 * @request POST:/b/echarts/getAchieveInfo
 */
export const echartsGetAchieveInfo = (request: GetEchartsDataRequestVO): Promise<RGetAchieveInfoData> => {
  return httpRequest({
    url: '/b/echarts/getAchieveInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags echarts-data-controller
 * @summary getActivityData
 * @request POST:/b/echarts/getActivityData
 */
export const echartsGetActivityData = (request: GetEchartsDataRequestVO): Promise<RActivityData> => {
  return httpRequest({
    url: '/b/echarts/getActivityData',
    method: 'post',
    data: request,
  });
};

/**
 * @tags echarts-data-controller
 * @summary getActivityDataTable
 * @request POST:/b/echarts/getActivityDataTable
 */
export const echartsGetActivityDataTable = (request: GetEchartsDataRequestVO): Promise<RMapStringObject> => {
  return httpRequest({
    url: '/b/echarts/getActivityDataTable',
    method: 'post',
    data: request,
  });
};

/**
 * @tags echarts-data-controller
 * @summary getCardPromotionRecord
 * @request POST:/b/echarts/getCardPromotionRecord
 */
export const echartsGetCardPromotionRecord = (request: GetEchartsDataRequestVO): Promise<RGetEchartsDataResponse> => {
  return httpRequest({
    url: '/b/echarts/getCardPromotionRecord',
    method: 'post',
    data: request,
  });
};

/**
 * @tags echarts-data-controller
 * @summary getCardTreeData
 * @request POST:/b/echarts/getCardTreeData
 */
export const echartsGetCardTreeData = (request: GetEchartsDataRequestVO): Promise<RListGetDbDataVO> => {
  return httpRequest({
    url: '/b/echarts/getCardTreeData',
    method: 'post',
    data: request,
  });
};

/**
 * @tags echarts-data-controller
 * @summary getPromotionRecord
 * @request POST:/b/echarts/getPromotionRecord
 */
export const echartsGetPromotionRecord = (request: GetEchartsDataRequestVO): Promise<RGetEchartsDataResponse> => {
  return httpRequest({
    url: '/b/echarts/getPromotionRecord',
    method: 'post',
    data: request,
  });
};

/**
 * @tags echarts-data-controller
 * @summary getTab
 * @request POST:/b/echarts/getTab
 */
export const echartsGetTab = (request: GetEchartsDataRequestVO): Promise<RListMapStringObject> => {
  return httpRequest({
    url: '/b/echarts/getTab',
    method: 'post',
    data: request,
  });
};

/**
 * @tags echarts-data-controller
 * @summary getTreeData
 * @request POST:/b/echarts/getTreeData
 */
export const echartsGetTreeData = (request: GetEchartsDataRequestVO): Promise<RListGetDbDataVO> => {
  return httpRequest({
    url: '/b/echarts/getTreeData',
    method: 'post',
    data: request,
  });
};

/**
 * @tags biz-export-controller
 * @summary exportExcel
 * @request POST:/b/export/exportExcel/export
 */
export const exportExportExcelExport = (request: BizExportRequest): Promise<void> => {
  return httpRequest({
    url: '/b/export/exportExcel/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags biz-export-controller
 * @summary productExportExcel
 * @request POST:/b/export/productExportExcel/export
 */
export const exportProductExportExcelExport = (request: BizExportRequest): Promise<void> => {
  return httpRequest({
    url: '/b/export/productExportExcel/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags biz-lottery-controller
 * @summary getLotteryRecord
 * @request POST:/b/lottery/getLotteryRecord
 */
export const lotteryGetLotteryRecord = (request: BizGetLotteryRecordRequest): Promise<RMapStringObject> => {
  return httpRequest({
    url: '/b/lottery/getLotteryRecord',
    method: 'post',
    data: request,
  });
};

/**
 * @tags biz-activity-plan-controller
 * @summary cancelPlan
 * @request POST:/b/plan/cancelPlan
 */
export const planCancelPlan = (request: BizUpdateActivityPlanRequest): Promise<RString> => {
  return httpRequest({
    url: '/b/plan/cancelPlan',
    method: 'post',
    data: request,
  });
};

/**
 * @tags biz-activity-plan-controller
 * @summary createActivityPlan
 * @request POST:/b/plan/createActivityPlan
 */
export const planCreateActivityPlan = (request: BizCreateActivityPlanRequest): Promise<RString> => {
  return httpRequest({
    url: '/b/plan/createActivityPlan',
    method: 'post',
    data: request,
  });
};

/**
 * @tags biz-activity-plan-controller
 * @summary getActivityPlanList
 * @request POST:/b/plan/getActivityPlanList
 */
export const planGetActivityPlanList = (request: BizGetActivityPlanRequest): Promise<RObject> => {
  return httpRequest({
    url: '/b/plan/getActivityPlanList',
    method: 'post',
    data: request,
  });
};

/**
 * @tags biz-activity-plan-controller
 * @summary getLiveRoomConfigList
 * @request POST:/b/plan/getLiveRoomConfigList
 */
export const planGetLiveRoomConfigList = (): Promise<LiveRoomConfigResponse[]> => {
  return httpRequest({
    url: '/b/plan/getLiveRoomConfigList',
    method: 'post',
  });
};

/**
 * @tags biz-activity-plan-controller
 * @summary updateLiveRoomConfig
 * @request POST:/b/plan/updateLiveRoomConfig
 */
export const planUpdateLiveRoomConfig = (request: LiveRoomConfigUpdateRequest): Promise<void> => {
  return httpRequest({
    url: '/b/plan/updateLiveRoomConfig',
    method: 'post',
    data: request,
  });
};

/**
 * @tags biz-activity-plan-controller
 * @summary updateOrderBy
 * @request POST:/b/plan/updateOrderBy
 */
export const planUpdateOrderBy = (request: BizUpdateActivityPlanRequest): Promise<RString> => {
  return httpRequest({
    url: '/b/plan/updateOrderBy',
    method: 'post',
    data: request,
  });
};

/**
 * @tags biz-activity-plan-controller
 * @summary updateStartTime
 * @request POST:/b/plan/updateStartTime
 */
export const planUpdateStartTime = (request: BizUpdateActivityPlanRequest): Promise<RString> => {
  return httpRequest({
    url: '/b/plan/updateStartTime',
    method: 'post',
    data: request,
  });
};

/**
 * @tags biz-template-config-controller
 * @summary getStyleList
 * @request POST:/b/template/getStyleList
 */
export const templateGetStyleList = (request: GetStyleListRequest): Promise<RObject> => {
  return httpRequest({
    url: '/b/template/getStyleList',
    method: 'post',
    data: request,
  });
};

/**
 * @tags biz-template-config-controller
 * @summary getTempList
 * @request POST:/b/template/getTempList
 */
export const templateGetTempList = (request: GetTemplateListRequest): Promise<RObject> => {
  return httpRequest({
    url: '/b/template/getTempList',
    method: 'post',
    data: request,
  });
};
