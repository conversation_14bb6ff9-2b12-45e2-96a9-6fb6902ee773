import {
  CurrentUser,
  IPageUserListResponse,
  PhoneLoginRequest,
  UserAddRequest,
  UserDelRequest,
  UserListRequest,
  UserLoginRequest,
  UserUpdateRequest,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 登录接口
 * @summary getCode
 * @request POST:/user/getCode
 */
export const getCode = (phoneLoginRequest: PhoneLoginRequest): Promise<string> => {
  return httpRequest({
    url: '/user/getCode',
    method: 'post',
    data: phoneLoginRequest,
  });
};

/**
 * @tags 用户接口
 * @summary getUserList
 * @request POST:/user/getUserList
 */
export const getUserList = (userListRequest: UserListRequest): Promise<IPageUserListResponse> => {
  return httpRequest({
    url: '/user/getUserList',
    method: 'post',
    data: userListRequest,
  });
};

/**
 * @tags 登录接口
 * @summary login
 * @request POST:/user/login
 */
export const login = (userLoginRequest: UserLoginRequest): Promise<CurrentUser> => {
  return httpRequest({
    url: '/user/login',
    method: 'post',
    data: userLoginRequest,
  });
};

/**
 * @tags 登录接口
 * @summary phoneLogin
 * @request POST:/user/phoneLogin
 */
export const phoneLogin = (phoneLoginRequest: PhoneLoginRequest): Promise<CurrentUser> => {
  return httpRequest({
    url: '/user/phoneLogin',
    method: 'post',
    data: phoneLoginRequest,
  });
};

/**
 * @tags 用户接口
 * @summary userAdd
 * @request POST:/user/userAdd
 */
export const userAdd = (userAddRequest: UserAddRequest): Promise<void> => {
  return httpRequest({
    url: '/user/userAdd',
    method: 'post',
    data: userAddRequest,
  });
};

/**
 * @tags 用户接口
 * @summary userDel
 * @request POST:/user/userDel
 */
export const userDel = (userDelRequest: UserDelRequest): Promise<void> => {
  return httpRequest({
    url: '/user/userDel',
    method: 'post',
    data: userDelRequest,
  });
};

/**
 * @tags 用户接口
 * @summary userUpdate
 * @request POST:/user/userUpdate
 */
export const userUpdate = (userUpdateRequest: UserUpdateRequest): Promise<void> => {
  return httpRequest({
    url: '/user/userUpdate',
    method: 'post',
    data: userUpdateRequest,
  });
};
