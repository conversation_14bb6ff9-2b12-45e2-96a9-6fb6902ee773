{"name": "@ice/lite-scaffold", "version": "1.3.0", "description": "A new ice.js project.", "dependencies": {"@alifd/next": "^1.27.32", "@ice/runtime": "^1.4.0", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@applint/spec": "^1.2.3", "@ice/app": "^3.4.0", "@ice/plugin-jsx-plus": "^1.0.4", "@ice/plugin-request": "^1.0.3", "@ice/plugin-store": "^1.1.2", "@iceworks/spec": "^1.6.0", "@types/node": "^18.11.17", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "bizcharts": "^4.1.23", "eslint": "^8.35.0", "fs": "^0.0.1-security", "lodash": "^4.17.21", "nanoid": "3.3.4", "path": "^0.12.7", "react-colorful": "^5.6.1", "stylelint": "^15.2.0", "swagger-typescript-api": "^12.0.4", "typescript": "^4.9.5"}, "scripts": {"start": "ice start --speedup", "build": "ice build", "analyze": "ice build --analyzer", "build:test": "ice build --mode test", "build:prod": "ice build --mode production", "eslint": "eslint ./src --cache --ext .js,.jsx,.ts,.tsx", "eslint:fix": "npm run eslint -- --fix", "stylelint": "stylelint \"src/**/*.{css,scss,less}\" --cache", "stylelint:fix": "npm run stylelint -- --fix"}, "publishConfig": {"access": "public"}, "overrides": {"bizcharts": {"react-reconciler": "0.29.0"}}, "repository": "**************:ice-lab/react-materials.git"}