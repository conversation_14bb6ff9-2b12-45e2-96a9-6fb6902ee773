import { Message } from '@alifd/next';
import { history } from 'ice';
import { hideAllDialogs } from './dialogMapper';

// 处理登录过期的情况
export const noAuthHandler = () => {
  hideAllDialogs();
  Message.error('当前登录身份已经过期，请重新登录');
  history?.replace('/login', { from: window.location.pathname });
  cleanStorage();
};


// 清除本地存储和会话存储
export const cleanStorage = () => {
  localStorage.clear();
  sessionStorage.clear();
};


export const logout = () => {
  cleanStorage();
  history?.replace('/login');
};
