export const ACTIVITY_STATUS = {
  NOT_STARTED: 1,
  IN_PROGRESS: 2,
  ENDED: 3,
};

/**
 * 用户条件类型枚举
 */
export const CONDITION_TYPE = {
  // 新客户
  NEW_CUSTOMER: 'newCustomer',
  // 老客户
  OLD_CUSTOMER: 'oldCustomer',
  // 最后一次下单时间
  LAST_ORDER_TIME: 'lastOrderTime',
  // 会员等级
  MEMBER_LEVEL: 'memberLevel',
  // 订单周期
  ORDER_CYCLE: 'orderCycle',
  // 订单状态
  ORDER_STATUS: 'orderStatus',
  // 累计订单金额
  TOTAL_ORDER_AMOUNT: 'totalOrderAmount',
  // 累计订单数
  TOTAL_ORDER_COUNT: 'totalOrderCount',
  // 购买指定商品
  PURCHASE_SPECIFIC_PRODUCT: 'purchaseSpecificProduct',
};

/**
 * 用户条件名称映射
 */
export const CONDITION_NAME_MAP = {
  [CONDITION_TYPE.NEW_CUSTOMER]: '新客户',
  [CONDITION_TYPE.OLD_CUSTOMER]: '老客户',
  [CONDITION_TYPE.LAST_ORDER_TIME]: '最后一次下单时间',
  [CONDITION_TYPE.MEMBER_LEVEL]: '会员等级',
  [CONDITION_TYPE.ORDER_CYCLE]: '订单周期',
  [CONDITION_TYPE.ORDER_STATUS]: '订单状态',
  [CONDITION_TYPE.TOTAL_ORDER_AMOUNT]: '累计订单金额',
  [CONDITION_TYPE.TOTAL_ORDER_COUNT]: '累计订单数',
  [CONDITION_TYPE.PURCHASE_SPECIFIC_PRODUCT]: '购买指定商品',
};

/**
 * 会员等级枚举
 */
export const MEMBER_LEVEL = {
  NORMAL: 1,
  VIP: 2,
  SVIP: 3,
};

/**
 * 会员等级名称映射
 */
export const MEMBER_LEVEL_NAME_MAP = {
  [MEMBER_LEVEL.NORMAL]: '普通会员',
  [MEMBER_LEVEL.VIP]: 'VIP会员',
  [MEMBER_LEVEL.SVIP]: 'SVIP会员',
};

/**
 * 订单周期类型枚举
 */
export const ORDER_CYCLE_TYPE = {
  DAYS: 1,
  DATE_RANGE: 2,
};

/**
 * 订单状态枚举
 */
export const ORDER_STATUS = {
  PAID_NOT_CLOSED: 1,
  RECEIVED: 2,
};

/**
 * 订单状态名称映射
 */
export const ORDER_STATUS_NAME_MAP = {
  [ORDER_STATUS.PAID_NOT_CLOSED]: '已付款未关闭',
  [ORDER_STATUS.RECEIVED]: '已收货',
};

export default {
  LZ_MENU_LIST: 'LZ_MENU_LIST',
  LZ_SSO_TOKEN: 'LZ_SSO_TOKEN',
  LZ_USER_INFO: 'LZ_USER_INFO',
  LZ_SSO_TOKEN_NAME: 'LZ_SSO_TOKEN_NAME',
  LZ_SHOP_LIST: 'LZ_SHOP_LIST',
  LZ_CURRENT_SHOP: 'LZ_CURRENT_SHOP',
  LZ_FUNCTION_LIST: 'LZ_FUNCTION_LIST',
  LZ_MEMBER_LEVEL_LIST: 'LZ_MEMBER_LEVEL_LIST',
  LZ_SUCCESS_CODE: 200,
  LZ_NO_AUTH_CODE: 401,
};
