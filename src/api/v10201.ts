import {
  Activity10201CreateOrUpdateRequest,
  Activity10201CreateOrUpdateResponse,
  Activity10201PrizeInfoRequest,
  Activity10201PrizeInfoResponse,
  Activity10201SeriesImportResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 伊利定制-集罐有礼
 * @summary 创建活动
 * @request POST:/10201/createActivity
 */
export const createActivity = (
  request: Activity10201CreateOrUpdateRequest,
): Promise<Activity10201CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/10201/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利定制-集罐有礼
 * @summary 获取本活动的阶梯奖品
 * @request POST:/10201/getActivityPrize
 */
export const getActivityPrize = (request: Activity10201PrizeInfoRequest): Promise<Activity10201PrizeInfoResponse[]> => {
  return httpRequest({
    url: '/10201/getActivityPrize',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利定制-集罐有礼
 * @summary 导入系列信息excel
 * @request POST:/10201/importSeriesExcel
 */
export const importSeriesExcel = (file: any): Promise<Activity10201SeriesImportResponse[]> => {
  return httpRequest({
    url: '/10201/importSeriesExcel',
    method: 'post',
    data: file,
  });
};

/**
 * @tags 伊利定制-集罐有礼
 * @summary 下载模板
 * @request POST:/10201/seriesTemplate/export
 */
export const seriesTemplateExport = (): Promise<void> => {
  return httpRequest({
    url: '/10201/seriesTemplate/export',
    method: 'post',
  });
};

/**
 * @tags 伊利定制-集罐有礼
 * @summary 修改活动
 * @request POST:/10201/updateActivity
 */
export const updateActivity = (
  request: Activity10201CreateOrUpdateRequest,
): Promise<Activity10201CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/10201/updateActivity',
    method: 'post',
    data: request,
  });
};
