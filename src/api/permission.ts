import {
  CurrentShop,
  MenuListResponse,
  PermissionListByRoleIdRequest,
  PermissionListResponse,
  SelectShopResponse,
  ShopListResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 权限接口
 * @summary getAllShopList
 * @request POST:/permission/getAllShopList
 */
export const getAllShopList = (): Promise<ShopListResponse[]> => {
  return httpRequest({
    url: '/permission/getAllShopList',
    method: 'post',
  });
};

/**
 * @tags 权限接口
 * @summary 查询当前账户所有菜单
 * @request POST:/permission/getMyMenuList
 */
export const getMyMenuList = (): Promise<MenuListResponse[]> => {
  return httpRequest({
    url: '/permission/getMyMenuList',
    method: 'post',
  });
};

/**
 * @tags 权限接口
 * @summary permissionList
 * @request POST:/permission/permissionList
 */
export const permissionList = (): Promise<PermissionListResponse[]> => {
  return httpRequest({
    url: '/permission/permissionList',
    method: 'post',
  });
};

/**
 * @tags 权限接口
 * @summary permissionListByRoleId
 * @request POST:/permission/permissionListByRoleId
 */
export const permissionListByRoleId = (
  permissionListByRoleIdRequest: PermissionListByRoleIdRequest,
): Promise<string[]> => {
  return httpRequest({
    url: '/permission/permissionListByRoleId',
    method: 'post',
    data: permissionListByRoleIdRequest,
  });
};

/**
 * @tags 权限接口
 * @summary permissionMenuList
 * @request POST:/permission/permissionMenuList
 */
export const permissionMenuList = (): Promise<MenuListResponse[]> => {
  return httpRequest({
    url: '/permission/permissionMenuList',
    method: 'post',
  });
};

/**
 * @tags 权限接口
 * @summary selectShop
 * @request POST:/permission/selectShop
 */
export const selectShop = (selectShopResponse: SelectShopResponse): Promise<CurrentShop> => {
  return httpRequest({
    url: '/permission/selectShop',
    method: 'post',
    data: selectShopResponse,
  });
};
