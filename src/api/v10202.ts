import { request as httpRequest } from 'ice';

/**
 * 10202 会员转段赠好礼 - 创建活动
 * @request POST:/10202/createActivity
 */
export const createActivity = (
  request: any,
): Promise<{ activityUrl?: string; activityId?: string }> => {
  return httpRequest({
    url: '/10202/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * 10202 会员转段赠好礼 - 更新活动
 * @request POST:/10202/updateActivity
 */
export const updateActivity = (
  request: any,
): Promise<{ activityUrl?: string; activityId?: string }> => {
  return httpRequest({
    url: '/10202/updateActivity',
    method: 'post',
    data: request,
  });
};

