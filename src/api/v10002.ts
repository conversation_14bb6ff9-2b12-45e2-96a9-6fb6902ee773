import {
  Activity10002AddLotteryNumRequest,
  Activity10002CreateOrUpdateRequest,
  Activity10002CreateOrUpdateResponse,
  Activity10002DrawChanceRecordRequest,
  Activity10002PhysicalPrizeInfoRequest,
  Activity10002PrizeInfoRequest,
  Activity10002PrizeInfoResponse,
  Activity10002PrizeLogRequest,
  Activity10002PrizeLogResponse,
  Activity10002PrizeRecordRequest,
  ActivityDataBasicRequest,
  ActivityDataBasicTotalResponse,
  ActivityDataByDayPage,
  IPageActivity10002DrawChanceRecordResponse,
  IPageActivity10002PhysicalPrizeInfoResponse,
  IPageActivity10002PrizeRecordResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 大转盘抽奖
 * @summary 添加奖品分数
 * @request POST:/10002/addLotteryNum
 */
export const addLotteryNum = (request: Activity10002AddLotteryNumRequest): Promise<boolean> => {
  return httpRequest({
    url: '/10002/addLotteryNum',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 大转盘抽奖
 * @summary 创建大转盘抽奖活动
 * @request POST:/10002/createActivity
 */
export const createActivity = (
  activity10002CreateOrUpdateRequest: Activity10002CreateOrUpdateRequest,
): Promise<Activity10002CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/10002/createActivity',
    method: 'post',
    data: activity10002CreateOrUpdateRequest,
  });
};

/**
 * @tags 大转盘抽奖数据
 * @summary 获取ByDay活动统计数据
 * @request POST:/10002/data/getByDayData
 */
export const dataGetByDayData = (request: ActivityDataBasicRequest): Promise<ActivityDataByDayPage> => {
  return httpRequest({
    url: '/10002/data/getByDayData',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 大转盘抽奖数据
 * @summary 导出ByDay活动统计数据
 * @request POST:/10002/data/getByDayData/export
 */
export const dataGetByDayDataExport = (request: ActivityDataBasicRequest): Promise<void> => {
  return httpRequest({
    url: '/10002/data/getByDayData/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 大转盘抽奖数据
 * @summary 获取抽奖机会记录
 * @request POST:/10002/data/getDrawChanceRecord
 */
export const dataGetDrawChanceRecord = (
  request: Activity10002DrawChanceRecordRequest,
): Promise<IPageActivity10002DrawChanceRecordResponse> => {
  return httpRequest({
    url: '/10002/data/getDrawChanceRecord',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 大转盘抽奖数据
 * @summary 导出抽奖机会记录
 * @request POST:/10002/data/getDrawChanceRecord/export
 */
export const dataGetDrawChanceRecordExport = (request: Activity10002DrawChanceRecordRequest): Promise<void> => {
  return httpRequest({
    url: '/10002/data/getDrawChanceRecord/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 大转盘抽奖数据
 * @summary 获取抽奖记录
 * @request POST:/10002/data/getDrawRecord
 */
export const dataGetDrawRecord = (
  activity10002PrizeRecordRequest: Activity10002PrizeRecordRequest,
): Promise<IPageActivity10002PrizeRecordResponse> => {
  return httpRequest({
    url: '/10002/data/getDrawRecord',
    method: 'post',
    data: activity10002PrizeRecordRequest,
  });
};

/**
 * @tags 大转盘抽奖数据
 * @summary 导出领奖记录
 * @request POST:/10002/data/getDrawRecord/export
 */
export const dataGetDrawRecordExport = (
  activity10002PrizeRecordRequest: Activity10002PrizeRecordRequest,
): Promise<void> => {
  return httpRequest({
    url: '/10002/data/getDrawRecord/export',
    method: 'post',
    data: activity10002PrizeRecordRequest,
  });
};

/**
 * @tags 大转盘抽奖数据
 * @summary 获取实物中奖信息
 * @request POST:/10002/data/getPhysicalPrizeInfo
 */
export const dataGetPhysicalPrizeInfo = (
  activity10002PhysicalPrizeInfoRequest: Activity10002PhysicalPrizeInfoRequest,
): Promise<IPageActivity10002PhysicalPrizeInfoResponse> => {
  return httpRequest({
    url: '/10002/data/getPhysicalPrizeInfo',
    method: 'post',
    data: activity10002PhysicalPrizeInfoRequest,
  });
};

/**
 * @tags 大转盘抽奖数据
 * @summary 导出实物中奖信息
 * @request POST:/10002/data/getPhysicalPrizeInfo/export
 */
export const dataGetPhysicalPrizeInfoExport = (request: Activity10002PhysicalPrizeInfoRequest): Promise<void> => {
  return httpRequest({
    url: '/10002/data/getPhysicalPrizeInfo/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 大转盘抽奖数据
 * @summary 获取奖品数据
 * @request POST:/10002/data/getPrizeInfo
 */
export const dataGetPrizeInfo = (request: Activity10002PrizeInfoRequest): Promise<Activity10002PrizeInfoResponse[]> => {
  return httpRequest({
    url: '/10002/data/getPrizeInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 大转盘抽奖数据
 * @summary 奖品修改记录
 * @request POST:/10002/data/getPrizeLog
 */
export const dataGetPrizeLog = (request: Activity10002PrizeLogRequest): Promise<Activity10002PrizeLogResponse[]> => {
  return httpRequest({
    url: '/10002/data/getPrizeLog',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 大转盘抽奖数据
 * @summary 获取活动统计数据
 * @request POST:/10002/data/getTotalData
 */
export const dataGetTotalData = (request: ActivityDataBasicRequest): Promise<ActivityDataBasicTotalResponse[]> => {
  return httpRequest({
    url: '/10002/data/getTotalData',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 大转盘抽奖
 * @summary 修改大转盘抽奖活动
 * @request POST:/10002/updateActivity
 */
export const updateActivity = (
  activity10002CreateOrUpdateRequest: Activity10002CreateOrUpdateRequest,
): Promise<Activity10002CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/10002/updateActivity',
    method: 'post',
    data: activity10002CreateOrUpdateRequest,
  });
};
