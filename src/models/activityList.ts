import { createModel } from 'ice';

export default createModel({
  state: {
    // 当前tab
    currentTab: 'current',
    // 筛选条件
    activityName: '',
    activityType: 0,
    activityStatus: 0,
    createRange: [],
    // 分页
    pageNo: 1,
    pageSize: 10,
    // 标记是否已经保存过状态
    hasStoredState: false,
  },

  reducers: {
    // 更新状态
    updateState(state, payload) {
      return { ...state, ...payload };
    },
    // 重置状态
    resetState(state) {
      return {
        ...state,
        currentTab: 'current',
        activityName: '',
        activityType: 0,
        activityStatus: 0,
        createRange: [],
        pageNo: 1,
        hasStoredState: false,
      };
    },
  },
});
