import { defineAppConfig } from 'ice';
import { defineRequestConfig, RequestConfig } from '@ice/plugin-request/types';
import { requestConfigInfo } from '@/config/requestConfig';

const consoleError = console.error;
console.error = function (...args) {
  const message = args.join(' ');
  // 因为icejs是用来React18 但是fusion与React18不兼容，所以需要屏蔽掉
  // 留一片净土
  if (typeof message === 'string' && message.startsWith('Warning: ')) {
    return;
  }
  consoleError.apply(console, args);
};


export const requestConfig: RequestConfig = defineRequestConfig(() => ({
  ...requestConfigInfo,
}));
// App config, see https://v3.ice.work/docs/guide/basic/app
export default defineAppConfig(() => ({
}));


