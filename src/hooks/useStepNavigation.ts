import { useState } from 'react';

/**
 * 自定义 Hook 用于管理步骤导航逻辑
 * @param {number} initialStep - 初始步骤索引，默认为 0
 * @param {number} totalSteps - 总步数
 * @returns {Object} { activeStep, goNextStep, goPrevStep, setStep }
 */
interface StepNavigation {
  activeStep: number;
  goNextStep: () => void;
  goPrevStep: () => void;
  setStep: (step: number) => void;
}
export default function useStepNavigation(initialStep: number = 0, totalSteps: number = 1): StepNavigation {
  const [activeStep, setActiveStep] = useState(initialStep);

  // 前进一步
  const goNextStep = () => {
    setActiveStep(prev => (prev < totalSteps - 1 ? prev + 1 : prev));
  };

  // 后退一步
  const goPrevStep = () => {
    setActiveStep(prev => (prev > 0 ? prev - 1 : prev));
  };

  // 跳转到指定步骤（如果在合法范围内）
  const setStep = (step: number) => {
    if (step >= 0 && step < totalSteps) {
      setActiveStep(step);
    }
  };

  return { activeStep, goNextStep, goPrevStep, setStep };
}
