import { useState } from 'react';

export interface PaginationState {
  current: number;
  pageSize: number;
  total: number;
}

export interface PaginationActions {
  changePage: (current: number) => void;
  changePageSize: (size: number) => void;
  setTotal: (total: number) => void;
  reset: (searchParams?: any) => void;
}

export interface UsePaginationResult extends PaginationState, PaginationActions {
  paginationProps: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (current: number) => void;
    onPageSizeChange: (size: number) => void;
  };
}

/**
 * 通用分页Hook
 * @param defaultCurrent 默认当前页，默认为1
 * @param defaultPageSize 默认每页条数，默认为1
 * @param onChange 分页变化时的回调函数
 * @returns 分页状态和操作方法
 */
export default function usePagination(
  defaultCurrent = 1,
  defaultPageSize = 10,
  onChange?: (current: number, size: number, searchParams?: any) => void,
): UsePaginationResult {
  const [pagination, setPagination] = useState<PaginationState>({
    current: defaultCurrent,
    pageSize: defaultPageSize,
    total: 0,
  });

  const changePage = (current: number) => {
    setPagination((prev) => ({ ...prev, current }));
    onChange?.(current, pagination.pageSize);
  };

  const changePageSize = (pageSize: number) => {
    setPagination((prev) => ({ ...prev, pageSize, current: 1 }));
    onChange?.(1, pageSize);
  };

  const setTotal = (total: number) => {
    setPagination((prev) => ({ ...prev, total }));
  };

  const reset = (searchParams?: any) => {
    setPagination({
      current: defaultCurrent,
      pageSize: defaultPageSize,
      total: pagination.total,
    });
    onChange?.(defaultCurrent, defaultPageSize, searchParams);
  };

  const paginationProps = {
    current: pagination.current,
    pageSize: pagination.pageSize,
    total: pagination.total,
    onChange: changePage,
    onPageSizeChange: changePageSize,
  };

  return {
    ...pagination,
    changePage,
    changePageSize,
    setTotal,
    reset,
    paginationProps,
  };
}