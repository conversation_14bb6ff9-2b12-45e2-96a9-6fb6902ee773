import { Message } from '@alifd/next';
import constant from '@/utils/constant';
import { noAuthHandler } from '@/utils/auth';
const controller = new AbortController();

// 创建一个pending的Promise 来阻断流程
const createBlockedPromise = () => {
  const { signal } = controller;
  const promise = new Promise((_, reject) => {
    signal.addEventListener('abort', () => {
      reject(new Error('Request aborted due to auth failure'));
    });
  });
  // 1分钟后自动取消
  setTimeout(() => controller.abort(), 5 * 1000);
  return promise;
};

// 处理无权限的情况
const noAuth = () => {
  controller.abort();
  noAuthHandler();
  // 不需要抛出错误，直接阻断流程
};

// 下载excel文件处理
const download = (data, response) => {
  delete response.data;
  // 下载excel
  return {
    data: {
      file: data,
      suffix: '.xlsx',
    },
    ...response,
  };
};

// 请求拦截器
const onBeforeRequest = (config) => {
  config.headers['content-type'] = 'application/json;';
  const tokenName = localStorage.getItem(constant.LZ_SSO_TOKEN_NAME);
  config.headers[tokenName as string] = localStorage.getItem(constant.LZ_SSO_TOKEN);
  if (config.url.endsWith('export') || config.url.endsWith('download')) {
    config.responseType = 'blob';
  }
  return config;
};

// 响应拦截器
const onAfterResponse = (response) => {
  const { data } = response;

  if (response.headers['content-type'].indexOf('excel') > -1) {
    return download(data, response);
  }

  // 无权限处理
  if (data.code === constant.LZ_NO_AUTH_CODE) {
    noAuth();
    // 返回一个永远 pending 的 Promise 来阻断流程
    return createBlockedPromise();
  }
  // 成功处理
  if (data.code === constant.LZ_SUCCESS_CODE) {
    return data;
  } else {
    return Promise.reject(data);
  }
};

// 错误处理
const onResponseError = (error) => {
  if (error?.response?.data) {
    const err = error.response.data;
    // 无权限处理
    if (err.code === constant.LZ_NO_AUTH_CODE) {
      noAuth();
      return createBlockedPromise();
    } else {
      Message.error(err.message);
      throw err;
    }
  }
  // 其他错误处理
  throw error;
};

export const requestConfigInfo = {
  baseURL: process.env.ICE_BASE_URL,
  interceptors: {
    request: {
      onConfig: onBeforeRequest,
      onError: Promise.reject,
    },
    response: {
      onConfig: onAfterResponse,
      onError: onResponseError,
    },
  },
};
