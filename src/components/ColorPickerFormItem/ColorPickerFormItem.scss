.color-picker-container {
  display: flex;
  align-items: center;

  .color-input-container {
    position: relative;

    .color-input {
      width: 184px;
      height: 32px;
      padding: 0 8px;
      font-size: 14px;
      border: 1px solid #c4c6cf;
      border-radius: 3px;
    }

    .color-block {
      position: absolute;
      top: 50%;
      right: 10px;
      width: 24px;
      height: 24px;
      border-radius: 6px;
      box-shadow: 0 0 5px 1px rgba(0, 0, 0, 10%);
      transform: translateY(-50%);
      cursor: pointer;
    }
  }

  .reset-button {
    margin-left: 10px;
  }
}