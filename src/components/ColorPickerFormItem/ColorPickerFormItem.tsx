import { useRef } from 'react';
import { <PERSON>oon, Button, Form } from '@alifd/next';
import { HexColorInput, HexAlphaColorPicker } from 'react-colorful';
import './ColorPickerFormItem.scss';

export default function ColorPickerFormItem(props) {
  const {
    label,
    color: { value },
    required,
    onSetColor = () => {},
    onReset = () => {},
    disabled = false,
  } = props;

  const resetValue = useRef(value);

  // 设置颜色时调用 onSetColor
  function setColor(newColor) {
    onSetColor(newColor);
  }

  return (
    <Form.Item
      label={label}
      required={required}
    >
      <div className="color-picker-container">
        <div className="color-input-container">
          <HexColorInput
            className="color-input"
            prefixed
            alpha
            color={value}
            onChange={setColor}
            disabled={disabled}
          />
          {disabled ? (
            <div
              className="color-block"
              style={{ background: value }}
              aria-label="点击选择颜色"
            />
          ) : (
            <Balloon
              v2
              trigger={
                <div
                  className="color-block"
                  style={{ background: value }}
                  aria-label="点击选择颜色"
                />
              }
              triggerType="click"
              closable={false}
            >
              <HexAlphaColorPicker
                color={value}
                // alpha
                onChange={setColor}
              />
            </Balloon>
          )}
        </div>
        <Button
          className="reset-button"
          type="primary"
          text
          disabled={disabled}
          onClick={() => onReset(resetValue.current)}
        >
          重置
        </Button>
      </div>
    </Form.Item>
  );
}
