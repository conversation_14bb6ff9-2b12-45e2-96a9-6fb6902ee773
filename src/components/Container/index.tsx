import React from 'react';
import './index.scss';

const Index = props => {
  const { title = '', subtitle = '', className = '', style = {}, children, append } = props;

  return (
    <div
      className={`my-container ${className}`}
      style={style}
    >
      {(title || subtitle) && (
        <div className="my-container-header">
          <div className="my-container-title">
            {title && <span className="my-container-main-title">{title}</span>}
            {subtitle && <span className="my-container-sub-title">{subtitle}</span>}
          </div>
          <div className="my-container-append">{append}</div>
        </div>
      )}
      <div>{children}</div>
    </div>
  );
};
export default Index;
