.my-container {
  flex-shrink: 0;
  margin-bottom: 20px;
  padding: 20px;
  overflow: hidden;
  background-color: #fff;
  border-radius: 6px;

  .my-container-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 20px;

    &:empty {
      padding: 0;
    }
  }

  .my-container-title {
    display: flex;
    align-items: center;
  }

  .my-container-main-title {
    margin-right: 15px;
    font-weight: bold;
    font-size: 16px;
  }

  .my-container-sub-title {
    display: flex;
    align-items: center;
    color: #999;
  }
}
