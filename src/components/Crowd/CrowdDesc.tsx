import { But<PERSON>, Card } from '@alifd/next';

interface CrowdDescProps {
  crowdInfo: {
    crowdName: string;
    crowdId: string;
  };
  onEdit: () => void;
}

export default function CrowdDesc({ crowdInfo, onEdit }: CrowdDescProps) {
  return (
    <Card
      title={crowdInfo.crowdName}
      showTitleBullet={false}
      contentHeight={80}
      style={{ width: 300 }}
      extra={
        <Button
          text
          type="primary"
          style={{ marginBottom: 10 }}
          onClick={onEdit}
        >
          <i style={{ fontSize: 18 }} className="iconfont icon-bianji" />
        </Button>
      }
    >
      <p style={{ lineHeight: 2, fontSize: 14 }}>CDP人群ID：{crowdInfo.crowdId}</p>
    </Card>
  );
}