import { Dialog } from '@alifd/next';
import Crowd from './index';
import { addDialogRef } from '@/utils/dialogMapper';

export default function openCrowdDialog(value?: any) {
  return new Promise(resolve => {
    const dialogRef = Dialog.show({
      v2: true,
      title: '配置CDP人群',
      width: 500,
      centered: true,
      closeMode: ['close'],
      className: 'choose-crowd-condition-dialog',
      content: (
        <Crowd
          value={value}
          onResolve={result => {
            result && resolve(result);
            dialogRef.hide();
          }}
        />
      ),
      footer: false,
    });
    addDialogRef(dialogRef);
  });
}
