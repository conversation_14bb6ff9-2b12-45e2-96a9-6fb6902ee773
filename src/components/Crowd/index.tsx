import { Box, Button, Form, Input, Field } from '@alifd/next';
import { useEffect } from 'react';

interface Props {
  value?: any;
  onResolve: (value: any) => void;
}
const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 16 },
};

export default function Crowd({ value, onResolve }: Props) {
  const field = Field.useField();
  useEffect(() => {
    field.setValues(value);
  }, [value]);

  const onSave = () => {
    field.validate((errors, values) => {
      if (errors) return;
      onResolve(values);
    });
  };

  const onCancle = () => {
    onResolve(null);
  };

  return (
    <Form labelAlign="left" field={field} {...layout}>
      <Form.Item name="crowdName" label="人群名称" required requiredMessage="请输入人群名称" extra={<div className="form-extra">仅做标识显示用，不参与业务逻辑</div>}>
        <Input maxLength={50} showLimitHint placeholder="请输入人群名称" />
      </Form.Item>
      <Form.Item name="crowdId" label="人群Id" required requiredMessage="请输入人群Id" extra={<div className="form-extra">请务必输入CDP中的人群id，否则将造成门槛校验错误</div>}>
        <Input placeholder="请输入人群Id" />
      </Form.Item>
      <Box justify="center" align="center" direction="row" spacing={8}>
        <Button onClick={onCancle}>取消</Button>
        <Button type="primary" style={{ width: 100 }} onClick={onSave}>保存</Button>
      </Box>
    </Form>
  );
}
