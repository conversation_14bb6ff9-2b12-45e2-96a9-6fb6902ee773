import { useEffect, useRef, useMemo } from 'react';
import { NumberPicker } from '@alifd/next';
import { debounce } from 'lodash';

/**
 * NumberInput 组件属性接口
 */
interface NumberInputProps {
  /** 输入框的值 */
  value?: number | string;
  /** 最小值 */
  min?: number;
  /** 最大值 */
  max?: number;
  /** 值变化时的回调函数 */
  onChange?: (value: number | string) => void;
  /** 失焦时的回调函数 */
  onBlur?: (value: string) => void;
  /** onFocus 回调函数 */
  onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;
  /** 防抖延迟时间(毫秒) */
  debounceTime?: number;
  /** 是否在失焦时进行格式化，默认为 true */
  formatOnBlur?: boolean;
  /** 其他 NumberPicker 支持的属性 */
  [key: string]: any;
}

/**
 * NumberInput 组件
 * 增强型数字输入框，提供了更完善的空值处理、防抖和格式化功能
 *
 * 特性：
 * 1. 输入为空时自动使用最小值或 0
 * 2. 支持输入防抖
 * 3. 聚焦时清空值为 0 的输入框，便于重新输入
 * 4. 失焦时自动格式化空值
 */
export default function NumberInput(props: NumberInputProps) {
  const { value, min, max = 9999, onChange, onBlur, debounceTime = 100, formatOnBlur = true } = props;

  // 用 ref 跟踪 min，防止闭包过时
  const minRef = useRef(min);
  useEffect(() => {
    minRef.current = min;
  }, [min]);

  /**
   * 处理值变更的防抖函数
   * 当输入为空时，使用最小值或 0 作为默认值
   */
  const handleChange = useMemo(() => {
    return debounce((val: number | string) => {
      // 如果输入为空，直接赋值 min 或 0
      if (val === '' || val === undefined) {
        onChange?.(minRef.current ?? 0);
      } else {
        onChange?.(val);
      }
    }, debounceTime);
  }, [onChange, debounceTime]);

  return (
    <NumberPicker
      {...props}
      value={value === '' || value === undefined ? (min ?? 0) : value}
      max={max}
      onChange={handleChange}
      format={(val) => {
        /**
         * 输入值格式化处理:
         * 1. 如果输入值非空但不是有效数字，则返回最小值或0
         * 2. 如果输入为空或undefined，返回最小值或0
         * 3. 其他情况返回数值形式的输入值
         */
        if (val !== '' && Number.isNaN(Number(val))) {
          return min ?? 0;
        }
        return val === '' || val === undefined ? (min ?? 0) : Number(val);
      }}
      onBlur={(e) => {
        const val = e.currentTarget.value?.trim?.() ?? '';
        // 兜底：失焦时如果为空，直接赋值 min 或 0
        if (val === '' && formatOnBlur) {
          onChange?.(minRef.current ?? 0);
        }
        onBlur?.(val);
      }}
      onFocus={(e) => {
        const val = +e.currentTarget.value;
        // 聚焦时若值为 0，则清空（仅在 formatOnBlur 为 true 时）
        if (val == 0 && formatOnBlur) {
          onChange?.(minRef.current ?? 0);
        }
      }}
    />
  );
}
