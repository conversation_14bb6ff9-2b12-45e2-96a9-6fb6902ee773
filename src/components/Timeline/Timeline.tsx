import React from 'react';
import './Timeline.scss';

const Timeline = ({ list, line = true }) => {
  return (
    <div className="timeline">
      {list.map((item, index) => (
        <div
          key={index}
          className={`timeline-item ${index === list.length - 1 ? 'last-item' : ''}`}
        >
          <div className="timeline-node" />
          {line && <div className="timeline-line" />}

          <div className="timeline-content">{typeof item === 'string' ? item : React.cloneElement(item)}</div>
        </div>
      ))}
    </div>
  );
};

export default Timeline;
