.timeline {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  .timeline-item {
    position: relative;
    display: flex;

    &:last-child {
      .timeline-line {
        display: none;
      }
    }
  }

  .timeline-node {
    position: relative;
    z-index: 2;
    flex-shrink: 0;
    width: 14px;
    height: 14px;
    margin: 5px 15px 15px 0;
    background-color: #3d7fff;
    border: 3px solid #ebf3ff;
    border-radius: 50%;
  }

  .timeline-line {
    position: absolute;
    top: 16px;
    left: 6px;
    z-index: 1;
    width: 1px;
    height: calc(100%);
    background-color: #ebf3ff;
  }

  .timeline-content {
  }
}
