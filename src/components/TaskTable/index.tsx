import { Box, Button, Dialog, Table, Message } from '@alifd/next';
import { useState, useEffect } from 'react';
import { TaskTypeLabel, openEditTaskDialog } from '../Task/util';
import { renderTaskContent } from './utils';
import dayjs from 'dayjs';

interface Props {
  dataSource: any[];
  disabled: boolean;
  onChange?: (taskList: any[]) => void;
}

export default function TaskTable({ dataSource, disabled, onChange }: Props) {
  const [taskList, setTaskList] = useState<any[]>(dataSource);

  // 监听dataSource变化并更新本地状态
  useEffect(() => {
    setTaskList(dataSource);
  }, [dataSource]);

  // 更新任务列表并通知父组件
  const updateTaskList = (newList: any[]) => {
    setTaskList(newList);
    onChange?.(newList);
  };

  // 任务移动
  const moveTask = (index: number, type: 'up' | 'down') => {
    const swap = ([a, b]) => [b, a];
    const newList = [...taskList];
    const targetIndex = type === 'up' ? index - 1 : index + 1;
    [newList[index], newList[targetIndex]] = swap([newList[index], newList[targetIndex]]);
    updateTaskList(newList);
  };
  const editTask = async (index: number, record: any) => {
    try {
      const newTaskList = await openEditTaskDialog({
        task: record,
        taskList,
        index,
      });
      updateTaskList(newTaskList as any);
      Message.success('任务编辑成功');
    } catch (error) {
      Message.error('任务编辑失败');
    }
  };
  const removeTask = (index: number) => {
    Dialog.confirm({
      title: '确认删除',
      content: '确定要删除这个任务吗？删除后不可恢复。',
      onOk: () => {
        const newList = [...taskList];
        newList.splice(index, 1);
        updateTaskList(newList);
      },
    });
  };


  const renderAction = (value: boolean, index: number, record: any) => {
    return (
      <Box direction="row" align="center" spacing={8}>
        <Button type={'primary'} text onClick={() => editTask(index, record)}>编辑</Button>
        <Button type={'primary'} text onClick={() => removeTask(index)}>删除</Button>
        <Button
          type={'primary'}
          text
          disabled={index === 0}
          onClick={() => moveTask(index, 'up')}
        >
          上移
        </Button>
        <Button
          type={'primary'}
          text
          disabled={index === taskList.length - 1}
          onClick={() => moveTask(index, 'down')}
        >
          下移
        </Button>
      </Box>
    );
  };


  // 任务时间
  const renderTaskTime = (value: boolean, index: number, record: any) => {
    if (record.taskTimeType === 2) {
      return (
        <Box direction="row" align="center" spacing={8}>
          {record.taskTimeRange.map((item: any) => {
            return dayjs(item).format('YYYY-MM-DD HH:mm:ss');
          }).join(' - ')}
        </Box>
      );
    } else {
      return <div>活动期间</div>;
    }
  };
  return (
    <Table dataSource={taskList}>
      <Table.Column title="序号" dataIndex="index" cell={(_, index) => index + 1} />
      <Table.Column title="任务标题" dataIndex="taskType" cell={(value) => TaskTypeLabel[value]} />
      <Table.Column title="任务内容" cell={(value, index, record) => renderTaskContent(record)} />
      <Table.Column title="完成任务次数" dataIndex="limitTimes" cell={(value) => `活动内最多完成${value}次`} />
      <Table.Column title="任务奖励" dataIndex="unitCount" cell={(value, index, record) => `${record.statisticsType === 1 ? record.totalCount : record.unitCount}次抽奖机会`} />
      <Table.Column title="任务时间" cell={renderTaskTime} />
      {
        !disabled && (
          <Table.Column title="操作" cell={renderAction} />
        )
      }
    </Table>
  );
}
