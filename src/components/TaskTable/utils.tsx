import { TaskType } from '../Task/util';


// 商品下单任务内容 累计订单 单笔订单
const renderProductOrder = (record: any) => {
  const { productType, statisticsType, totalAmount, unitAmount, totalOrder } = record;
  const productTypeText = productType === 1
    ? '全店商品'
    : productType === 2 ? '排除部分商品' : '指定商品';

  // 累计订单
  if (statisticsType === 1) {
    return <div>每成功累计{totalOrder}单且单笔金额≥{totalAmount}元（{productTypeText}）</div>;
  }
  // 单笔订单
  if (statisticsType === 2) {
    return <div>成功下单1笔，且单笔金额≥{unitAmount}元（{productTypeText}）</div>;
  }
  return <div>-</div>;
};

// 积分兑换抽奖次数任务内容
const renderPointsExchange = (record: any) => {
  const { points } = record;
  return <div>消耗{points}积分</div>;
};

// 浏览指定页面任务内容
const renderViewPage = (record: any) => {
  const { second } = record;
  return <div>每成功浏览指定页面{second}秒</div>;
};

// 浏览指定商品任务内容
const renderViewProduct = (record: any) => {
  const { products } = record;
  return <div>每成功浏览指定商品{products}次</div>;
};

// 关注店铺任务内容
const renderFocusShop = () => {
  return <div>活动期间内首次关注店铺</div>;
};

// 分享活动任务内容
const renderShareActivity = (record: any) => {
  const { shareCount } = record;
  return <div>用户每成功分享{shareCount}次活动</div>;
};

// 签到活动任务内容
const renderSignActivity = (record: any) => {
  const { signCount } = record;
  return <div>用户每日活动内签到{signCount}次</div>;
};

// 新客首次下单任务内容
const renderFirstBuyBonus = (record: any) => {
  const { productType } = record;
  const productTypeText = productType === 1
  ? '全店商品'
  : productType === 2 ? '排除部分商品' : '指定商品';
  return <div>新客首次下单1笔（{productTypeText}）</div>;
};

// 入会任务内容
const renderJoinMember = () => {
  return <div>用户在活动期间内入会</div>;
};


// 渲染任务内容
export const renderTaskContent = (record: any) => {
  switch (record.taskType) {
    case TaskType.PRODUCT_ORDER:
      return renderProductOrder(record);
    case TaskType.POINTS_EXCHANGE:
      return renderPointsExchange(record);
    case TaskType.VIEW_PAGE:
      return renderViewPage(record);
    case TaskType.VIEW_PRODUCT:
      return renderViewProduct(record);
    case TaskType.FOCUS_SHOP:
      return renderFocusShop();
    case TaskType.SHARE_ACTIVITY:
      return renderShareActivity(record);
    case TaskType.SIGN_ACTIVITY:
      return renderSignActivity(record);
    case TaskType.FIRST_BUY_BONUS:
      return renderFirstBuyBonus(record);
    case TaskType.JOIN_MEMBER:
      return renderJoinMember();
    default:
      return <div>-</div>;
  }
};