import React from 'react';
import { Balloon, Icon } from '@alifd/next';
import './HelpTooltip.scss';


const HelpTooltip = ({
  content,
  iconType = 'help',
  iconSize = 'small',
  iconStyle = {},
  align = 't',
  closable = false,
}: any) => {
  return (
    <Balloon
      v2
      trigger={
        <Icon
          type={iconType}
          size={iconSize}
          style={{ marginLeft: 5, position: 'relative', top: -1, color: 'var(--color-text-regular)', ...iconStyle }}
        />
      }
      closable={closable}
      align={align}
    >
      <p className="help-tooltip">{content}</p>
    </Balloon>
  );
};

export default HelpTooltip;
