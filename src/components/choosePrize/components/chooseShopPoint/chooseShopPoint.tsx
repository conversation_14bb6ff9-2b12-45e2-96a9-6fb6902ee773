import { Dialog } from '@alifd/next';
import ChooseShopPointDialogContent from './ChooseShopPointDialogContent';
import { addDialogRef } from '@/utils/dialogMapper';

export default function chooseShopPoint({ record, field, activityType, status }) {
  return new Promise(resolve => {
    const dialogRef = Dialog.show({
      v2: true,
      title: '编辑店铺会员积分',
      width: 600,
      centered: true,
      closeMode: ['close'],
      content: (
        <ChooseShopPointDialogContent
          record={record}
          onResolve={result => {
            resolve(result); // result为表单数据
            dialogRef.hide();
          }}
          field={field}
          activityType={activityType}
          status={status}
        />
      ),
      footer: false,
    });
    addDialogRef(dialogRef);
  });
}
