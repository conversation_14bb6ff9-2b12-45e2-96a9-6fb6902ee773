import { Dialog } from '@alifd/next';
import ChoosePrizeModalContent from './ChoosePrizeModalContent.jsx';
import { addDialogRef } from '@/utils/dialogMapper';

interface Props {
  defaultActiveTab?: string;
  disabledTabs?: number[];
  activityType?: string;
  status?: [number, string];
}

export default function choosePrize({ defaultActiveTab, disabledTabs, activityType, status }: Props) {
  return new Promise(resolve => {
    const dialogRef = Dialog.show({
      v2: true,
      title: '选择奖品',
      width: 'auto',
      height: '90vh',
      className: 'choose-prize-dialog',
      centered: true,
      closeMode: ['close'],
      content: (
        <ChoosePrizeModalContent
          defaultActiveTab={defaultActiveTab}
          disabledTabs={disabledTabs}
          activityType={activityType}
          status={status}
          onSelectPrize={result => {
            resolve(result);
            dialogRef.hide();
          }}
        />
      ),
      footer: false,
    });
    addDialogRef(dialogRef);
  });
}
