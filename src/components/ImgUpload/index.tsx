import React, { useRef, useState } from 'react';
import { Button, Form, Input, Message, Upload } from '@alifd/next';
import './index.scss';
import MaterialCenter from '@/components/MaterialCenter';
import constant from '@/utils/constant';
import { UploadFile } from '@alifd/next/lib/upload';

// 图片上传组件
export default function ImgUpload(props) {
  const {
    img: { value, width, height, maxSize = 3000, accept = ['PNG', 'JPG', 'JPEG', 'GIF'] },
    name,
    label,
    extra,
    required,
    requiredMessage,
    onSuccess,
    onReset,
    uploadMethod = 'upload', // 可选值：'choose' 或 'upload'
    disabled = false,
  } = props;

  const resetValue = useRef(value);
  const [visible, setVisible] = useState<boolean>(false);


  const handleClose = () => {
    setVisible(false);
  };

  const handleSelect = (material) => {
    onSuccess(material.url);
  };

  // 调用官方选择图片空间接口
  async function chooseImage() {
    setVisible(true);
  }

  const tokenName: string = localStorage.getItem(constant.LZ_SSO_TOKEN_NAME)!;
  const token = localStorage.getItem(constant.LZ_SSO_TOKEN);

  const uploader = new Upload.Uploader({
    action: `${process.env.ICE_BASE_URL}/img/upload`,
    name: 'file',
    // 这个东西必须姚设置，否则会遇到跨域拦截问题
    withCredentials: false,
    data: {},
    headers: {
      [tokenName]: token,
    },
    onSuccess: async (res: any) => {
      onSuccess(res.data.url);
    },
  });

  const checkSize = (file) => {
    return new Promise((resolve): any => {
      if (file.size > maxSize * 1024) {
        Message.error(`图片大小不可以大于${maxSize < 1000 ? `${maxSize}KB` : `${maxSize / 1000}MB`}`);
        resolve(false);
        return false;
      }
      // 读取图片数据
      const reader = new FileReader();
      reader.onload = function (e) {
        const image: any = new Image();
        image.onload = function () {
          // 校验图片尺寸
          const imgWidth = image.width;
          const imgHeight = image.height;

          if (width && imgWidth !== width) {
            Message.error(`图片宽度必须为${width}px`);
            resolve(false);
            return false;
          }
         if (height && imgHeight !== height) {
            Message.error(`图片高度必须为${height}px`);
            resolve(false);
            return false;
          }

          resolve(true);
          return true;
        };
        image.src = e.target!.result;
      };
      reader.readAsDataURL(file);
      return; // 确保Promise回调函数有返回值
    });
  };

  const onHandleImgSelect = async (files: UploadFile[]) => {
    const file = files[0];
    const allow = await checkSize(file);
    if (allow) {
      uploader.startUpload(file);
    }
  };


  return (
    <Form.Item
      name={name}
      label={label}
      extra={extra}
      required={required}
      requiredMessage={requiredMessage}
      className={`${required ? 'required-form-item' : ''}`}
    >
      <Input
        value={value}
        htmlType="hidden"
      />
      <div className="img-upload-form-item">
        {/* 图片展示区域 */}
        <div
          className="choose-img"
        >
          {value ? (
            <img
              src={value}
              alt="上传图片"
            />
          ) : (
            <img
              src="https://img.alicdn.com/imgextra/i4/155168396/O1CN01ypGxZ52BtQ97IJgOA_!!155168396.png"
              alt="默认占位图"
            />
          )}
        </div>
        <div className="img-format">
          <p>
            尺寸：{width ? `宽${width}px` : '不限'} × {height ? `高${height}px` : '不限'}
          </p>
          <p>大小：不超过{maxSize < 1000 ? `${maxSize}KB` : `${maxSize / 1000}MB`}</p>
          <p>格式：{accept.join(', ')}</p>
          <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 10 }}>
            {
              uploadMethod === 'choose' && (<Button
                type="primary"
                className="upload-image-button"
                onClick={chooseImage}
                style={{ marginRight: 20 }}
              >
                选择图片
              </Button>)
            }
            {
              uploadMethod === 'upload' && (
                <Upload.Selecter accept="image/png, image/jpg, image/jpeg, image/gif" onSelect={onHandleImgSelect}>
                  <Button
                    type="primary"
                    disabled={disabled}
                  >
                    上传图片
                  </Button>
                </Upload.Selecter>
              )
            }
            {onReset && (
              <Button
                type="primary"
                text
                disabled={disabled}
                onClick={() => onReset(resetValue.current)}
              >
                重置
              </Button>
            )}
          </div>
        </div>
        <MaterialCenter
          visible={visible}
          onClose={handleClose}
          onSelect={handleSelect}
          maxFileSize={5}
          width={width}
          height={height}
        />
      </div>
    </Form.Item>
  );
}
