import { Button, Checkbox, Input, Message, Pagination, Select, Box, Loading } from '@alifd/next';
import { uniqBy } from 'lodash';
import { useEffect, useState } from 'react';
import { getProductList } from '@/api/sku';

/**
 * ProductSearch 组件用于搜索商品并选择
 *
 * @prop {number} min - 最少需要选择的商品数量
 * @prop {number} max - 最多可以选择的商品数量
 * @prop {function} onSuccess - 选择商品后的回调函数，接收已选择的商品列表作为参数
 * @prop {function} closeDialog - 关闭弹窗的回调函数
 * @prop {Array} selectedItems - 已选择的商品列表
 * @prop {Array} disabledItems - 禁选商品列表
 * @prop {string} itemIdentifier - 用于判断商品对象相等的 key
 * @prop {Boolean} showSaleStatusFilter - 是否显示"已上架、已下架"筛选下拉框
 */
const ProductSearch = ({
  min,
  max,
  onSuccess,
  closeDialog,
  selectedItems,
  disabledItems,
  itemIdentifier,
  showSaleStatusFilter,
}) => {
  // 初始化搜索参数
  const initData = {
    pageNum: 1,
    pageSize: 30,
    productIds: '', // skuId
    name: '', // sku名称
    status: -1, // 商品上下架状态
    order: 'sold_quantity:desc', // 排序依据
  };

  const [searchParams, setSearchParams] = useState(initData);
  const [total, setTotal] = useState(0); // 商品总数
  const [products, setProducts] = useState<any[]>([]); // 从服务器获取的商品列表
  const [selectedProducts, setSelectedProducts] = useState<any[]>([]); // 当前选中的商品列表
  const [selectAllChecked, setSelectAllChecked] = useState(false); // 全选按钮是否选中
  const [loading, setLoading] = useState(false);
  const [inputSelect, setInputSelect] = useState('id');

  useEffect(() => {
    (async () => {
      const productList: any = await fetchData(searchParams);

      // 初始化时勾选已选中的商品
      if (selectedItems && itemIdentifier) {
        let initialSelectedProducts = selectedItems.map(p => ({
          ...p,
        }));
        initialSelectedProducts = uniqBy(initialSelectedProducts, `${itemIdentifier}`);
        setSelectedProducts(initialSelectedProducts);
        setSelectAllChecked(isAllProductsSelected(productList, initialSelectedProducts));
      } else {
        setSelectAllChecked(isAllProductsSelected(productList));
      }
    })();
  }, []);

  // 判断当前页面是否所有可选商品都已被选中
  const isAllProductsSelected = (productList?: any[], selectList?: any) => {
    // 使用传入的productList或组件状态中的products
    const currentProducts = productList || products;

    const currentSelectedProducts = selectList || selectedProducts;
    // 如果没有可选商品，返回false
    if (currentSelectedProducts.length === 0) {
      return false;
    }
    console.log('products', currentProducts);
    console.log('selectList', currentSelectedProducts);
    // 检查当前页所有可选商品是否都已被选中
    return currentProducts.every(product => {
      return isProductSelected(product, currentSelectedProducts);
    });
  };

  // 判断商品是否已被选中
  const isProductSelected = (product: any, selectList?: any) => {
    const currentSelectedProducts = selectList || selectedProducts;
    const result = currentSelectedProducts.filter(selectedProduct =>
      selectedProduct[itemIdentifier] === product[itemIdentifier]);
    return !!result.length;
  };
  // 判断商品是否被禁选
  const isProductDisabled = product => {
    return disabledItems.some(disabledItem => disabledItem[itemIdentifier] == product[itemIdentifier]);
  };
  // 处理输入框值改变事件
  const handleInputChange = value => {
    if (inputSelect === 'id') {
      setSearchParams({ ...searchParams, productIds: value, name: '' });
    } else {
      setSearchParams({ ...searchParams, name: value, productIds: '' });
    }
  };
  // 处理商品上下架状态改变事件
  const handleApproveStatusChange = value => {
    setSearchParams({ ...searchParams, status: value });
  };
  // 处理当前页码改变事件
  const handlePageNoChange = async value => {
    const updatedParams = { ...searchParams, pageNum: value };
    setSearchParams(updatedParams);
    const productList = await fetchData(updatedParams);
    setSelectAllChecked(isAllProductsSelected(productList));
  };
  // 处理每页显示的商品数量改变事件
  const handlePageSizeChange = async value => {
    const updatedParams = { ...searchParams, pageNum: 1, pageSize: value };
    setSearchParams(updatedParams);
    const productList = await fetchData(updatedParams);
    setSelectAllChecked(isAllProductsSelected(productList));
  };
  // 处理查询按钮点击事件
  const handleSearch = () => {
    searchParams.pageNum = 1; // 重置页码为1
    fetchData(searchParams).then((productList) => {
      // 更新查询结果后，更新全选状态，传入获取的产品列表
      setSelectAllChecked(isAllProductsSelected(productList));
    });
  };
  // 处理重置按钮点击事件
  const handleReset = () => {
    setInputSelect('id'); // 重置输入类型为ID
    setSearchParams(initData);
    fetchData(initData).then((productList) => {
      // 更新查询结果后，更新全选状态，传入获取的产品列表
      setSelectAllChecked(isAllProductsSelected(productList));
    });
  };
  // 获取商品列表数据
  const fetchData = params => {
    return new Promise<any[]>(async (resolve) => {
      setLoading(true);
      try {
        const {
          data,
          total,
        } = await getProductList(params);
        const productList = data || [];
        setProducts(productList);
        setTotal(+total! || 0);
        resolve(productList);
      } finally {
        setLoading(false);
      }
    });
  };
  // 处理商品选择事件
  const handleProductSelect = product => {
    // 如果商品被禁选，不做任何操作
    if (isProductDisabled(product)) {
      return;
    }

    let updatedSelectedProducts;

    if (max == 1) {
      // 当 max 为 1 时，只允许选择一个商品
      if (selectedProducts.find(p => p[itemIdentifier] == product[itemIdentifier])) {
        // 如果已经选择了这个商品，取消选择
        updatedSelectedProducts = [];
      } else {
        // 选择当前点击的商品，并取消其他商品的选择
        updatedSelectedProducts = [product];
      }
    } else {
      // 当 max 大于 1 时，正常处理商品选择逻辑
      if (selectedProducts.find(p => p[itemIdentifier] == product[itemIdentifier])) {
        updatedSelectedProducts = selectedProducts.filter(p => p[itemIdentifier] !== product[itemIdentifier]);
      } else {
        updatedSelectedProducts = [...selectedProducts, product];
      }

      if (updatedSelectedProducts.length > max) {
        Message.error(`最多只能选择 ${max} 个商品。`);
        updatedSelectedProducts = updatedSelectedProducts.slice(0, max);
      }
    }

    setSelectedProducts(updatedSelectedProducts);
    console.log(1111, products);
    // 更新选择后直接计算全选状态，无需setTimeout
    setSelectAllChecked(isAllProductsSelected(products, updatedSelectedProducts));
  };

  // 处理全选按钮点击事件
  const handleSelectAll = value => {
    if (value) {
      const selectedProductsExcludingCurrentPage = selectedProducts.filter(selected =>
        !products.some(product => product[itemIdentifier] == selected[itemIdentifier]),
      );

      const remainingQuota = max - selectedProductsExcludingCurrentPage.length;

      // 排除禁用的商品，获取当前页可选商品
      const selectableProducts = products.filter(product => !isProductDisabled(product));

      if (remainingQuota <= 0) {
        // 已经达到或超过最大选择数量
        Message.error(`最多只能选择 ${max} 个商品。`);
        return;
      }

      // 如果当前页可选商品数量超过剩余配额，只选择部分
      if (selectableProducts.length > remainingQuota) {
        const productsToSelect = selectableProducts.slice(0, remainingQuota);
        // 更新选中商品列表，保留之前不在当前页的选中商品
        setSelectedProducts([...selectedProductsExcludingCurrentPage, ...productsToSelect]);
        setSelectAllChecked(false); // 更新全选按钮状态
        Message.error(`已达到最大选择数量 ${max} 个`);
      } else {
        // 选择当前页所有可选商品
        setSelectedProducts([...selectedProductsExcludingCurrentPage, ...selectableProducts]);
        setSelectAllChecked(true); // 更新全选按钮状态
      }
    } else {
      // 取消选择当前页所有商品
      const selectedProductsExcludingCurrentPage = selectedProducts.filter(selected =>
        !products.some(product => product[itemIdentifier] == selected[itemIdentifier]),
      );

      setSelectedProducts([...selectedProductsExcludingCurrentPage]);
      setSelectAllChecked(false); // 更新全选按钮状态
    }
  };
  // 处理确定按钮点击事件
  const handleConfirm = () => {
    if (selectedProducts.length < min) {
      Message.error(`至少需要选择 ${min} 个商品。`);
    } else {
      // 调用 onSuccess 回调，并将选中的商品传递给它
      onSuccess(selectedProducts);
      // 关闭弹窗
      closeDialog();
    }
  };

  const handleInputSelectChange = (value: string) => {
    setInputSelect(value);
  };

  return (
    <Box className="product-content">
      {/* 查询条件 */}
      <Box
        direction="row"
        justify="space-between"
        padding={[0, 0, 10]}
      >
        <Box
          direction="row"
          spacing={10}
        >
          {showSaleStatusFilter && (
            <Select
              value={searchParams.status}
              onChange={handleApproveStatusChange}
            >
              <Select.Option value={-1}>全部</Select.Option>
              <Select.Option value={0}>已上架</Select.Option>
              <Select.Option value={1}>已下架</Select.Option>
            </Select>
          )}
        </Box>
        <Box
          direction="row"
          spacing={10}
        >
          <Input
            style={{ width: 350 }}
            trim
            composition
            placeholder={inputSelect === 'id' ? '请输入商品ID，多个商品用\',\'分隔' : '请输入商品名称'}
            addonTextBefore={(
              <div style={{ display: 'flex', margin: '-1px -8px' }}>
                <Select defaultValue={'id'} value={inputSelect} onChange={(val: string) => handleInputSelectChange(val)}>
                  <option value="id">商品ID</option>
                  <option value="name">商品名称</option>
                </Select>
              </div>

            )}
            value={inputSelect === 'id' ? searchParams.productIds : searchParams.name}
            onChange={handleInputChange}
          />
          <Button onClick={handleSearch}>查询</Button>
          <Button onClick={handleReset}>重置</Button>
        </Box>
      </Box>
      {/* 商品列表 */}
      {max > 1 && (
        <Box
          direction="row"
          padding={[0, 0, 10]}
        >
          {/* <div>{selectAllChecked ? 'true' : 'false'}</div> */}
          <Checkbox
            onChange={handleSelectAll}
            checked={selectAllChecked}
          >
            全选
          </Checkbox>
        </Box>
      )}
      {/* 商品列表 */}
      <Loading visible={loading} style={{ height: '100%', width: '100%', overflowY: 'auto' }} >
        <div className="product-grid-list">
          {products.map((product: any) => (
            <div
              key={product.numIid}
              className={`product-item ${isProductDisabled(product) ? 'disabled' : ''} ${isProductSelected(product) ? 'selected' : ''}`}
            >
              <div className="product-img">
                <img
                  src={product.picUrl}
                  alt={product.title}
                  onClick={() => handleProductSelect(product)}
                />
                <Checkbox
                  checked={isProductSelected(product)}
                  disabled={isProductDisabled(product)}
                  className="checkbox"
                  onChange={() => handleProductSelect(product)}
                />
              </div>
              <span className="product-title">{product.title}</span>
              <div className="product-info">
                <span className="product-price" />
                <div className="product-stock">
                  销量
                  <span className="product-stock-num">{product.sellNum || 0}</span>
                </div>
              </div>
            </div>
          ))}

        </div>
      </Loading>

      <Box
        direction="row"
        justify="space-between"
        align="center"
        padding={[15, 0]}
      >
        <Pagination
          current={searchParams.pageNum}
          pageSize={searchParams.pageSize}
          total={total}
          shape={'arrow-only'}
          totalRender={total => `共 ${total} 条`}
          pageSizeList={[30, 60]}
          onChange={handlePageNoChange}
          onPageSizeChange={handlePageSizeChange}
          pageSizeSelector="dropdown"
          pageSizePosition="end"
        />
        <Box
          direction="row"
          align="center"
          spacing={16}
        >
          <Box
            direction="row"
            align="center"
            spacing={10}
          >
            <span className="product-selected-num">已选择：{selectedProducts.length} / </span>
            <span>{total}</span>
          </Box>
          <Button
            type="primary"
            onClick={handleConfirm}
          >
            确定
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default ProductSearch;
