import React, { useState, useEffect } from 'react';
import { Dialog, Button, Box } from '@alifd/next';
import ProductSearch from './ProductSearch/ProductSearch';
import SelectedProductsDialog from './SelectedProductsDialog/SelectedProductsDialog';
import { getProductList } from '@/api/sku';
import './index.scss';
import { addDialogRef } from '@/utils/dialogMapper';

/**
 * 弹出选择商品对话框，返回选择结果 Promise
 */
function showProductDialog({
  min,
  max,
  selectedItems = [],
  disabledItems = [],
  itemIdentifier = 'numIid',
  showSaleStatusFilter = true,
}: any) {
  return new Promise(resolve => {
    const dialogRef = Dialog.show({
      v2: true,
      title: '选择商品',
      width: 'auto',
      height: '90vh',
      centered: true,
      closeMode: ['close'],
      className: 'products-dialog',
      content: (
        <ProductSearch
          min={min}
          max={max}
          onSuccess={selectedProducts => {
            resolve(selectedProducts);
            dialogRef.hide();
          }}
          closeDialog={() => {
            resolve(null);
            dialogRef.hide();
          }}
          selectedItems={selectedItems}
          disabledItems={disabledItems}
          itemIdentifier={itemIdentifier}
          showSaleStatusFilter={showSaleStatusFilter}
        />
      ),
      footer: false,
    });
    addDialogRef(dialogRef);
  });
}

/**
 * ProductPicker 组件
 */
export default function ProductPicker({
  min,
  max,
  selectedItems = [],
  disabledItems = [],
  itemIdentifier = 'numIid',
  showSaleStatusFilter = true,
  onSelectedProducts,
  disabled = false,
}: any) {
  const [currentSelected, setCurrentSelected] = useState<any>([]);
  const [showSelectedModal, setShowSelectedModal] = useState(false);

  // 当 selectedItems 改变时，如果只有 numIid，则调用接口补全信息
  useEffect(() => {
    async function enrichItems() {
      if (selectedItems.length > 0 &&
        selectedItems.every((item: any) => Object.keys(item).length === 1 && item.numIid)) {
        try {
          const q = selectedItems.map((item: any) => item.numIid).join(',');
          const len = selectedItems.length;
          const res: any = await getProductList({ productIds: q, pageNum: 1, pageSize: len } as any);
          if (Array.isArray(res.data)) {
            setCurrentSelected(res.data);
          } else {
            setCurrentSelected([]);
          }
        } catch (err) {
          console.error('getItemListSimple error', err);
          setCurrentSelected([]);
        }
      } else {
        // 已经有完整信息，直接使用
        setCurrentSelected(selectedItems);
      }
    }
    enrichItems();
  }, [selectedItems]);

  // 打开“选择商品”弹窗
  const handleClick = async () => {
    const result = await showProductDialog({
      min,
      max,
      selectedItems: currentSelected,
      disabledItems,
      itemIdentifier,
      showSaleStatusFilter,
    });
    if (result) {
      setCurrentSelected(result);
      onSelectedProducts?.(result);
    }
  };

  // 打开“已选商品”管理弹窗
  const handleViewSelected = () => {
    setShowSelectedModal(true);
  };

  // “已选商品”弹窗保存
  function handleSelectedModalSave(updatedItems) {
    setCurrentSelected(updatedItems);
    onSelectedProducts?.(updatedItems);
  }

  return (
    <Box
      direction="row"
      align="center"
      spacing={16}
    >
      <Button
        type="primary"
        onClick={handleClick}
        disabled={disabled}
      >
        添加商品 ({currentSelected.length}/{max})
      </Button>
      <Button
        text
        type="primary"
        onClick={handleViewSelected}
      >
        查看已选商品
      </Button>

      {/* 已选商品管理弹窗 */}
      {showSelectedModal && (
        <SelectedProductsDialog
          items={currentSelected}
          onClose={() => setShowSelectedModal(false)}
          onSave={handleSelectedModalSave}
          itemIdentifier={itemIdentifier}
          disabled={disabled}
        />
      )}
    </Box>
  );
}
