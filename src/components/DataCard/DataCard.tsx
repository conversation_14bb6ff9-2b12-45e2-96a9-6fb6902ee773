import React from 'react';
import { Box } from '@alifd/next';
import classNames from 'classnames';
import HelpTooltip from '@/components/HelpTooltip/HelpTooltip';
import './DataCard.scss';

export default function DataCard({ title, tooltip, value, warning = false, connectingLine = false }) {
  return (
    <Box
      padding={10}
      spacing={10}
      className={classNames('data-card', {
        'data-card-warning': warning,
        'data-card-with-line': connectingLine,
      })}
    >
      <Box
        direction="row"
        align="center"
        spacing={10}
      >
        <span>{title}</span>
        <HelpTooltip content={tooltip} />
      </Box>
      <span className="data-value">{value}</span>
    </Box>
  );
}
