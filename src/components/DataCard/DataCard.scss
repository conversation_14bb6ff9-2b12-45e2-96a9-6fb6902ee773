.data-card {
  flex-shrink: 0;
  min-width: 130px;
  background: var(--color-background-widget);
  border-radius: 8px;

  .data-value {
    color: var(--color-text-primary);
    font-weight: bold;
    font-size: 32px;
    line-height: 1;
  }
}

.data-card-warning {
  background-color: #fff6df;
}

$dashed-width: 50px;

.data-card-with-line {
  position: relative;
  margin-right: $dashed-width;
  margin-left: $dashed-width;

  &::before,
  &::after {
    position: absolute;
    top: 50%;
    width: $dashed-width;
    height: 1px;
    background-image: linear-gradient(to right, #ccc 50%, transparent 0);
    background-repeat: repeat-x;
    background-size: 6px 1px; /* 间隔宽度 + 虚线宽度 */
    content: '';
  }

  &::before {
    left: -$dashed-width;
  }

  &::after {
    right: -$dashed-width;
  }
}

.order-num-cell {
  width: 100%;
  height: 100%;
  background: var(--color-background-widget);
  border-radius: 8px;
}
