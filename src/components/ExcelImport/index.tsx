import React from 'react';
import { Button, Upload, Message } from '@alifd/next';
import constant from '@/utils/constant';

interface ExcelImportProps {
  /** 上传URL */
  action: string;
  /** 按钮文字 */
  buttonText?: string;
  /** 按钮类型 */
  buttonType?: 'primary' | 'secondary' | 'normal';
  /** 是否显示下载模板按钮 */
  showTemplateButton?: boolean;
  /** 下载模板按钮文字 */
  templateButtonText?: string;
  /** 下载模板函数 */
  onDownloadTemplate?: () => void;
  /** 自定义请求头 */
  headers?: Record<string, string>;
  /** 上传成功回调 */
  onSuccess?: (res: any) => void;
  /** 上传失败回调 */
  onError?: (error: any) => void;
  /** 上传中状态 */
  loading?: boolean;
}

const ExcelImport: React.FC<ExcelImportProps> = ({
  action,
  buttonText = '导入Excel',
  buttonType = 'primary',
  showTemplateButton = true,
  templateButtonText = '下载Excel导入模板',
  onDownloadTemplate,
  headers,
  onSuccess,
  onError,
  loading = false,
}) => {
  const tokenName: string = localStorage.getItem(constant.LZ_SSO_TOKEN_NAME)!;
  const token = localStorage.getItem(constant.LZ_SSO_TOKEN);
  const defaultHeaders = {
    [tokenName]: token,
  };

  const handleSuccess = (res: any) => {
    Message.success('导入成功');
    onSuccess?.(res);
  };

  const handleError = (error: any) => {
    Message.error('导入失败');
    onError?.(error);
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <Upload
        action={action}
        name="file"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
        autoUpload
        withCredentials
        headers={{ ...defaultHeaders, ...headers }}
        listType="text"
        useDataURL
        limit={1}
        onError={handleError}
        onSuccess={handleSuccess}
      >
        <Button
          type={buttonType}
          loading={loading}
          disabled={loading}
        >
          {buttonText}
        </Button>
      </Upload>

      {showTemplateButton && onDownloadTemplate && (
        <Button
          type="primary"
          text
          loading={loading}
          disabled={loading}
          onClick={onDownloadTemplate}
          style={{ marginLeft: '16px' }}
        >
          {templateButtonText}
        </Button>
      )}
    </div>
  );
};

export default ExcelImport;
