import { Dialog } from '@alifd/next';
import Threshold from './index';
import { addDialogRef } from '@/utils/dialogMapper';
import { CONDITION_NAME_MAP } from '@/utils/constant';

export const conditionNameMap = CONDITION_NAME_MAP;

export default function openThresholdDialog(value?: any) {
  return new Promise(resolve => {
    const dialogRef = Dialog.show({
      v2: true,
      title: '选择人群条件',
      width: 700,
      centered: true,
      closeMode: ['close'],
      className: 'choose-crowd-condition-dialog',
      content: (
        <Threshold
          value={value}
          onResolve={result => {
            result && resolve(result);
            dialogRef.hide();
          }}
        />
      ),
      footer: false,
    });
    addDialogRef(dialogRef);
  });
}
