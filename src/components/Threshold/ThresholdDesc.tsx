import { <PERSON><PERSON>, <PERSON> } from '@alifd/next';
import { formatDateRange } from '@/utils';
import { conditionNameMap } from './util';
import { ORDER_STATUS_NAME_MAP } from '@/utils/constant';
import useMemberLevel from '@/hooks/useMemberLevel';

// 条件值接口定义
interface ConditionValue {
  enabled: boolean;
  value?: any;
  thresholdId: number;
}

interface ThresholdDescProps {
  thresholdInfo: any;
  onEdit: () => void;
  disabled?: boolean;
}

export default function ThresholdDesc({ thresholdInfo, onEdit, disabled = false }: ThresholdDescProps) {
  const memberLevelList = useMemberLevel();
  /**
   * 获取条件名称映射
   */
  const getConditionName = (key: string) => {
    return conditionNameMap[key] || key;
  };

  /**
   * 格式化条件值显示
   */
  const formatConditionValue = (key: string, condition: ConditionValue) => {
    if (!condition.value) return '';

    switch (key) {
      case 'newCustomer':
      case 'oldCustomer':
        return '';
      case 'lastOrderTime':
        if (Array.isArray(condition.value) && condition.value.length === 2) {
          return `：${formatDateRange(condition.value)}`;
        }
        return '';
      case 'memberLevel':
        if (Array.isArray(condition.value)) {
          const levels = condition.value.map(v => memberLevelList.find(item => item.grade === v)?.gradeName || v);
          return `：${levels.join('、')}`;
        }
        return '';
      case 'orderCycle':
        if (condition.value.type === 1) {
          return `：在${condition.value.days}天内累计订单`;
        } else if (condition.value.type === 2 && condition.value.dateRange) {
          return `：${formatDateRange(condition.value.dateRange)}`;
        }
        return '';
      case 'orderStatus':
        return `：${ORDER_STATUS_NAME_MAP[condition.value] || condition.value}`;
      case 'totalOrderAmount':
        return `：${condition.value.min}元 - ${condition.value.max}元`;
      case 'totalOrderCount':
        return `：${condition.value.min}单 - ${condition.value.max}单`;
      case 'purchaseSpecificProduct':
        if (Array.isArray(condition.value) && condition.value.length > 0) {
          return `：${condition.value.length}个商品`;
        }
        return '';
      default:
        return '';
    }
  };

  /**
   * 收集已开启的条件
   */
  const getEnabledConditions = () => {
    if (!thresholdInfo?.conditions) return [];

    return Object.entries(thresholdInfo.conditions)
      .filter(([, condition]: [string, ConditionValue]) => condition.enabled)
      .sort(([, condition1]: [string, ConditionValue], [, condition2]: [string, ConditionValue]) => {
        return condition1.thresholdId - condition2.thresholdId;
      })
      .map(([key, condition]: [string, ConditionValue]) => ({
        key,
        name: getConditionName(key),
        condition,
        displayValue: formatConditionValue(key, condition),
      }));
  };

  const enabledConditions = getEnabledConditions();
  const enabledCount = enabledConditions.length;

  return (
    <Card
      title={thresholdInfo.crowdName}
      showTitleBullet={false}
      contentHeight={130}
      subTitle={`（共${enabledCount}项）`}
      style={{ width: 380 }}
      extra={
        <Button
          text
          type="primary"
          style={{ marginBottom: 10 }}
          disabled={disabled}
          onClick={onEdit}
        >
          <i style={{ fontSize: 18 }} className="iconfont icon-bianji" />
        </Button>
      }
    >
      <p style={{ lineHeight: 2, fontSize: 12 }}>
        {enabledConditions.map((item, index) => (
          <div key={index} style={{ marginBottom: 4 }}>
            <span style={{ fontWeight: 'bold' }}>{item.name}</span>
            <span>{item.displayValue}</span>
          </div>
        ))}
      </p>
    </Card>
  );
}