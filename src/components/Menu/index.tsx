import { useState, useEffect, useCallback } from 'react';
import './index.css';
import { history, useLocation } from 'ice';
import constant from '@/utils/constant';
interface MenuItem {
  id: number;
  title: string;
  icon: string;
  url: string;
  children: MenuItem[];
  isFolder?: boolean; // 是否是父菜单
  selected?: boolean; // 是否选中
  expanded?: boolean; // 是否展开
  hasDivider?: boolean; // 是否在该项前添加分割线
  hasSelectedChild?: boolean; // 是否有选中的子菜单
}

// 底部菜单项配置
const BOTTOM_MENU_ITEMS = ['导出记录', '小程序数据'] as const;
type BottomMenuItem = typeof BOTTOM_MENU_ITEMS[number];

const MenuIcon = ({ icon }: { icon: string }) => {
  return <i className={`iconfont ${icon || 'icon-yemianzhuangxiuicon-21'}`} />;
};

// 模拟API获取的原始菜单数据
const fetchMenuList = (): Promise<Array<MenuItem>> => {
  const menuList = localStorage.getItem(constant.LZ_MENU_LIST);
  return Promise.resolve(JSON.parse(menuList || '[]'));
};

export default function Menu() {
  const [iconOnly, setIconOnly] = useState(false);
  const [activeId, setActiveId] = useState<number | null>(1);
  const [menuData, setMenuData] = useState<MenuItem[]>([]);
  const [expandedMenuIds, setExpandedMenuIds] = useState<Set<number>>(() => {
    // 从 localStorage 获取展开状态
    const savedExpandedId = localStorage.getItem('expandedMenuId');
    return savedExpandedId ? new Set([parseInt(savedExpandedId, 10)]) : new Set();
  });

  const location = useLocation();

  // 加载菜单数据
  useEffect(() => {
    fetchMenuList().then((data) => {
      setMenuData(data);
    });
  }, []);

  // 递归查找匹配当前路径的菜单项及其父菜单项
  const findMenuItemByPath = useCallback(
    (
      items: MenuItem[],
      url: string,
    ): {
      itemId: number | null;
      parentId: number | null;
    } => {
      for (const item of items) {
        if (item.url === url) {
          return { itemId: item.id, parentId: null };
        }
        if (item.children && item.children.length > 0) {
          for (const child of item.children) {
            if (child.url === url) {
              return { itemId: child.id, parentId: item.id };
            }
          }
          // 深度查找子菜单
          const childResult = findMenuItemByPath(item.children, url);
          if (childResult.itemId) {
            return childResult;
          }
        }
      }
      return { itemId: null, parentId: null };
    },
    [],
  );

  // 根据路径更新选中状态和展开状态
  useEffect(() => {
    const currentPath = location.pathname.length > 1 ? location.pathname.replace(/\/$/, '') : location.pathname;
    const { itemId, parentId } = findMenuItemByPath(menuData, currentPath);
    if (itemId) {
      setActiveId(itemId);
      // 如果有父菜单，则展开父菜单
      if (parentId) {
        setExpandedMenuIds(new Set([parentId]));
        // 保存展开状态到 localStorage
        localStorage.setItem('expandedMenuId', parentId.toString());
      }
    }
  }, [location.pathname, findMenuItemByPath, menuData]);

  // 当展开菜单改变时，保存到 localStorage
  useEffect(() => {
    if (expandedMenuIds.size === 1) {
      // 只保存一个展开的菜单ID
      const expandedId = Array.from(expandedMenuIds)[0];
      localStorage.setItem('expandedMenuId', expandedId.toString());
    } else if (expandedMenuIds.size === 0) {
      // 没有展开的菜单时，清除存储
      localStorage.removeItem('expandedMenuId');
    }
  }, [expandedMenuIds]);

  const toggleIconOnly = () => {
    setIconOnly(!iconOnly);
  };

  // 递归更新菜单项的动态属性
  const processMenuItems = (items: MenuItem[], isRootLevel: boolean = true): MenuItem[] => {
    return items.map((item: MenuItem) => {
      // 处理子菜单
      const processedChildren = processMenuItems(item.children, false);
      // 检查是否有选中的子菜单
      const hasSelectedChild = processedChildren.some((child) => child.selected || child.hasSelectedChild);
      return {
        ...item,
        // 动态计算isFolder：根级菜单都是folder
        isFolder: isRootLevel,
        // 动态计算selected：当前活动项
        selected: item.id === activeId,
        // 动态计算expanded：是否在展开列表中
        expanded: expandedMenuIds.has(item.id),
        // 动态计算hasDivider：小程序数据前添加分割线
        hasDivider: item.id === 12,
        // 标记是否有选中的子菜单
        hasSelectedChild,
        // 递归处理子菜单
        children: processedChildren,
      };
    });
  };

  // 应用动态属性
  const processedMenuItems = processMenuItems(menuData, true);
  // 分离底部菜单项和主菜单项
  const [mainMenuItems, bottomMenuItems] = processedMenuItems.reduce<[MenuItem[], MenuItem[]]>(
    (acc, item) => {
      const [main, bottom] = acc;
      if (BOTTOM_MENU_ITEMS.includes(item.title as BottomMenuItem)) {
        return [main, [...bottom, item]];
      }
      return [[...main, item], bottom];
    },
    [[], []],
  );

  const handleMenuItemClick = (item: MenuItem) => {
    setActiveId(item.id);
    if (!item.children || item.children.length === 0) {
      history?.push(item.url);
    }
  };

  // 单个菜单项组件
  const MenuItem = ({
    item,
    isSubmenu = false,
    iconOnly,
  }: {
    item: MenuItem;
    isSubmenu?: boolean;
    iconOnly: boolean;
  }) => {
    // 从菜单项本身获取expanded状态，或者默认为false
    const [expanded, setExpanded] = useState(item.expanded || false);
    const [isHovered, setIsHovered] = useState(false);
    const hasChildren = item.children && item.children.length > 0;

    // 当item.expanded属性变化时更新本地expanded状态
    useEffect(() => {
      if (item.expanded !== undefined) {
        setExpanded(item.expanded);
      }
    }, [item.expanded]);

    const handleClick = () => {
      if (hasChildren) {
        // 对于已展开的菜单项，点击后应该收缩
        if (expanded) {
          // 如果当前已展开，则关闭
          setExpandedMenuIds(new Set());
          localStorage.removeItem('expandedMenuId');
        } else {
          // 如果当前未展开，则只展开当前点击的菜单，关闭其他已展开菜单
          setExpandedMenuIds(new Set([item.id]));
          localStorage.setItem('expandedMenuId', item.id.toString());
        }
        setExpanded(!expanded);
      } else {
        handleMenuItemClick(item);
      }
    };

    const handleMouseEnter = () => {
      setIsHovered(true);
    };

    const handleMouseLeave = () => {
      setIsHovered(false);
    };

    return (
      <div
        className={`menu-item ${isSubmenu ? 'submenu-item' : ''} ${iconOnly ? 'icon-only' : ''} ${
          item.hasDivider ? 'has-divider' : ''
        }`}
      >
        <div
          className={`menu-item-content ${item.selected ? 'selected' : ''} ${
            item.hasSelectedChild ? 'has-selected-child' : ''
          }`}
          onClick={handleClick}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <div className="menu-item-icon">
            <MenuIcon icon={item.icon} />
          </div>
          {!iconOnly && <span className="menu-item-label">{item.title}</span>}
          {hasChildren && !iconOnly && (
            <span className={`menu-arrow ${expanded ? 'expanded' : ''}`}>
              <i className="iconfont icon-a-2right" />
            </span>
          )}
        </div>

        {iconOnly && isHovered && <div className="menu-item-tooltip">{item.title}</div>}

        {hasChildren && expanded && (
          <div className="submenu">
            {item.children.map((child) => (
              <MenuItem key={child.id} item={child} isSubmenu iconOnly={iconOnly} />
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`menu-container ${iconOnly ? 'icon-only-container' : ''}`}>
      <div className="menu-toggle" onClick={toggleIconOnly}>
        {iconOnly ? '≫' : '≪'}
      </div>

      <div className="menu-header">
        <div className="menu-logo">
          <img src="https://novae.oss-cn-zhangjiakou.aliyuncs.com/douyin/images/logo.png" alt="" />
        </div>
        {!iconOnly && (
          <div className="menu-content">
            <div className="menu-title">超级追单</div>
            <div className="menu-subtitle">陆泽科技有限公司</div>
          </div>
        )}
      </div>

      <div className="menu-main-items">
        {mainMenuItems.map((item) => (
          <MenuItem key={item.id} item={item} iconOnly={iconOnly} />
        ))}
      </div>
      {bottomMenuItems.length > 0 && (
        <div className="menu-bottom-items">
          {bottomMenuItems.map((item) => (
            <MenuItem key={item.id} item={item} iconOnly={iconOnly} />
          ))}
        </div>
      )}
    </div>
  );
}
