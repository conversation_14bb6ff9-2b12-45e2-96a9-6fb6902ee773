.menu-container {
  width: 220px;
  display: flex;
  flex-direction: column;
  background-color: var(--primary-black);
  color: #fff;
  position: relative;
  transition: width 0.3s;
  height: 100vh;
  overflow: visible;
}

.icon-only-container {
  width: 85px;
}

.menu-toggle {
  position: absolute;
  right: 8px;
  top: 8px;
  cursor: pointer;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--primary-black-lighter);
  color: #fff;
  z-index: 1;
  transition: all 0.3s;
}

.icon-only-container .menu-toggle {
  right: 0;
}

.menu-toggle:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.menu-header {
  padding: 16px;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.menu-logo {
  width: 45px;
  height: 45px;
  background-color: var(--primary-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  position: relative;
}

.menu-logo img {
  position: absolute;
  width: 30px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.menu-content {
  margin-left: 10px;
}

.menu-title {
  font-size: 18px;
  font-weight: bold;
  color: white;
}

.menu-subtitle {
  font-size: 12px;
  color: rgb(194, 194, 194);
  margin-top: 4px;
}

.menu-item {
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
}

.menu-item-content {
  display: flex;
  align-items: center;
  padding: 16px;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
  color: #adb5bd;
  position: relative;
  margin: 0 15px;
  border-radius: 8px;
}

.menu-item-content.selected {
  background-color: var(--primary-color);
  color: white;
}

.icon-only .menu-item-content {
  justify-content: center;
  padding: 12px 0;
  overflow: visible;
}

.menu-item-content:hover:not(.selected) {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--primary-color);
}

.menu-item-content.selected:hover {
  background-color: var(--primary-color);
}

.menu-item-icon {
  margin-right: 10px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
}

.menu-item-dot {
  width: 6px;
  height: 6px;
  background-color: white;
  border-radius: 50%;
  margin-right: 10px;
}

.icon-only .menu-item-icon,
.icon-only .menu-item-dot {
  margin-right: 0;
}

.menu-item-label {
  flex: 1;
  font-size: 14px;
}

.menu-arrow {
  font-size: 10px;
  transition: transform 0.3s;
  color: #8a8a8a;

  i {
    font-size: 10px;
    transition: transform 0.3s;
    color: #8a8a8a;
  }
}

.menu-arrow.expanded {
  transform: rotate(90deg);
}

.submenu {
  display: flex;
  flex-direction: column;
  background-color: rgba(17, 17, 17, 0.9);
}

.icon-only .submenu {
  padding-left: 0;
}

.submenu-item .menu-item-content {
  padding: 14px 16px 14px 35px;
  margin: 0 15px;
  border-radius: 8px;
}

.submenu-item .menu-item-content.selected {
  background-color: var(--primary-color);
  color: white;
}

.submenu-item .menu-item-content:hover:not(.selected) {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--primary-color);
}

.submenu-item .menu-item-content.selected:hover {
  background-color: var(--primary-color);
}

.icon-only .submenu-item .menu-item-content {
  padding: 10px 0;
}

/* 主菜单和底部菜单样式 */
.menu-main-items {
  flex: 1;
  overflow-y: unset;
}

.menu-bottom-items {
  position: relative;
  padding: 16px 0 8px;
  margin-top: auto;
}

.menu-bottom-items::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background-color: rgb(116, 116, 116);
}

.icon-only-container .menu-bottom-items::before {
  left: 8px;
  right: 8px;
}

/* 移除原来的分割线样式 */
.menu-item.has-divider {
  position: relative;
  margin-top: 0;
}

.menu-item.has-divider::before {
  display: none;
}

.icon-only-container .menu-item.has-divider::before {
  display: none;
}

/* 当子菜单选中时，父菜单的样式 */
.menu-item-content.has-selected-child {
  color: white;
  font-weight: bold;
}

.menu-item-content.has-selected-child .menu-item-icon {
  color: white;
  font-weight: bold;
}

.menu-item-content.has-selected-child .menu-item-label {
  color: white;
  font-weight: bold;
}

.menu-item-content.has-selected-child .menu-arrow {
  i {
    color: white;
  }
}

/* 工具提示样式 */
.menu-item-tooltip {
  position: absolute;
  left: 88px;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 1000;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  pointer-events: none;
}

.menu-item-tooltip::before {
  content: '';
  position: absolute;
  top: 50%;
  left: -5px;
  transform: translateY(-50%);
  border-width: 5px 5px 5px 0;
  border-style: solid;
  border-color: transparent rgba(0, 0, 0, 0.8) transparent transparent;
}
