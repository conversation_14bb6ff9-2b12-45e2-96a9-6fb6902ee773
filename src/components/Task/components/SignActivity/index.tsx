import { Box, Button, Field, Form, Input, Message, Radio } from '@alifd/next';
import HelpTooltip from '@/components/HelpTooltip/HelpTooltip';
import NumberInput from '@/components/NumberInput/NumberInput';
import { useState } from 'react';
import { TaskType } from '@/components/Task/util';

const tipPanel = (
  <div>
    <div style={{ fontWeight: 'bold' }}>任务说明</div>
    <div>1.用户每日进入活动签到后，可获得抽奖机会</div>
    <div>2.每天可完成1次</div>
    <div>3.如已到达完成任务上限后，将无法继续兑换</div>
  </div>
);
const defaultFormData = {
  taskTitle: '活动签到获机会', // 任务标题
  taskDesc: '', // 任务描述
  taskTimeType: 1, // 任务时间类型 1:活动期间 2:指定时间
  signCount: 1, // 签到次数
  unitCount: 1, // 单次完成任务赠送抽奖次数
  limitTimes: 1, // 活动期间内完成上限
};


export default function SignActivity({ onSave, layout, initialData }) {
  const field = Field.useField();
  const [formData, setFormData] = useState({
    ...defaultFormData,
    ...initialData,
    taskType: TaskType.SIGN_ACTIVITY,
  });

  const handleChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value,
    });
  };

  const handleSave = () => {
    field.validate((errors) => {
      console.log(errors);
      if (errors) {
        return;
      }
      onSave(formData);
    });
  };

  return (
    <Form {...layout} field={field}>
      <Message type={'notice'} style={{ marginBottom: 16 }}>
        {tipPanel}
      </Message>
      <Form.Item
        label={'任务时间'}
      >
        <Radio.Group
          value={formData.taskTimeType}
          onChange={value => handleChange('taskTimeType', value)}
        >
          <Radio value={1}>活动期间</Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item label={'任务条件'}>
        <Box direction={'row'} align={'center'} spacing={8}>
          用户每日活动内签到
          <NumberInput
            min={1}
            disabled
            value={formData.signCount}
            onChange={(value: number) => handleChange('signCount', value)}
          />
          次，赠送
          <NumberInput
            min={1}
            value={formData.unitCount}
            onChange={(value: number) => handleChange('unitCount', value)}
          />
          次抽奖机会
        </Box>
      </Form.Item>
      <Form.Item label={'活动时间内完成上限'}>
        <Box direction={'row'} align={'center'} spacing={8}>
          限制
          <NumberInput
            min={1}
            value={formData.limitTimes}
            onChange={value => handleChange('limitTimes', value)}
          />
          次
          <HelpTooltip content={'用户在活动时间内可完成该任务的次数'} />
        </Box>
      </Form.Item>

      <Form.Item label={'任务标题(C端展示)'} name={'taskTitle'} required requiredMessage="请输入任务标题">
        <Input maxLength={16} showLimitHint value={formData.taskTitle} onChange={value => handleChange('taskTitle', value)} />
      </Form.Item>
      <Form.Item label={'任务描述(C端展示)'} name={'taskDesc'} required requiredMessage="请输入任务描述">
        <Input.TextArea maxLength={18} showLimitHint rows={1} value={formData.taskDesc} onChange={value => handleChange('taskDesc', value)} />
      </Form.Item>

      <Box direction={'row'} justify={'center'} spacing={8} style={{ marginTop: 16 }}>
        <Button onClick={() => onSave()}>取消</Button>
        <Button type={'primary'} style={{ width: 100 }} onClick={handleSave}>确定</Button>
      </Box>
    </Form>
  );
}
