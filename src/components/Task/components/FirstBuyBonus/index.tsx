import { Box, Button, Field, Form, Input, Message, Radio } from '@alifd/next';
import HelpTooltip from '@/components/HelpTooltip/HelpTooltip';
import NumberInput from '@/components/NumberInput/NumberInput';
import ProductPicker from '@/components/ProductPicker';
import { useState } from 'react';
import { TaskType } from '@/components/Task/util';

const tipPanel = (
  <div>
    <div style={{ fontWeight: 'bold' }}>任务说明</div>
    <div>1. 用户的首笔订单付款时间在活动时间内则视为完成任务</div>
    <div>2. 若用户下首单未领取机会前发生退单（退款/售后完成）视为【未完成】，可重新完成</div>
    <div>3. 该任务默认只可完成1次</div>
  </div>
);
const defaultFormData = {
  taskTitle: '首购送机会', // 任务标题
  taskDesc: '', // 任务描述
  taskTimeType: 1, // 任务时间类型 1:活动期间
  unitCount: 1, // 单次完成任务赠送抽奖次数
  limitTimes: 1, // 活动期间内完成上限
  orderStatus: 1, // 订单状态 1:已付款 2:已完成
  orderType: 1, // 订单类型 1:现货订单 2:预售订单
  orderStatisticsTime: 1, // 订单统计时间 1:付定金时统计 2:付尾款时统计
  productType: 1, // 商品类型 1:全店商品 2:排除部分商品 3:指定商品
  selectedProducts: [], // 选择的商品
  guideUrl: '', // 任务引导链接
};


export default function FirstBuyBonus({ onSave, layout, initialData }) {
  const field = Field.useField();
  const [formData, setFormData] = useState({
    ...defaultFormData,
    ...initialData,
    taskType: TaskType.FIRST_BUY_BONUS,
  });

  const handleChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value,
    });
  };

  const handleSave = () => {
    field.validate((errors) => {
      console.log(errors);
      if (errors) {
        return;
      }
      onSave(formData);
    });
  };

  return (
    <Form {...layout} field={field}>
      <Message type={'notice'} style={{ marginBottom: 16 }}>
        {tipPanel}
      </Message>
      <Form.Item
        label={'任务时间'}
        required
      >
        <Radio.Group
          value={formData.taskTimeType}
          onChange={value => handleChange('taskTimeType', value)}
        >
          <Radio value={1}>活动期间</Radio>
        </Radio.Group>

      </Form.Item>
      <Form.Item
        label={'任务条件'}
      >
        <Box direction={'row'} align={'center'} spacing={8}>

          新客首次下单1笔，赠送
          <NumberInput
            min={1}
            value={formData.unitCount}
            onChange={(value: number) => handleChange('unitCount', value)}
          />
          次抽奖机会
          <HelpTooltip content={'付款时间在【活动期间】或【指定时间内】且 订单状态满足配置 且 订单中包含指定商品则视为满足条件'} />
        </Box>
      </Form.Item>
      <Form.Item label={'订单状态'} name="orderStatus">
        <Radio.Group
          value={formData.orderStatus}
          onChange={value => handleChange('orderStatus', value)}
        >
          <Radio value={1}>已付款 <HelpTooltip content={'已付款，已发货，已完成状态均可视为符合条件'} /></Radio>
          <Radio value={2}>已完成  <HelpTooltip content={'订单确认收货完成后算作有效订单'} /></Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item
        label={'订单类型'}
        required
        name="orderTypes"
      >
        <Radio.Group
          value={formData.orderType}
          onChange={value => handleChange('orderType', value)}
        >
          <Radio value={1}>现货订单</Radio>
        </Radio.Group>
      </Form.Item>
      {/* <Form.Item label={'订单统计'} name="orderStatisticsTime">
        <Radio.Group
          value={formData.orderStatisticsTime}
          onChange={value => handleChange('orderStatisticsTime', value)}
        >
          <Radio value={1}>付定金时统计 <HelpTooltip content={
            <div>
              <div>(1) 预售订单【付定金时间】在下单时间内便算作满足条件</div>
              <div>(2) 将订单金额（非实付金额）计入阶梯统计</div>
            </div>
          }
          /> </Radio>
          <Radio value={2}>付尾款时统计 <HelpTooltip content={'（1）预售订单【付尾款时间】在下单时间内便算作满足条件'} /></Radio>
        </Radio.Group>
      </Form.Item> */}
      <Form.Item
        label={'订单商品'}
        required
        name="selectedProducts"
        validateState={formData.productType !== 1 && !formData.selectedProducts.length ? 'error' : undefined}
        help={formData.productType !== 1 && !formData.selectedProducts.length ? '请选择订单商品' : undefined}
      >
        <Radio.Group
          value={formData.productType}
          onChange={value => handleChange('productType', value)}
        >
          <Radio value={1}>全店商品</Radio>
          <Radio value={2}>排除部分商品 <HelpTooltip content={'您可以排除例如小样、购物金、补邮费链接或已经参与其他活动的商品，最多排除200件'} /></Radio>
          <Radio value={3}>指定商品</Radio>
        </Radio.Group>
        {/* 自定义组件无法通过field认证 使用Input伪造 */}
        <Input
          x-if={formData.productType !== 1}
          htmlType="hidden"
          name="selectedProducts"
          value={formData.selectedProducts.length > 0 ? '1' : ''}
        />
        <div style={{ marginTop: 16 }} x-if={formData.productType !== 1}>
          <ProductPicker
            min={1}
            max={200}
            selectedItems={formData.selectedProducts}
            onSelectedProducts={(products: any) => {
              handleChange('selectedProducts', products);
              field.setError('selectedProducts', '');
            }}
          />
        </div>
      </Form.Item>
      <Form.Item label={'任务引导按钮跳转'}>
        <Input
          placeholder={'默认跳转店铺首页'}
          disabled
          value={formData.guideUrl}
          onChange={value => handleChange('guideUrl', value)}
        />
      </Form.Item>
      <Form.Item label={'活动时间内完成上限'}>
        <Box direction={'row'} align={'center'} spacing={8}>
          限制
          <NumberInput
            min={1}
            disabled
            value={formData.limitTimes}
            onChange={value => handleChange('limitTimes', value)}
          />
          次
          <HelpTooltip content={'用户在活动时间内可完成该任务的次数'} />
        </Box>
      </Form.Item>

      <Form.Item label={'任务标题(C端展示)'} name={'taskTitle'} required requiredMessage="请输入任务标题">
        <Input maxLength={16} showLimitHint value={formData.taskTitle} onChange={value => handleChange('taskTitle', value)} />
      </Form.Item>
      <Form.Item label={'任务描述(C端展示)'} name={'taskDesc'} required requiredMessage="请输入任务描述">
        <Input.TextArea maxLength={200} showLimitHint rows={3} value={formData.taskDesc} onChange={value => handleChange('taskDesc', value)} />
      </Form.Item>

      <Box direction={'row'} justify={'center'} spacing={8} style={{ marginTop: 16 }}>
        <Button onClick={() => onSave()}>取消</Button>
        <Button type={'primary'} style={{ width: 100 }} onClick={handleSave}>确定</Button>
      </Box>
    </Form>
  );
}
