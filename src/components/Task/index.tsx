import { useEffect, useState } from 'react';
import FocusShop from './components/FocuShop';
import PointsExchange from './components/PointsExchange';
import ProductOrder from './components/ProductOrder';
import ShareActivity from './components/ShareActivity';
import SignActivity from './components/SignActivity';
import ViewPage from './components/ViewPage';
import { Form, Select } from '@alifd/next';
import FirstBuyBonus from './components/FirstBuyBonus';
import JoinMember from './components/JoinMember';
import ViewProduct from './components/ViewProduct';

interface TaskProps {
  onResolve: (value: any) => void;
  apply: number[];
  initialData?: any;
}

interface TaskItem {
  label: string;
  value: number;
  component: React.ComponentType<any>;
}


const taskOptions: TaskItem[] = [
  { label: '商品下单', value: 1, component: ProductOrder },
  { label: '积分兑换抽奖次数', value: 2, component: PointsExchange },
  { label: '浏览指定页面', value: 3, component: ViewPage },
  { label: '浏览指定商品', value: 4, component: ViewProduct },
  { label: '关注店铺', value: 5, component: FocusShop },
  { label: '分享活动', value: 6, component: ShareActivity },
  { label: '活动签到', value: 7, component: SignActivity },
  { label: '首购送机会', value: 8, component: FirstBuyBonus },
  { label: '加入会员', value: 9, component: JoinMember },
];


const layout = {
  labelCol: { fixedSpan: 7 },
  wrapperCol: { span: 18 },
};

export default function Task({ onResolve, apply, initialData }: TaskProps) {
  const [currentTask, setCurrentTask] = useState<number>(initialData?.taskType || apply[0]);
  const [taskList, setTaskList] = useState<TaskItem[]>([]);


  const onSave = (value: any) => {
    console.log('onSave', value);
    onResolve(value);
  };

  const componentProps = {
    layout,
    onSave,
    initialData,
  };

  const renderTaskComponent = () => {
    const TaskComponent = taskList.find(item => item.value === currentTask)?.component;
    if (!TaskComponent) return null;
    return <TaskComponent {...componentProps} />;
  };

  useEffect(() => {
    setTaskList(taskOptions.filter(item => apply.includes(item.value)));
  }, []);


  return (
    <div>
      <Form {...layout}>
        <Form.Item label="任务类型">
          <Select style={{ width: '100%' }} value={currentTask} onChange={(value: number) => setCurrentTask(value)} disabled={!!initialData}>
            {taskList.map(item => (
              <Select.Option key={item.value} value={item.value}>
                {item.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        {renderTaskComponent()}
      </Form>
    </div>
  );
}
