import { Dialog } from '@alifd/next';
import Task from './index';
import { addDialogRef } from '@/utils/dialogMapper';

interface Props {
  apply?: number[];
  taskList?: any[];
}

export function openTaskDialog({ apply = [], taskList }: Props) {
  const selectTypeList = taskList?.map(task => task.taskType);
  const applyResult = apply.filter(item => !selectTypeList?.includes(item));

  return new Promise(resolve => {
    const dialogRef = Dialog.show({
      v2: true,
      title: '添加任务',
      width: 750,
      centered: true,
      closeMode: ['close'],
      content: (
        <Task
          apply={applyResult}
          onResolve={result => {
            result && resolve(result);
            dialogRef.hide();
          }}
        />
      ),
      footer: false,
    });
    addDialogRef(dialogRef);
  });
}

export function openEditTaskDialog({ task, taskList, index }: { task: any; taskList: any[]; index: number }) {
  return new Promise(resolve => {
    const dialogRef = Dialog.show({
      v2: true,
      title: '编辑任务',
      width: 750,
      centered: true,
      closeMode: ['close'],
      content: (
        <Task
          apply={[task.taskType]}
          initialData={task}
          onResolve={result => {
            if (result) {
              const newTaskList = [...taskList];
              newTaskList[index] = { ...newTaskList[index], ...result };
              resolve(newTaskList);
            }
            dialogRef.hide();
          }}
        />
      ),
      footer: false,
    });
    addDialogRef(dialogRef);
  });
}

export const TaskTypeLabel = {
  1: '商品下单',
  2: '积分兑换抽奖次数',
  3: '浏览指定页面',
  4: '浏览指定商品',
  5: '关注店铺',
  6: '分享活动',
  7: '活动签到',
  8: '首购送机会',
  9: '加入会员',
};
export const TaskType = {
  PRODUCT_ORDER: 1,
  POINTS_EXCHANGE: 2,
  VIEW_PAGE: 3,
  VIEW_PRODUCT: 4,
  FOCUS_SHOP: 5,
  SHARE_ACTIVITY: 6,
  SIGN_ACTIVITY: 7,
  FIRST_BUY_BONUS: 8,
  JOIN_MEMBER: 9,
};