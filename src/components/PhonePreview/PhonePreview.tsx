import './PhonePreview.scss';

export default function PhonePreview({ children, width = 375, scale = 1 }) {
  // 根据设计，计算基础宽度、高度和圆角（scale = 1时的尺寸）
  const contentScale = 0.854;
  const baseWidth = width / contentScale; // 原始容器宽度
  const baseHeight = (1252 / 640) * baseWidth; // 保持宽高比例
  const borderRadius = (baseWidth / 375) * 20; // 原始圆角

  // 外层容器尺寸乘以 scale，内层内容使用 transform 缩放
  return (
    <div
      className="phone-preview"
      style={{
        width: `${baseWidth * scale}px`,
        height: `${baseHeight * scale}px`,
      }}
    >
      <div
        className="phone-preview-content no-scrollbar"
        style={{
          width: `${baseWidth * contentScale}px`,
          height: `${baseHeight * 0.938}px`,
          borderRadius: `${borderRadius}px`,
          transform: `scale(${scale})`,
          transformOrigin: 'top left',
        }}
      >
        {children}
      </div>
    </div>
  );
}
