import React, { useEffect, useState } from 'react';
import { useLocation, history } from 'ice';
import styles from './index.module.scss';
import constant from '@/utils/constant';
import { Balloon, Button, Dialog, Icon, Message } from '@alifd/next';
import { logout } from '@/utils/auth';


// 菜单定义
interface MenuItem {
  id: number;
  title: string;
  icon: string;
  url: string;
  children: MenuItem[];
  isFolder?: boolean;
  selected?: boolean;
  expanded?: boolean;
  hasDivider?: boolean;
  back?: boolean;
  parentId?: number;
}


// 查找当前路径对应的菜单项及其父菜单
const findMenuPathByUrl = (
  items: MenuItem[],
  path: string,
  parentPath: MenuItem[] = [],
): MenuItem[] | null => {
  for (const item of items) {
    const currentPath = [...parentPath, item];
    if (item.url === path) {
      return currentPath;
    }

    if (item.children && item.children.length > 0) {
      const result = findMenuPathByUrl(item.children, path, currentPath);
      if (result) return result;
    }
  }

  return null;
};


// 创建面包屑组件
const Breadcrumb = ({ menuList, currentPath }) => {
// 菜单数据，与Menu组件中的保持一致
  const menuPath = findMenuPathByUrl(menuList, currentPath) || [];


  return (
    <div className={styles.breadcrumb}>
      {menuPath.map((item, index) => (
        <React.Fragment key={item.id}>
          <span className={styles.breadcrumbItem}>{item.title}</span>
          {index < menuPath.length - 1 && (
            <span className={styles.breadcrumbSeparator}>&gt;</span>
          )}
        </React.Fragment>
      ))}
    </div>
  );
};

const getRemainingDays = (expireTime?: number) => {
  if (!expireTime) return 0;
  const now = new Date().getTime();
  const diff = expireTime - now;
  return Math.max(0, Math.floor(diff / (1000 * 60 * 60 * 24)));
};

/**
 * 根据 extraMenuList 的 parentId，将其插入到 menuList 对应 id 的 children 中
 * @param extraMenuList 额外菜单列表
 * @param menuList 主菜单列表
 * @returns 合并后的菜单列表
 */
function mergeMenuLists(extraMenuList: MenuItem[], menuList: MenuItem[]): MenuItem[] {
  // 深拷贝 menuList，避免直接修改原数据
  const result: MenuItem[] = JSON.parse(JSON.stringify(menuList));

  // 递归查找并插入
  function insertIntoMenu(menuItems: MenuItem[], extraItem: MenuItem): boolean {
    for (const item of menuItems) {
      if (item.id === extraItem.parentId) {
        if (!item.children) item.children = [];
        item.children.push({ ...extraItem });
        return true;
      }
      if (item.children && item.children.length > 0) {
        if (insertIntoMenu(item.children, extraItem)) {
          return true;
        }
      }
    }
    return false;
  }

  for (const extraItem of extraMenuList) {
    insertIntoMenu(result, extraItem);
  }

  return result;
}

export default function Header() {
  const location = useLocation();
  const currentPath = location.pathname.length > 1 ? location.pathname.replace(/\/$/, '') : location.pathname;
  const [menuList, setMenuList] = useState<MenuItem[]>([]);
  const menuPath = findMenuPathByUrl(menuList, currentPath) || [];
  const [currentShop, setCurrentShop] = useState<any>({});
  const [userInfo, setUserInfo] = useState<any>({});

  // 检查当前菜单项是否有back=true属性
  const currentMenuItem = menuPath.length > 0 ? menuPath[menuPath.length - 1] : null;
  const showBackButton = currentMenuItem?.back === true;

  const extraMenuList: any = [
    {
      checkboxDisabled: false,
      children: [],
      icon: 'icon-yemianzhuangxiuicon-21',
      id: 3.1,
      keyName: 'activity_custom_select',
      parentId: 2,
      sort: 3,
      title: '自定义活动',
      url: '/activity/custom/create',
    },
    {
      checkboxDisabled: false,
      children: [],
      icon: 'icon-yemianzhuangxiuicon-21',
      id: 31.1,
      keyName: 'activity_custom_select',
      parentId: 31,
      sort: 1,
      title: '活动数据',
      url: '/activity/data',
      back: true,
    },
    {
      checkboxDisabled: false,
      children: [],
      icon: 'icon-yemianzhuangxiuicon-21',
      id: 31.2,
      keyName: 'activity_custom_select',
      parentId: 31,
      sort: 1,
      title: '推广数据',
      url: '/activity/promotion/data',
      back: true,
    },
    {
      checkboxDisabled: false,
      children: [],
      icon: 'icon-yemianzhuangxiuicon-21',
      id: 4.1,
      keyName: 'activity_custom_select',
      parentId: 2,
      sort: 4,
      title: '抽奖活动',
      url: '/activity/10002/create',
    },
    {
      checkboxDisabled: false,
      children: [],
      icon: 'icon-yemianzhuangxiuicon-21',
      id: 31.2,
      keyName: 'activity_custom_select',
      parentId: 31,
      sort: 2,
      title: '活动数据',
      url: '/activity/10002/data',
      back: true,
    },
  ];

  const shopList = JSON.parse(localStorage.getItem(constant.LZ_SHOP_LIST) || '[]');

  useEffect(() => {
    const _menuList = JSON.parse(localStorage.getItem(constant.LZ_MENU_LIST) || '[]');
    setMenuList(mergeMenuLists(extraMenuList, _menuList));
    setCurrentShop(JSON.parse(localStorage.getItem(constant.LZ_CURRENT_SHOP) || '{}'));
    setUserInfo(JSON.parse(localStorage.getItem(constant.LZ_USER_INFO) || '{}'));
  }, []);

  const handleLogout = () => {
    Dialog.confirm({
      title: '提示',
      content: '确认退出登录当前账号吗？',
      onOk: async () => {
        logout();
        Message.success('已退出登录');
      },
    });
  };

  // 获取当前页面标题，默认为"活动"
  const pageTitle = menuPath.length > 0
    ? menuPath[menuPath.length - 1].title
    : '活动';

  const back = () => {
    window.history?.back();
  };

  const handleSelectShop = () => {
    history?.push('/shop');
  };

  return (
    <div className={styles.header}>
      <div className={styles.bread}>
        {showBackButton && (
          <Button
            onClick={back}
            className={styles.backBtn}
            type={'secondary'}
          > <Icon type="arrow-left" style={{ marginRight: 5 }} /> 返回</Button>
        )}
        <div className={styles.leftContent}>
          <div className={styles.title}>{pageTitle}</div>
          {menuPath.length > 0 && <Breadcrumb currentPath={currentPath} menuList={menuList} />}
        </div>
      </div>
      <div className={styles.rightContent}>
        {/* <div className={styles.serviceItem}> */}
        {/*   <div className={styles.icon} > */}
        {/*     <i className={'iconfont icon-customer-service-fill'} /> */}
        {/*   </div> */}
        {/*   <span>在线客服</span> */}
        {/* </div> */}
        <div
          className={styles.serviceItem}
          onClick={() => {
          window.open('https://gyj4qdmjsv.feishu.cn/wiki/space/7501902566843006979?ccm_open_type=lark_wiki_spaceLink&open_tab_from=wiki_home', '_blank');
        }}
        >
          <div className={styles.icon} >
            <i className={'iconfont icon-chaojiyingxiaoicon-10'} />
          </div>
          <span>帮助教程</span>
        </div>
        <Balloon
          v2
          triggerType="click"
          closable={false}
          popupClassName={styles.shopProfileProp}
          animation={{ in: 'expandInDown', out: 'expandOutDown' }}
          trigger={
            <div className={styles.storeName}>
              <div className={styles.icon}>
                <img src={currentShop.logo} alt="" />
              </div>
              <span>{currentShop.shopName}：{userInfo.account}</span>
            </div>
          }
        >
          {
          shopList.length > 1 && (
            <div className={styles.cell}>
              <div className={[styles.value, styles.logout].join(' ')} onClick={handleSelectShop}>
                <i className="iconfont icon-shop" />
                <span>选择店铺</span>
              </div>
            </div>
          )
         }
          <div className={styles.cell}>
            <div className={[styles.value, styles.logout].join(' ')} onClick={handleLogout}>
              <i className="iconfont icon-a-29logout" />
              <span>退出登录</span>
            </div>
          </div>
        </Balloon>
        <div className={styles.subscriptionInfo}>
          <span className={styles.remainDays}>
            剩余
            <span className={styles.daysNumber}>{getRemainingDays(currentShop.orderExpireTime)}</span>
            天</span>
          {/* <button className={styles.renewButton}>立即续费</button> */}
        </div>
      </div>
    </div>
  );
}
