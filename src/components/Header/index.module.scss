.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 60px;
  border-bottom: 1px solid #e8e8e8;
  background-color: #fff;
}

.bread {
  display: flex;
  justify-content: center;
  align-items: center;
}

.backBtn {
  margin-right: 20px;
}

.leftContent {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  padding: 0;
  color: #333;
}

.breadcrumb {
  display: flex;
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.breadcrumbItem {
  color: #999;
}

.breadcrumbSeparator {
  margin: 0 4px;
}

.rightContent {
  display: flex;
  align-items: center;
}

.serviceItem {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
  margin-left: 20px;
  cursor: pointer;
  gap: 10px;
}

.icon {
  img {
    height: 30px;
    margin-right: 5px;
    border-radius: 2px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

}

.storeName {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
  margin-left: 20px;
  position: relative;
  padding: 0 20px;
  cursor: pointer;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    height: 16px;
    width: 1px;
    background-color: #e8e8e8;
  }

  &::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    height: 16px;
    width: 1px;
    background-color: #e8e8e8;
  }
}

.subscriptionInfo {
  display: flex;
  align-items: center;
  margin-left: 20px;
  padding: 4px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.remainDays {
  font-size: 14px;
  color: #666;
}

.daysNumber {
  font-size: 16px;
  font-weight: bold;
  color: var(--primary-color);
}

.renewButton {
  margin-left: 8px;
  padding: 2px 8px;
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 14px;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

.cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px;
  font-size: 14px;
  font-weight: 400;
  height: 32px;

  &:hover {
    color: var(--primary-color);
    background-color: #f4f6f9;
  }

  .label {
    flex: 1;
    color: #8d9299;
  }

  .value {
    flex: 1;
    color: var(--primary-color);
    display: flex;
    gap: 5px;
    align-items: center;
  }

  .user {
    cursor: pointer;
  }

  .logout {
    cursor: pointer;
    color: var(--primary-color);
  }
}