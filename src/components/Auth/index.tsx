import constant from '@/utils/constant';
import { ReactNode, useEffect, useState } from 'react';
import { Message } from '@alifd/next';

/**
 * 权限控制组件
 * 用于包装需要权限验证的子组件，当用户没有对应权限时阻止操作并显示提示
 */

// 定义功能项的类型接口
interface FunctionItem {
  key: string;
  [key: string]: any;
}

// 定义组件 Props 接口
interface AuthProps {
  /** 子组件内容 */
  children: ReactNode;
  /** 权限验证的 key 值 */
  authKey: string;
}

export function Auth({ children, authKey }: AuthProps) {
  // 存储用户权限列表
  const [authList, setAuthList] = useState<string[]>([]);

  /**
   * 从本地存储获取用户权限列表
   * 解析存储的功能列表并提取权限 key
   */
  const fetchAuthList = () => {
    // 从 localStorage 获取功能列表
    const functionList: FunctionItem[] = JSON.parse(
      localStorage.getItem(constant.LZ_FUNCTION_LIST) || '[]',
    );
    // 提取所有功能的 key 值作为权限列表
    const functionKeys: string[] = functionList.map((item: FunctionItem) => item.key);
    setAuthList(functionKeys);
  };

  // 组件挂载时获取权限列表
  useEffect(() => {
    fetchAuthList();
  }, []);

  // 检查当前用户是否有对应权限
  const hasAuth = authList.includes(authKey);

  /**
   * 处理点击事件捕获
   * 当用户没有权限时，阻止事件传播并显示警告信息
   * @param event - React 鼠标事件对象
   */
  const handleClickCapture = (event: React.MouseEvent) => {
    if (!hasAuth) {
      // 显示权限不足的警告信息
      Message.warning('您暂无当前页面导出权限，请联系管理员');
      // 阻止事件冒泡和默认行为
      event.stopPropagation();
      event.preventDefault();
    }
  };

  // 使用事件捕获机制包装子组件，在没有权限时拦截所有点击操作
  return <div onClickCapture={handleClickCapture}>{children}</div>;
}
