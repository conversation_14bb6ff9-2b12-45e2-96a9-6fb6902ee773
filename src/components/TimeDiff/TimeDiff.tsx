import dayjs from 'dayjs';
import './TimeDiff.scss';

const TimeDiff = ({ startTime, endTime }) => {
  const startTimeFormat = dayjs(startTime).format('YYYY-MM-DD\nHH:mm:ss');
  const endTimeFormat = dayjs(endTime).format('YYYY-MM-DD\nHH:mm:ss');
  let diffTimeFormat;
  // 活动持续时间(当前时间在结束时间之后)
  diffTimeFormat = dayjs(endTime).diff(dayjs(startTime), 'day');

  if (diffTimeFormat < 0) diffTimeFormat = 0;

  return (
    <div className="time-range-box">
      <div className="time-bgd">{startTimeFormat}</div>
      <div className="time-bgd-arrow">{diffTimeFormat}天</div>
      <div className="time-bgd">{endTimeFormat}</div>
    </div>
  );
};
export default TimeDiff;
