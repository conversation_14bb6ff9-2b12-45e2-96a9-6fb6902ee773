import React from 'react';
import { Button, Pagination } from '@alifd/next';
import styles from '../index.module.scss';

interface FooterProps {
  currentPage: number;
  totalCount: number;
  pageSize: number;
  meterialUrl: string;
  onPageChange: (page: number) => void;
  onConfirm: () => void;
  onClose: () => void;
}

const Footer: React.FC<FooterProps> = ({
  currentPage,
  totalCount,
  pageSize,
  meterialUrl,
  onPageChange,
  onConfirm,
  onClose,
}) => {
  return (
    <>
      <div className={styles.footer}>
        <Pagination
          current={currentPage}
          total={totalCount}
          pageSize={pageSize}
          onChange={onPageChange}
          hideOnlyOnePage
          totalRender={() => `共${totalCount}条`}
        />
      </div>
      <div className={styles.footerContainer}>
        <div className={styles.centerLink}>
          可前往素材中心进行图片/视频的上传，<a href={meterialUrl} target={'_blank'}>进入素材中心</a>
        </div>
        <div className={styles.footerBtn}>
          <Button type="primary" onClick={onConfirm}>确定</Button>
          <Button onClick={onClose}>取消</Button>
        </div>
      </div>
    </>
  );
};


export default Footer;
