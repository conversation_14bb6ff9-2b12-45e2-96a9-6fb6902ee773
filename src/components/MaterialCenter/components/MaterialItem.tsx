import React from 'react';
import { Icon } from '@alifd/next';
import styles from '../index.module.scss';

export interface MaterialItemData {
  materialId: string;
  materialName: string;
  url: string;
}

interface MaterialItemProps {
  item: MaterialItemData;
  isSelected: boolean;
  isValid: boolean;
  sizesLoaded: boolean;
  onSelect: (materialId: string) => void;
}

const MaterialItem: React.FC<MaterialItemProps> = ({
  item,
  isSelected,
  isValid,
  sizesLoaded,
  onSelect,
}) => {
  return (
    <div
      key={item.materialId}
      className={`${styles.materialItem} ${isSelected ? styles.selected : ''}`}
      onClick={() => onSelect(item.materialId)}
    >
      <div className={styles.imageWrapper}>
        <img
          src={item.url || ''}
          alt={item.materialName}
          className={styles.materialImage}
        />
        {isSelected && isValid && (
          <div className={styles.selectedMark}>
            <Icon type="select" size="xs" />
          </div>
        )}
        {sizesLoaded && !isValid && (
          <div className={styles.invalidMask}>
            <Icon type="warning" size="medium" />
            <div className={styles.invalidText}>图片尺寸不符</div>
          </div>
        )}
        <div className={styles.imageSize}>
          {item.url ? `${item.url.match(/www(\d+)-(\d+)/)?.[1]}×${item.url.match(/www(\d+)-(\d+)/)?.[2]}` : ''}
        </div>
      </div>
      <div className={styles.imageName}>
        {item.materialName}
      </div>
    </div>
  );
};

export default MaterialItem;
