import React from 'react';
import styles from '../index.module.scss';
import MaterialItem, { MaterialItemData } from './MaterialItem';

interface MaterialGridProps {
  materials: MaterialItemData[];
  loading: boolean;
  selectedMaterial: string;
  imageSizes: Record<string, {width: number; height: number; valid: boolean}>;
  onSelectMaterial: (materialId: string) => void;
}

const MaterialGrid: React.FC<MaterialGridProps> = ({
  materials,
  loading,
  selectedMaterial,
  imageSizes,
  onSelectMaterial,
}) => {
  if (loading) {
    return <div className={styles.loading}>加载中...</div>;
  }

  if (materials.length === 0) {
    return <div className={styles.empty}>暂无图片</div>;
  }

  return (
    <div style={{ height: 480 }}>
      <div className={styles.materialGrid}>
        {materials.map(item => {
          const isValid = imageSizes[item.materialId]?.valid;
          const sizesLoaded = item.materialId in imageSizes;
          return (
            <MaterialItem
              key={item.materialId}
              item={item}
              isSelected={selectedMaterial === item.materialId}
              isValid={isValid}
              sizesLoaded={sizesLoaded}
              onSelect={onSelectMaterial}
            />
          );
        })}
      </div>
    </div>
  );
};

export default MaterialGrid;
