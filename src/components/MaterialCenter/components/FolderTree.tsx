import React from 'react';
import { Tree, Icon } from '@alifd/next';
import styles from '../index.module.scss';

export interface FolderItem {
  key: string;
  label: string;
  icon?: string;
  selectable?: boolean;
  children?: FolderItem[];
}

interface FolderTreeProps {
  treeData: FolderItem[];
  expandedKeys: string[];
  selectedKeys: string[];
  autoExpandParent: boolean;
  onSelect: (keys: string[], info: any) => void;
  onExpand: (keys: string[]) => void;
}

const FolderTree: React.FC<FolderTreeProps> = ({
  treeData,
  expandedKeys,
  selectedKeys,
  autoExpandParent,
  onSelect,
  onExpand,
}) => {
  return (
    <div className={styles.leftPanel}>
      <div className={styles.folderContainer}>
        <div className={styles.folderTitle}>
          <Icon type="folder" size="small" /> 我的图片
        </div>
        <div className={styles.treeContainer}>
          <Tree
            defaultExpandAll
            dataSource={treeData}
            isNodeBlock
            onSelect={onSelect}
            expandedKeys={expandedKeys}
            selectedKeys={selectedKeys}
            onExpand={onExpand}
            autoExpandParent={autoExpandParent}
          />
        </div>
      </div>
      <div className={styles.usageInfo}>
        {/* 17.6MB / 20GB <a href="#" className={styles.detailLink}>详情扩容</a> */}
      </div>
    </div>
  );
};

export default FolderTree;
