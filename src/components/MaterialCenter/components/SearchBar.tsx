import React from 'react';
import { Input, Button, Icon, Upload } from '@alifd/next';
import { UploadFile } from '@alifd/next/lib/upload';
import styles from '../index.module.scss';

interface SearchBarProps {
  searchQuery: string;
  loading: boolean;
  onInputChange: (value: string) => void;
  onSearch: (value: string) => void;
  onImageSelect: (files: UploadFile[]) => void;
}

const SearchBar: React.FC<SearchBarProps> = ({
  searchQuery,
  loading,
  onInputChange,
  onSearch,
  onImageSelect,
}) => {
  return (
    <div className={styles.searchBar}>
      <Input
        placeholder="查询图片名称"
        value={searchQuery}
        onChange={onInputChange}
        onPressEnter={() => onSearch(searchQuery)}
        innerAfter={
          <Icon
            type="search"
            size="xs"
            onClick={() => onSearch(searchQuery)}
            className={styles.searchIcon}
          />
        }
      />
      <Upload.Selecter accept="image/png, image/jpg, image/jpeg, image/gif" onSelect={onImageSelect}>
        <Button type="secondary" loading={loading}>
          上传图片
        </Button>
      </Upload.Selecter>
    </div>
  );
};

export default SearchBar;
