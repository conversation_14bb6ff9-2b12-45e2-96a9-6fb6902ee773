import React, { useState, useEffect } from 'react';
import { Dialog, Grid, Message, Upload } from '@alifd/next';
import styles from './index.module.scss';
import constant from '@/utils/constant';
import { UploadFile } from '@alifd/next/lib/upload';
import FolderTree, { FolderItem } from './components/FolderTree';
import MaterialGrid from './components/MaterialGrid';
import SearchBar from './components/SearchBar';
import Footer from './components/Footer';
import { MaterialItemData } from './components/MaterialItem';


const { Row, Col } = Grid;

interface MaterialCenterProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (material: MaterialItemData) => void;
  maxFileSize?: number; // 默认为5MB
  width?: number; // 默认为600
  height?: number; // 默认为600
}

// 获取目录数据
const getFolderData = async (): Promise<FolderItem[]> => {
  // 模拟API请求，实际项目中应该调用真实API
  return [
    {
      key: '101',
      label: '店铺图',
      icon: 'form',
      children: [
        {
          key: '72618681472336366451763',
          label: '切片',
          icon: 'form',
        },
      ],
    },
    {
      key: '102',
      label: '测试',
      icon: 'form',
    },
    {
      key: '103',
      label: '飞鸽图',
      icon: 'form',
      children: [
        {
          key: '73753777154257881981763',
          label: '生日礼遇',
          icon: 'form',
        },
        {
          key: '73948221401835768831763',
          label: '徽章',
          icon: 'form',
        },
        {
          key: '74065193889236585341763',
          label: '百瓶活动',
          icon: 'form',
        },
      ],
    },
  ];
};

// 获取图片数据
const getMaterialData = async (key: string): Promise<MaterialItemData[]> => {
  // 模拟API请求，实际项目中应该调用真实API
  console.log('key', key);
  return [
    {
      materialId: '74878932502849620820763',
      materialName: '会员抽签购-2025/3/31.png',
      url: 'https://p3-aio.ecombdimg.com/obj/ecom-shop-material/png_m_fab90db52638063bc8b190c94493fd4b_sx_221919_www750-385',
    },
    {
      materialId: '74878931558933302300763',
      materialName: '0元入会 解锁会员专属特权-2025/3/31.png',
      url: 'https://p3-aio.ecombdimg.com/obj/ecom-shop-material/png_m_5f78c36974086a76a4dee7b481c11a30_sx_228596_www750-499',
    },
    {
      materialId: '74878930616122247870763',
      materialName: '幸运盲盒 参与活动 赢好礼-2025/3/31.png',
      url: 'https://p3-aio.ecombdimg.com/obj/ecom-shop-material/png_m_dd617a01823ab1a55025a6aec7a01833_sx_258853_www500-500',
    },
    {
      materialId: '74878929765779540830763',
      materialName: '会员抽签购-2025/3/31.png',
      url: 'https://placehold.jp/500x500.png?a=png_m_dd617a01823ab1a55025a6aec7a01833_sx_258853_www500-500',
    },
    {
      materialId: '74878927462386240000763',
      materialName: '20250228-112521-2025/3/31.png',
      url: 'https://p3-aio.ecombdimg.com/obj/ecom-shop-material/png_m_adc2a3075ba5be5527f96f769489dedb_sx_141967_www750-897',
    },
    {
      materialId: '74878912896238226420763',
      materialName: 'resized-2025/3/31-16:42:25',
      url: 'https://p3-aio.ecombdimg.com/obj/ecom-shop-material/png_m_3edb172b25609a352a2ba933618ed293_sx_287336_www686-521',
    },
  ];
};

const MaterialCenter: React.FC<MaterialCenterProps> = (props) => {
  const {
    visible,
    onClose,
    onSelect,
    maxFileSize = 5,
    width = 600,
    height = 600,
  } = props;
  const [treeData, setTreeData] = useState<FolderItem[]>([]);
  const [materials, setMaterials] = useState<MaterialItemData[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedMaterial, setSelectedMaterial] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [expandedKeys, setExpandedKeys] = useState<string[]>(['101']);
  const [selectedKeys, setSelectedKeys] = useState<string[]>(['101']);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [currentKey, setCurrentKey] = useState<string>('');
  // 添加图片尺寸合规状态
  const [imageSizes, setImageSizes] = useState<Record<string, {width: number; height: number; valid: boolean}>>({});
  // 素材中心网址
  const meterialUrl = 'https://fxg.jinritemai.com/ffa/creative/material_center?btm_ppre=a2427.b76571.c902327.d871297&btm_pre=a2427.b919503.c0.d0&btm_show_id=a6b6675c-492c-46fe-b465-a3a6b8bd1752';
  const pageSize = 15; // 修改为每页15项（3行5列）
  const totalCount = 20; // 动态使用实际材料数量

  // 加载目录数据
  useEffect(() => {
    const loadFolderData = async () => {
      try {
        const data = await getFolderData();
        setTreeData(data);
        // 默认选中并加载店铺图菜单下的图片
        setCurrentKey('101');
        setSelectedKeys(['101']);
        try {
          const materialsData = await getMaterialData('101');
          setMaterials(materialsData);
        } catch (error) {
          Message.error('加载素材数据失败');
          console.error('加载素材失败:', error);
        }
      } catch (error) {
        Message.error('加载目录失败');
        console.error(error);
      }
    };

    if (visible) {
      loadFolderData().then();
    }
  }, [visible]);

  // 点击目录节点处理函数
  const handleNodeClick = async (keys: string[], info: any) => {
    // 管理展开状态
    const shouldExpand = keys.length > 0;
    const node = shouldExpand ? info.selectedNodes?.[0]?.props : null;
    // 只在以下情况设置一次expandedKeys：
    // 1. 没有选中节点时清空
    // 2. 正常选中节点时展开
    const expandedKeys = shouldExpand ? keys : [];
    setExpandedKeys(expandedKeys);
    setSelectedKeys(keys);

    // 如果没有选中节点或节点已展开，则不加载数据
    if (!shouldExpand) return;
    // 只有叶子节点才加载素材数据
    const key = keys[0];
    if (node) {
      try {
        // 设置当前key
        setCurrentKey(key);
        // setMaterials(await getMaterialData(key));
        // 返回随机数组，模拟不同接口返回的数据
        const materialsData = await getMaterialData(key);
        const shuffledMaterials = [...materialsData];
        for (let i = shuffledMaterials.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [shuffledMaterials[i], shuffledMaterials[j]] = [shuffledMaterials[j], shuffledMaterials[i]];
        }
        setMaterials(shuffledMaterials);
      } catch (error) {
        Message.error('加载素材数据失败');
        console.error('加载素材失败:', error);
      }
    }
  };

  // 处理搜索框输入变化
  const handleInputChange = (value: string) => {
    setSearchQuery(value);
  };

  // 选择图片
  const handleSelectMaterial = (materialId: string) => {
    // 检查图片是否符合尺寸要求
    if (imageSizes[materialId]?.valid) {
      setSelectedMaterial(materialId);
    } else if (imageSizes[materialId]) {
      // 图片不符合要求，提示用户
      Message.error('所选图片尺寸不符合要求');
    }
  };

  // 确认选择
  const handleConfirm = () => {
    if (!selectedMaterial) {
      Message.error('请选择图片');
      return;
    }

    // 再次验证所选图片是否符合要求
    if (!imageSizes[selectedMaterial]?.valid) {
      Message.error('所选图片尺寸不符合要求');
      return;
    }

    const material = materials.find(item => item.materialId === selectedMaterial);
    if (material) {
      onSelect(material);
      onClose();
    }
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    // 实际项目中应该调用搜索API
    console.log('搜索:', value);
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // 实际项目中应该重新加载数据
  };

  // 处理树节点展开
  const handleExpand = (keys: string[]) => {
    setExpandedKeys(keys);
    setAutoExpandParent(false);
  };

  // 检查图片尺寸是否符合要求
  const checkImageSize = (materialId: string, url: string) => {
    const img = new Image();
    img.onload = () => {
      const valid = img.width === width && img.height === height;
      setImageSizes(prev => ({
        ...prev,
        [materialId]: {
          width: img.width,
          height: img.height,
          valid,
        },
      }));
    };
    img.onerror = () => {
      setImageSizes(prev => ({
        ...prev,
        [materialId]: {
          width: 0,
          height: 0,
          valid: false,
        },
      }));
    };
    img.src = url;
  };

  // 在设置材料时检查所有图片的尺寸
  useEffect(() => {
    materials.forEach(item => {
      if (item.url && !imageSizes[item.materialId]) {
        checkImageSize(item.materialId, item.url);
      }
    });
  }, [materials]);

  const checkSize = (file) => {
    return new Promise((resolve): any => {
      if (file.size > 3 * 1024 * 1024) {
        Message.error('图片尺寸不可以大于3MB');
        resolve(false);
        return false;
      }
      // 读取图片数据
      const reader = new FileReader();
      reader.onload = function (e) {
        const image: any = new Image();
        image.onload = function () {
          resolve(true);
          return false;
        };
        image.src = e.target!.result;
      };
      reader.readAsDataURL(file);
    });
  };

  const tokenName: string = localStorage.getItem(constant.LZ_SSO_TOKEN_NAME)!;
  const token = localStorage.getItem(constant.LZ_SSO_TOKEN);

  const uploader = new Upload.Uploader({
    action: `${process.env.ICE_BASE_URL}/image/uploadImage`,
    name: 'file',
    // 这个东西必须姚设置，否则会遇到跨域拦截问题
    withCredentials: false,
    data: {},
    headers: {
      [tokenName]: token,
    },
    onSuccess: async () => {
      try {
        setLoading(false);
        await getMaterialData(currentKey);
      } catch (e) {
        console.error('图片上传失败');
      }
    },
  });

  const onHandleImgSelect = async (files: UploadFile[]) => {
    setLoading(true);
    const file = files[0];
    const allow = await checkSize(file);
    if (allow) {
      uploader.startUpload(file);
    }
  };

  const renderDialogTitle = () => {
    return (
      <div>
        <span>选择图片</span>
        <span className={styles.subTitle}>{`（宽度${width}，高度${height}，大小不超过${maxFileSize}M）`}</span>
      </div>
    );
  };

  return (
    <Dialog
      visible={visible}
      title={renderDialogTitle()}
      onClose={onClose}
      onCancel={onClose}
      onOk={handleConfirm}
      footer={false}
      closeMode={['close', 'mask', 'esc']}
      className={styles.dialogWidth}
    >
      <div className={styles.container}>
        <Row>
          <Col span={5}>
            <FolderTree
              treeData={treeData}
              expandedKeys={expandedKeys}
              selectedKeys={selectedKeys}
              autoExpandParent={autoExpandParent}
              onSelect={handleNodeClick}
              onExpand={handleExpand}
            />
          </Col>
          <Col span={19}>
            <div className={styles.rightPanel}>
              <SearchBar
                searchQuery={searchQuery}
                loading={loading}
                onInputChange={handleInputChange}
                onSearch={handleSearch}
                onImageSelect={onHandleImgSelect}
              />
              <MaterialGrid
                materials={materials}
                loading={loading}
                selectedMaterial={selectedMaterial}
                imageSizes={imageSizes}
                onSelectMaterial={handleSelectMaterial}
              />
              <Footer
                currentPage={currentPage}
                totalCount={totalCount}
                pageSize={pageSize}
                meterialUrl={meterialUrl}
                onPageChange={handlePageChange}
                onConfirm={handleConfirm}
                onClose={onClose}
              />
            </div>
          </Col>
        </Row>
      </div>
    </Dialog>
  );
};

export default MaterialCenter;
