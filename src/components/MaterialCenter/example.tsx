import React, { useState } from 'react';
import { Button, Message } from '@alifd/next';
import MaterialCenter from './index';

interface MaterialItem {
  materialId: string;
  materialName: string;
  url: string;
}

const MaterialCenterExample: React.FC = () => {
  const [visible, setVisible] = useState<boolean>(false);
  const [selectedMaterial, setSelectedMaterial] = useState<MaterialItem | null>(null);

  const handleOpen = () => {
    setVisible(true);
  };

  const handleClose = () => {
    setVisible(false);
  };

  const handleSelect = (material: MaterialItem) => {
    setSelectedMaterial(material);
    Message.success(`已选择图片：${material.materialName}`);
  };

  return (
    <div style={{ padding: 20 }}>
      <Button type="primary" onClick={handleOpen}>打开素材中心</Button>

      {selectedMaterial && (
        <div style={{ marginTop: 20 }}>
          <h3>已选择图片：</h3>
          <p>文件名：{selectedMaterial.materialName}</p>
          <div style={{ width: 300, marginTop: 10 }}>
            <img src={selectedMaterial.url} alt={selectedMaterial.materialName} style={{ width: '100%' }} />
          </div>
        </div>
      )}

      <MaterialCenter
        visible={visible}
        onClose={handleClose}
        onSelect={handleSelect}
        maxFileSize={5}
        width={600}
        height={600}
      />
    </div>
  );
};

export default MaterialCenterExample;
