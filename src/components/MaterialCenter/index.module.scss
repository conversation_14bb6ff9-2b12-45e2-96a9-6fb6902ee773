// 素材中心样式
.container {
  height: 600px;
  overflow: hidden;
  :global {
    .next-tree-child-tree .next-tree-switcher.next-noop-noline {
      width: 10px;
      margin-right: 0;
    }

    .next-tree-switcher.next-noline {
      margin-right: 0;
    }
    .next-tree-switcher {
      margin-right: 0;
    }
  }
}

.subTitle {
  font-size: 14px;
  color: #666;
}

.leftPanel {
  height: 600px;
  border-right: 1px solid #e6e6e6;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
}
.folderTitle {
  padding: 12px;
  font-weight: 500;
  border-bottom: 1px solid #e6e6e6;
}

.treeContainer {
  padding: 12px;
  width: 165px;
}

.rightPanel {
  height: 600px;
  display: flex;
  flex-direction: column;
}

.searchBar {
  padding: 12px;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.materialGrid {
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 12px;
  width: 100%;
}

.materialItem {
  position: relative;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  height: 124.66px;
  width: 110.67px;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  &.selected {
    outline: 2px solid var(--primary-color);
    background-color: #fff;
  }
}

.imageWrapper {
  position: relative;
  width: 100%;
  padding-bottom: 88%;
  background-color: #f5f5f5;
  overflow: hidden;
}

.materialImage {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
  padding: 4px;
}

.imageSize {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 2px;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.45);
  color: #fff;
  font-size: 12px;
}

.imageName {
  padding: 6px;
  font-size: 12px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #333;
}

.selectedMark {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  z-index: 1;
}

.invalidMask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.45);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2;

  i {
    color: white;
  }
}

.invalidText {
  color: #fff;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 2px;
}

.loading, .empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
}

.footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 12px;
}

.footerBtn {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 12px;
  gap: 12px;
}

.usageInfo {
  color: #666;
  font-size: 12px;
  text-align: center;
  width: 100%;
  height: 57px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top: 1px solid #e6e6e6;
}

.detailLink {
  color: var(--primary-color);
  text-decoration: none;
  margin-left: 5px;
}


.centerLink {
  padding: 12px;
  text-align: center;
  color: #666;
  font-size: 12px;

  a {
    color: var(--primary-color);
    text-decoration: none;
  }
}

.footerContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #e6e6e6;
}

.searchIcon {
  margin: 4px;
  cursor: pointer;
}

.dialogWidth {
  width: 800px;
}
