import { Box, Button } from '@alifd/next';
import './index.scss';

interface IntroProps {
  activityName?: string;
  docLinkText?: string;
  docLink?: string;
  instructions?: string;
  qrcodeSrc?: string;
}
const Intro = (props: IntroProps) => {
  const { activityName = '', docLinkText = '如何创建？', docLink, instructions = '', qrcodeSrc = '' } = props;
  const openWeb = (url?: string) => {
    url && window.open(url, '_blank');
  };
  return (
    <Box
      className="activity-intro"
      padding={[10, 15]}
      spacing={10}
      direction="row"
      justify="space-between"
    >
      {/* 左侧内容：标题、链接、活动说明 */}
      <Box
        className="left-content"
        spacing={10}
        direction="column"
      >
        <Box
          className="title-row"
          direction="row"
          spacing={10}
          align="center"
        >
          <h2 className="activity-title">{activityName}</h2>
          {/* “如何创建”链接，可传入点击事件 */}
          <Button
            text
            type="primary"
            className="doc-link"
            onClick={() => {
              openWeb(docLink);
            }}
          >
            {docLinkText}
          </Button>
        </Box>
        <Box className="instructions">{instructions}</Box>
      </Box>

      {/* 右侧内容：二维码预览 */}
      {qrcodeSrc && (
        <Box
          className="right-content"
          align="center"
          spacing={10}
        >
          <img
            src={qrcodeSrc}
            alt={'二维码预览'}
            className="activity-qrcode"
          />
          <p className="right-content-tips">使用抖音APP扫一扫预览</p>
        </Box>
      )}
    </Box>);
};

export default Intro;
