import React, { useState, useEffect } from 'react';
import { history } from 'ice';
import styles from './index.module.scss';
import { getCode, login, phoneLogin } from '@/api/user';
import { getAllShopList } from '@/api/permission';
import constant from '@/utils/constant';
import { Message } from '@alifd/next';

// 倒计时相关常量
const VERIFY_CODE_COUNTDOWN_KEY = 'verifyCodeCountdownEndTime';
const VERIFY_CODE_PHONE_KEY = 'verifyCodePhone';

export default function Login() {
  const isDev = process.env.NODE_ENV === 'development';
  const [account, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [errorMsg, setErrorMsg] = useState('');
  const [formActive, setFormActive] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loginType] = useState<'account' | 'phone'>(isDev ? 'account' : 'phone'); // 'account' 或 'phone'
  const [phone, setPhone] = useState('');
  const [verifyCode, setVerifyCode] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [sending, setSending] = useState(false);

  const toLogin = async ({ account, password }) => {
    setLoading(true);
    try {
      const loginInfo = await login({
        account,
        password,
      });
      localStorage.setItem(constant.LZ_SSO_TOKEN, loginInfo.tokenValue!);
      localStorage.setItem(constant.LZ_SSO_TOKEN_NAME, loginInfo.tokenName!);
      localStorage.setItem(constant.LZ_USER_INFO, JSON.stringify(loginInfo));
      const shopList = await getAllShopList();
      localStorage.setItem(constant.LZ_SHOP_LIST, JSON.stringify(shopList));
      history?.push('/shop');
    } catch (e) {
      setErrorMsg(e.message);
    } finally {
      setLoading(false);
    }
  };

  const toPhoneLogin = async ({ phone, verifyCode }) => {
    setLoading(true);
    try {
      const loginInfo = await phoneLogin({
        mobile: phone,
        code: verifyCode,
      });
      localStorage.setItem(constant.LZ_SSO_TOKEN, loginInfo.tokenValue!);
      localStorage.setItem(constant.LZ_SSO_TOKEN_NAME, loginInfo.tokenName!);
      localStorage.setItem(constant.LZ_USER_INFO, JSON.stringify(loginInfo));
      const shopList = await getAllShopList();
      localStorage.setItem(constant.LZ_SHOP_LIST, JSON.stringify(shopList));
      history?.push('/shop');
    } catch (e) {
      setErrorMsg(e.message);
    } finally {
      setLoading(false);
    }
  };

  // 初始化倒计时状态
  useEffect(() => {
    const endTimeStr = localStorage.getItem(VERIFY_CODE_COUNTDOWN_KEY);
    const savedPhone = localStorage.getItem(VERIFY_CODE_PHONE_KEY);

    if (endTimeStr && savedPhone) {
      const endTime = parseInt(endTimeStr, 10);
      const now = Date.now();

      if (endTime > now) {
        setPhone(savedPhone);
        const remainingSeconds = Math.ceil((endTime - now) / 1000);
        setCountdown(remainingSeconds);
      } else {
        // 倒计时已结束，清除存储
        localStorage.removeItem(VERIFY_CODE_COUNTDOWN_KEY);
        localStorage.removeItem(VERIFY_CODE_PHONE_KEY);
      }
    }
  }, []);

  const sendVerifyCode = async () => {
    if (countdown > 0 || sending) return;
    if (!phone || !/^1\d{10}$/.test(phone)) {
      setErrorMsg('请输入正确的手机号');
      return;
    }
    setErrorMsg('');
    setSending(true);
    try {
      await getCode({
        mobile: phone,
      });
      // 计算倒计时结束时间并存储
      const countdownSeconds = 60;
      const endTime = Date.now() + countdownSeconds * 1000;
      localStorage.setItem(VERIFY_CODE_COUNTDOWN_KEY, endTime.toString());
      localStorage.setItem(VERIFY_CODE_PHONE_KEY, phone);
      setCountdown(countdownSeconds);
      Message.success('验证码已发送到手机');
    } catch (e) {
      setErrorMsg(e.message);
    } finally {
      setSending(false);
    }
  };

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    if (countdown > 0) {
      timer = setTimeout(() => {
        const newCountdown = countdown - 1;
        setCountdown(newCountdown);

        // 倒计时结束时清除localStorage
        if (newCountdown === 0) {
          localStorage.removeItem(VERIFY_CODE_COUNTDOWN_KEY);
          localStorage.removeItem(VERIFY_CODE_PHONE_KEY);
        }
      }, 1000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [countdown]);

  useEffect(() => {
    setFormActive(true);
  }, []);

  const handleSubmit = async (formEvent: React.FormEvent) => {
    formEvent.preventDefault();

    if (loginType === 'account') {
      if (!account || !password) {
        setErrorMsg('请输入账号和密码');
        return;
      }
      setErrorMsg('');
      await toLogin({
        account,
        password,
      });
    } else {
      if (!phone || !verifyCode) {
        setErrorMsg('请输入手机号和验证码');
        return;
      }
      if (!/^1\d{10}$/.test(phone)) {
        setErrorMsg('请输入正确的手机号');
        return;
      }
      setErrorMsg('');
      await toPhoneLogin({
        phone,
        verifyCode,
      });
    }
  };

  return (
    <div className={styles.loginContainer}>
      <div className={`${styles.loginCard} ${formActive ? styles.active : ''}`}>

        <div className={styles.logoContainer}>
          <div className={styles.logo}>
            <img src="https://novae.oss-cn-zhangjiakou.aliyuncs.com/douyin/images/logo.png" alt="" />
          </div>
          <div className={styles.title}>超级追单</div>
        </div>

        <form onSubmit={handleSubmit} className={styles.loginForm}>
          {errorMsg && <div className={styles.errorMessage}>{errorMsg}</div>}

          {loginType === 'account' ? (
            <>
              <div className={[styles.inputGroup, styles.usernameInput].join(' ')}>
                <label htmlFor="account">账号</label>
                <input
                  type="text"
                  id="account"
                  value={account}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder="请输入账号"
                  className={account ? styles.filled : ''}
                />
              </div>

              <div className={[styles.inputGroup, styles.passwordInput].join(' ')}>
                <label htmlFor="password">密码</label>
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="请输入密码"
                  className={password ? styles.filled : ''}
                />
              </div>
            </>
          ) : (
            <>
              <div className={[styles.inputGroup, styles.phoneInput].join(' ')}>
                <label htmlFor="phone">手机号</label>
                <input
                  type="tel"
                  id="phone"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  placeholder="请输入手机号"
                  className={phone ? styles.filled : ''}
                  maxLength={11}
                />
              </div>

              <div className={[styles.inputGroup, styles.verifyCodeInput].join(' ')}>
                <label htmlFor="verifyCode">验证码</label>
                <input
                  type="text"
                  id="verifyCode"
                  value={verifyCode}
                  onChange={(e) => setVerifyCode(e.target.value)}
                  placeholder="请输入验证码"
                  className={verifyCode ? styles.filled : ''}
                  maxLength={6}
                />
                <button
                  type="button"
                  className={styles.sendCodeBtn}
                  disabled={countdown > 0 || sending}
                  onClick={sendVerifyCode}
                >
                  {sending ? '发送中' : countdown > 0 ? `${countdown}秒后重试` : '发送验证码'}
                </button>
              </div>
            </>
          )}

          <button
            type="submit"
            className={styles.loginButton}
            disabled={loading}
          >
            {loading ? (
              <span className={styles.loadingSpinner}>
                <span className={styles.spinnerDot} />
                <span className={styles.spinnerDot} />
                <span className={styles.spinnerDot} />
              </span>
            ) : (
              '登录'
            )}
          </button>
        </form>

        <div className={styles.footer}>
          <div>版权所有© 2025 陆泽科技有限公司
            <span
              onClick={() => {
                window.open('https://beian.miit.gov.cn/', '_blank');
              }}
              className={styles.link}
            > 辽ICP备18007719号-2</span>
          </div>
        </div>
      </div>
    </div>
  );
}
