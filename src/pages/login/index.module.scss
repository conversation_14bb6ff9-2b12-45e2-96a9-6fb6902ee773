.loginContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: rgb(17, 17, 17);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      linear-gradient(rgba(255, 0, 80, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 250, 240, 0.05) 1px, transparent 1px);
    background-size: 40px 40px;
    z-index: 1;
  }

  &::after {
    content: '';
    position: absolute;
    width: 150%;
    height: 150%;
    top: -25%;
    left: -25%;
    background: radial-gradient(ellipse at center, transparent 0%, rgba(17, 17, 17, 1) 70%);
    z-index: 2;
  }
}

.loginCard {
  width: 400px;
  padding: 40px;
  background-color: rgba(25, 25, 25, 0.8);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    0 0 30px rgba(22, 119, 255, 0.2);
  ;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  z-index: 3;


  &:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6),
      0 0 0 1px rgba(255, 255, 255, 0.07),
      0 0 30px var(--primary-color-dark);
  }

  &.active {
    animation: fadeInUp 0.6s forwards;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.logoContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
  gap: 15px;
  position: relative;

  .logo {
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    position: relative;
  }

  .logo img {
    position: absolute;
    width: 40px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}

.logo {
  height: 60px;
  filter: drop-shadow(0 0 8px var(--primary-color-light));
  border-radius: 15px;
}

.title {
  font-size: 28px;
  color: white;
  text-align: center;
  //margin-bottom: 30px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  z-index: 2;
  text-shadow: 0 0 5px var(--primary-cyan-light), 0 0 10px var(--primary-color-light);
}

.loginForm {
  margin-bottom: 30px;
  position: relative;
  z-index: 2;
}

.errorMessage {
  padding: 12px;
  margin-bottom: 20px;
  background-color: rgba(106, 0, 37, .3);
  border: 1px solid rgb(106, 0, 37);
  border-radius: 8px;
  color: rgb(255, 44, 108);
  font-size: 14px;
  animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

@keyframes shake {

  10%,
  90% {
    transform: translateX(-1px);
  }

  20%,
  80% {
    transform: translateX(2px);
  }

  30%,
  50%,
  70% {
    transform: translateX(-4px);
  }

  40%,
  60% {
    transform: translateX(4px);
  }
}

.inputGroup {
  margin-bottom: 25px;
  position: relative;

  label {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s;
    font-size: 16px;
    letter-spacing: 0.5px;
  }

  input {
    width: 100%;
    padding: 15px;
    padding-left: 40px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    font-size: 15px;
    background-color: rgba(30, 30, 30, 0.6);
    color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s;
    box-sizing: border-box;

    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 4px var(--primary-color-lighter);
      background-color: rgba(30, 30, 30, 0.8);
    }

    &::placeholder {
      color: rgba(255, 255, 255, 0.4);
    }

    /* 处理Chrome自动填充样式 */
    &:-webkit-autofill,
    &:-webkit-autofill:hover,
    &:-webkit-autofill:focus,
    &:-webkit-autofill:active {
      -webkit-text-fill-color: white !important;
      -webkit-box-shadow: 0 0 0 1000px rgba(30, 30, 30, 0.8) inset !important;
      box-shadow: 0 0 0 1000px rgba(30, 30, 30, 0.8) inset !important;
      transition: background-color 5000s ease-in-out 0s !important;
      caret-color: white !important;
      border-color: var(--primary-cyan) !important;
      background-color: rgba(30, 30, 30, 0.8) !important;
      color: white !important;
      background-clip: border-box !important;
    }
  }

  /* 使用延迟动画技巧来覆盖Chrome自动填充样式 */
  @keyframes autofillFix {
    to {
      color: white;
      background-color: rgba(30, 30, 30, 0.8);
    }
  }

  input:-webkit-autofill {
    animation-name: autofillFix;
    animation-fill-mode: both;
    animation-duration: 5000s;
  }

  &::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 46.5px;
    width: 18px;
    height: 18px;
    opacity: 0.8;
    z-index: 1;
    background-size: contain;
    background-repeat: no-repeat;
    transition: all 0.3s;
  }

  &.usernameInput::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231677ff'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'%3E%3C/path%3E%3C/svg%3E");
  }

  &.phoneInput::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231677ff'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'%3E%3C/path%3E%3C/svg%3E");
  }

  &.passwordInput::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231677ff'%3E%3Cpath d='M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z'%3E%3C/path%3E%3C/svg%3E");
  }

  &.verifyCodeInput::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231677ff'%3E%3Cpath d='M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z'%3E%3C/path%3E%3C/svg%3E");
  }

  input.filled {
    border-color: var(--primary-cyan);
    box-shadow: 0 0 0 2px var(--primary-cyan-lighter);
  }
}

.loginButton {
  width: 100%;
  padding: 15px 0;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 4px 15px var(--primary-color-dark);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.6s;
  }

  &:hover {
    background: var(--primary-color);
    box-shadow: 0 6px 20px var(--primary-color-dark);
    transform: translateY(-2px);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(1px);
    box-shadow: 0 2px 8px var(--primary-color-dark);
  }

  &:disabled {
    background: rgb(100, 100, 100);
    cursor: not-allowed;
    box-shadow: none;
  }
}

.footer {
  text-align: center;
  margin-top: 25px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 2;
}


.loadingSpinner {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.spinnerDot {
  width: 6px;
  height: 6px;
  background-color: white;
  border-radius: 50%;
  display: inline-block;
  animation: bounce 1.4s infinite ease-in-out both;

  &:nth-child(1) {
    animation-delay: -0.32s;
  }

  &:nth-child(2) {
    animation-delay: -0.16s;
  }
}

@keyframes bounce {

  0%,
  80%,
  100% {
    transform: scale(0);
  }

  40% {
    transform: scale(1);
  }
}

.link {
  cursor: pointer;

  &:hover {
    color: var(--primary-color);
    text-decoration: underline;
  }
}


/* 手机验证码输入框组 */
.verifyCodeInput {
  position: relative;

  input {
    padding-right: 110px;
    /* 给验证码按钮留出空间 */
  }
}

/* 发送验证码按钮 */
.sendCodeBtn {
  position: absolute;
  right: 8px;
  top: 47%;
  border: 1px solid var(--primary-color);
  background: transparent;
  color: var(--primary-color);
  //border: none;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
  height: 35px;

  &:hover {
    background: var(--primary-color-light);
    color: rgba(255, 255, 255, 0.8);
  }

  &:disabled {
    background: var(--primary-color-darker);
    cursor: not-allowed;
    color: rgba(255, 255, 255, 0.7);
  }
}
