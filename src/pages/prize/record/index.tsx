import { useState, useEffect } from 'react';
import { Box, Input, Select, Button, Table, Pagination, DatePicker2, Message } from '@alifd/next';
import { lotteryGetLotteryRecord, exportProductExportExcelExport } from '@/api/b';
import { downloadExcel } from '@/utils';

import dayjs from 'dayjs';
import Container from '@/components/Container';
import { Auth } from '@/components/Auth';

export default function PrizeRecordsIndexPage() {
  // 查询条件
  const [userNick, setUserNick] = useState('');
  const [ouid, setOuid] = useState('');
  const [activityName, setActivityName] = useState('');
  const [lotteryType, setLotteryType] = useState(0);
  const [timeRange, setTimeRange] = useState<any>([]);

  // 表格数据
  const [dataList, setDataList] = useState([]);
  const [columns, setColumns] = useState<any>([]);
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchList().then();
  }, []);

  const fetchList = async (params?: any) => {
    setLoading(true);
    try {
      const [receiveTimeStart, receiveTimeEnd] = timeRange;
      const res: any = await lotteryGetLotteryRecord({
        userNick: userNick.trim(),
        ouid: ouid.trim(),
        activityName: activityName.trim(),
        lotteryType,
        receiveTimeStart: receiveTimeStart ? dayjs(receiveTimeStart).format('YYYY-MM-DD 00:00:00') : '',
        receiveTimeEnd: receiveTimeEnd ? dayjs(receiveTimeEnd).format('YYYY-MM-DD 23:59:59') : '',
        pageNum,
        pageSize,
        ...params,
      });

      const { list = [], total = 0, title = '[]' } = res;
      setDataList(list);
      setTotal(total);
      let parsedTitle = [];
      try {
        const fixedTitle = title.replace(/,]$/, ']'); // 修复尾部多逗号问题
        parsedTitle = JSON.parse(fixedTitle);
        setColumns(parsedTitle);
      } catch (err) {
        console.error('解析错误内容：', title);
        Message.error('解析表头失败');
      }
    } catch (err) {
      console.error(err);
      Message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setPageNum(1);
    fetchList({
      pageNum: 1,
    });
  };

  const handleReset = () => {
    setUserNick('');
    setOuid('');
    setActivityName('');
    setLotteryType(0);
    setTimeRange([]);
    setPageNum(1);
    fetchList({
      pageNum: 1,
      pageSize: 10,
      userNick: '',
      ouid: '',
      lotteryType: 0,
      activityName: '',
      receiveTimeStart: '',
      receiveTimeEnd: '',
    });
  };

  const handlePageChange = (current) => {
    setPageNum(current);
    fetchList({
      pageNum: current,
    });
  };
  const handlePageSizeChange = (size) => {
    setPageSize(size);
    setPageNum(1);
    fetchList({
      pageSize: size,
    });
  };

  const handleExport = async () => {
    const exportType = 'lotteryReceiveRecord'; // 奖品领取记录-导出
    setLoading(true);
    try {
      const [receiveTimeStart, receiveTimeEnd] = timeRange;
      const exportParams = {
        exportType, // 奖品领取记录-导出
        exportParam: JSON.stringify({
          pageNo: 1,
          pageSize: 0, // 导出全部
          userNick: userNick.trim(),
          ouid: ouid.trim(),
          activityName: activityName.trim(),
          lotteryType,
          receiveTimeStart: receiveTimeStart ? dayjs(receiveTimeStart).format('YYYY-MM-DD 00:00:00') : '',
          receiveTimeEnd: receiveTimeEnd ? dayjs(receiveTimeEnd).format('YYYY-MM-DD 23:59:59') : '',
        }),
      };
      const data: any = await exportProductExportExcelExport(exportParams);
      downloadExcel(data, `奖品领取记录${dayjs().format('YYYY-MM-DD HH:mm')}`);
    } catch (err) {
      console.error(err);
      Message.error('导出失败');
    } finally {
      setLoading(false);
    }
  };

  const PrizeTypeEnum = Object.freeze({
    PRACTICALITY: { label: '实物（手工发货）', value: 7 },
    PRACTICALITY_BY_ORDER: { label: '实物（随单发货）', value: 6 },
    COUPON: { label: '优惠券', value: 3 },
    MEMBER_POINT: { label: '会员积分', value: 9 },
  });

  return (
    <Container>
      <Box spacing={16}>
        {/* 筛选区域 */}
        <Box
          direction="row"
          spacing={10}
        >
          <Input
            label="用户昵称"
            placeholder=""
            value={userNick}
            onChange={(val: string) => setUserNick(val)}
          />
          <Input
            label="openId"
            placeholder=""
            value={ouid}
            onChange={(val: string) => setOuid(val)}
          />
          <Input
            label="活动名称"
            placeholder=""
            value={activityName}
            onChange={(val: string) => setActivityName(val)}
          />
          <Select
            label="奖品类型"
            value={lotteryType}
            onChange={(val: number) => setLotteryType(val)}
            dataSource={[
              { label: '全部', value: 0 },
              ...Object.values(PrizeTypeEnum).map((i: any) => ({
                label: i.label,
                value: i.value,
              })),
            ]}
            style={{ width: 200 }}
          />
          <DatePicker2.RangePicker
            label="领取时间"
            value={timeRange}
            onChange={(val: any) => setTimeRange(val)}
            style={{ width: 280 }}
          />
          <Button
            type="primary"
            onClick={handleSearch}
          >
            查询
          </Button>
          <Button onClick={handleReset}>重置</Button>
          <Auth authKey={'prize_record_export'}>
            <Button
              type="secondary"
              onClick={handleExport}
            >
              导出
            </Button>
          </Auth>
        </Box>

        {/* 表格 */}
        <Table
          dataSource={dataList}
          hasBorder
          loading={loading}
        >
          {columns.map(col => (
            <Table.Column
              key={col.dataIndex}
              title={col.name}
              dataIndex={col.dataIndex}
              cell={value => value || '--'}
            />
          ))}
        </Table>

        {/* 分页 */}
        <Box direction="row" justify="flex-end" >
          <Pagination
            current={pageNum}
            pageSize={pageSize}
            total={total}
            shape="arrow-only"
            onChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
            totalRender={total => `共 ${total} 条`}
            pageSizeSelector="dropdown"
            pageSizePosition="end"
          />
        </Box>

      </Box>
    </Container>
  );
}
