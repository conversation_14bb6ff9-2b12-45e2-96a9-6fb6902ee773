import styles from './index.module.scss';
import { Tab } from '@alifd/next';
import React, { useState } from 'react';
import Intro from '@/components/Intro';
import PhysicalGoodsList from './components/PhysicalGoodsList';
import PhysicalGoodsOrderList from './components/PhysicalGoodsOrderList';
import Container from '@/components/Container';

const instructions = `此项奖品需要商家导出用户填写的收货信息，线下自行发货视频教程
1、奖品冻结：当活动选择实物奖品时，活动创建成功后会冻结相应数量的实物奖品，被冻结部分的奖品不能被其他活动选择，待活动结束后，该活动未发放的实物奖品会解冻为可用状态
2、用户领取奖品：用户获得实物奖品后需要填写收货信息，商家根据收货信息线下发货`;

export default function PhysicalPrize() {
  const [activeTabKey, setActiveTabKey] = useState('goods');

  const handleTabChange = key => {
    setActiveTabKey(key);
  };
  return (
    <div className={styles.container}>
      <Intro
        activityName="实物奖品"
        docLink="https://gyj4qdmjsv.feishu.cn/wiki/KatTwljdjismfZkiXYGcbkoSnTc?from=from_copylink"
        instructions={instructions}
      />
      <Tab
        activeKey={activeTabKey}
        onChange={handleTabChange}
      >
        <Tab.Item
          key="goods"
          title="实物奖品"
          closeable={false}
        >
          <Container style={{ marginTop: 20 }}>
            <PhysicalGoodsList />
          </Container>


        </Tab.Item>
        <Tab.Item
          key="orders"
          title="实物奖品订单"
        >
          <Container style={{ marginTop: 20 }}>
            <PhysicalGoodsOrderList />
          </Container>
        </Tab.Item>
      </Tab>
    </div>
  );
}


