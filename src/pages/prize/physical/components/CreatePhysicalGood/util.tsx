import { Dialog } from '@alifd/next';
import CreatePhysicalGoodModalContent from './index';
import { addDialogRef } from '@/utils/dialogMapper';

export default function createPhysicalGood(initialValues: any = {}) {
  const skuCode = initialValues?.skuCode || '';
  return new Promise(resolve => {
    const dialogRef = Dialog.show({
      v2: true,
      title: `${skuCode ? '编辑' : '新建'}实物奖品`,
      width: 500,
      centered: true,
      closeMode: ['close'],
      content: (
        <CreatePhysicalGoodModalContent
          initialValues={initialValues}
          onResolve={result => {
            resolve(result);
            dialogRef.hide();
          }}
        />
    ),
    footer: false,
  });
    addDialogRef(dialogRef);
  });
}
