import { useState } from 'react';
import { Form, Field, Input, Button, Box, Message, NumberPicker } from '@alifd/next';
import ImgUploadFromItem from '@/components/ImgUpload';
import { prizeAddSku } from '@/api/vf';
import { PrizeSkuAddRequestVO } from '@/api/types';

export default function CreatePhysicalGoodModalContent({ initialValues = {}, onResolve }) {
  const goodField = Field.useField({ values: initialValues });
  const [loading, setLoading] = useState(false);

  const skuCode = goodField.getValue('skuCode');

  const handleOk = () => {
    goodField.validate(async (errors, values: PrizeSkuAddRequestVO) => {
      if (!errors) {
        const { skuMainPicture, skuName, skuValue, quantityTotal } = values;
        try {
          setLoading(true);
          await prizeAddSku({
            skuMainPicture,
            skuName,
            skuValue,
            skuCode: skuCode || '',
            quantityTotal,
          } as any);
          Message.success(`${skuCode ? '编辑' : '新建'}实物奖品成功`);
          onResolve(true);
        } catch (err) {
          Message.error(`${skuCode ? '编辑' : '新建'}实物奖品失败`);
        } finally {
          setLoading(false);
        }
      }
    });
  };

  const handleCancel = () => {
    onResolve(null);
  };

  return (
    <Box style={{ padding: 20 }}>
      <Form field={goodField}>
        <ImgUploadFromItem
          name="skuMainPicture"
          label="实物奖品主图"
          required
          requiredMessage="实物奖品主图不能为空"
          disabled={!!skuCode}
          img={{
            value: goodField.getValue('skuMainPicture'),
            width: 500,
            height: 500,
          }}
          onSuccess={(url) => {
            goodField.setValue('skuMainPicture', url);
            setTimeout(() => {
              goodField.validate('skuMainPicture');
            }, 10);
          }}
          onReset={() => {
            goodField.setValue('skuMainPicture', '');
          }}
        />
        <Form.Item
          name="skuName"
          label="实物奖品名称"
          required
          requiredMessage="实物奖品名称不能为空"
          pattern="^[\u4E00-\u9FA5A-Za-z0-9_\-/.]+$"
          patternMessage="不可输入特殊符号，支持中文、英文、数字及下划线"
        >
          <Input
            maxLength={15}
            trim
            defaultValue={goodField.getValue('benefitName')}
            showLimitHint
            placeholder="请输入实物奖品名称"
          />
        </Form.Item>
        <Form.Item name="skuValue" label="实物奖品价值" required requiredMessage="实物奖品价值不能为空">
          <NumberPicker
            min={0.01}
            max={1000000}
            step={0.01}
            defaultValue={goodField.getValue('amount')}
            hasTrigger={false}
            style={{ width: '100%' }}
          />
        </Form.Item>
        <Form.Item
          name="quantityTotal"
          label="奖品总库存"
          required
          requiredMessage="奖品总库存不能为空"
          disabled={!!skuCode}
        >
          <NumberPicker
            min={1}
            max={1000000}
            hasTrigger={false}
            defaultValue={goodField.getValue('prizeQuantity')}
            style={{ width: '100%' }}
          />
        </Form.Item>
      </Form>
      <Box direction="row" justify="center" spacing={10} margin={[20, 0, 0, 0]}>
        <Button onClick={handleCancel}>取消</Button>
        <Button type="primary" onClick={handleOk} disabled={loading} loading={loading}>
          确定
        </Button>
      </Box>
    </Box>
  );
}
