import React from 'react';
import { Box, Form, Field, Input, Button, Message, NumberPicker } from '@alifd/next';
import { prizeAddStock } from '@/api/vf';
import { PrizeSkuAddStockRequestVO } from '@/api/types';

export default function AppendStockModalContent({ record, onResolve }) {
  // 使用 Field 管理表单，初始值为空；用户输入追加库存值
  const stockField = Field.useField({ values: { addQuantity: '' } });
  const [loading, setLoading] = React.useState(false);

  // 点击"确定"处理函数
  const handleOk = () => {
    stockField.validate(async (errors, values: PrizeSkuAddStockRequestVO) => {
      if (errors) return;
      const { addQuantity } = values;
      // 确保输入数字有效
      if (!addQuantity || Number.isNaN(+addQuantity)) {
        return Message.error('请输入有效的追加库存数量');
      }
      try {
        setLoading(true);
        // 调用接口更新库存，注意 addQuantity 应为数值类型
        await prizeAddStock({
          skuCode: record.skuCode,
          addQuantity: addQuantity,
        });
        Message.success('库存追加成功');
        onResolve(true);
      } catch (err) {
        console.error('updateResPrizeSkuStock error:', err);
        Message.error('库存追加失败');
      } finally {
        setLoading(false);
      }
    });
  };

  const handleCancel = () => {
    onResolve(null);
  };

  return (
    <Box>
      <Form field={stockField}>
        {/* 显示只读信息 */}
        <Form.Item label="实物名称" disabled>
          <Input
            value={record.benefitName}
            readOnly
          />
        </Form.Item>
        <Form.Item label="当前可用库存" disabled>
          <Input
            value={record.remainPrizeQuantity}
            readOnly
          />
        </Form.Item>
        {/* 用户输入追加库存数量 */}
        <Form.Item
          name="addQuantity"
          label="追加数量"
          required
          requiredMessage="请输入追加库存数量"
        >
          <NumberPicker
            min={0}
            max={999999999}
            hasTrigger={false}
            style={{ width: '100%' }}
          />
        </Form.Item>
      </Form>
      <Box
        direction="row"
        justify="center"
        spacing={10}
        margin={[20, 0, 0, 0]}
      >
        <Button onClick={handleCancel}>取消</Button>
        <Button
          type="primary"
          onClick={handleOk}
          loading={loading}
          disabled={loading}
        >
          确定
        </Button>
      </Box>
    </Box>
  );
}
