import { Dialog } from '@alifd/next';
import AppendStockModalContent from './index';
import { addDialogRef } from '@/utils/dialogMapper';

export default function appendStock(record = {}) {
  return new Promise(resolve => {
    const dialogRef = Dialog.show({
      v2: true,
      title: '追加实物奖品库存',
      width: 500,
      centered: true,
      closeMode: ['close'],
      content: (
        <AppendStockModalContent
          record={record}
          onResolve={res => {
            resolve(res);
            dialogRef.hide();
          }}
        />
      ),
      footer: false,
    });
    addDialogRef(dialogRef);
  });
}
