import { Dialog, Button } from '@alifd/next';
import Index from './index';
import { addDialogRef } from '@/utils/dialogMapper';

export default function viewBindingActivities(record) {
  const dialogRef = Dialog.show({
    v2: true,
    title: '查看绑定活动',
    width: 600,
    centered: true,
    closeMode: ['close'],
    content: (
      <Index
        record={record}
      />
    ),
    footer: (
      <>
        <Button
          type="primary"
          size={'small'}
          onClick={() => dialogRef.hide()}
        >
          我知道了
        </Button>
      </>
    ),
  });
  addDialogRef(dialogRef);
}
