import { useEffect, useState } from 'react';
import { Table, Box, Message } from '@alifd/next';
import { prizeGetBindActivityPage } from '@/api/vf';
import { PrizeSkuRelationActivityPageResponseVO } from '@/api/types';
import dayjs from 'dayjs';

export default function Index({ record }) {
  const [list, setList] = useState<PrizeSkuRelationActivityPageResponseVO[]>([]);
  const [loading, setLoading] = useState(true);


  const fetchList = async () => {
    try {
      setLoading(true);
      const { records } = await prizeGetBindActivityPage({ skuCode: record.skuCode, pageNum: 1, pageSize: 10 });
      setList(records || []);
    } catch (err) {
      Message.error('获取绑定活动异常');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchList().then();
  }, []);

  return (
    <Box spacing={16}>
      <Box direction={'row'}>注：仅展示正在 <span style={{ color: 'var(--primary-color)' }}>【进行中】</span> 的活动</Box>
      <Table
        dataSource={list}
        hasBorder
        fixedHeader
        loading={loading}
        maxBodyHeight={300}
      >
        <Table.Column
          title="活动名称"
          align={'center'}
          dataIndex="activityName"
        />
        <Table.Column
          title="活动类型"
          align={'center'}
          dataIndex="activityType"
        />
        <Table.Column
          title="活动时间"
          align={'center'}
          width={200}
          cell={(_, __, record) => (
            <Box spacing={5}>
              <span>{dayjs(record.startDate).format('YYYY-MM-DD HH:mm:ss')}</span>
              <span>{dayjs(record.endDate).format('YYYY-MM-DD HH:mm:ss')}</span>
            </Box>
          )}
        />
        <Table.Column
          title="冻结资产数"
          align={'center'}
          dataIndex="activityFreezeNum"
        />
      </Table>
    </Box>
  );
}
