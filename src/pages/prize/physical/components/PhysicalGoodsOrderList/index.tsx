import { useState, useEffect } from 'react';
import { Box, Input, Select, Button, Table, Pagination, DatePicker2, Message, Dialog } from '@alifd/next';
import dayjs from 'dayjs';
import { downloadExcel, maskSensitiveInfo, copyText } from '@/utils';
import {
  prizeSkuPrizeOrderDownload,
  prizeSkuPrizeOrderPage,
  prizeSkuPrizeOrderExport,
  prizePrizeSkuDeal,
} from '@/api/vf';
import { PrizeSkuOrderPageResponseVO } from '@/api/types';
import ExcelImport from '@/components/ExcelImport';
import { Auth } from '@/components/Auth';
import usePagination from '@/hooks/usePagination';
import { activityGetActivityType } from '@/api/b';


export default function Index() {
  // 查询条件
  const [sendCode, setSendCode] = useState<string>('');
  const [dealStatus, setDealStatus] = useState<number>(-1);
  const [nickName, setUserNick] = useState<string>('');
  const [openId, setOpenId] = useState<string>('');
  const [prizeName, setPrizeName] = useState<string>('');
  const [activityName, setActivityName] = useState<string>('');
  const [activityType, setActivityType] = useState(-1);
  const [createTimeRange, setCreateTimeRange] = useState<any>([]);
  const [activityTypeList, setActivityTypeList] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // 使用分页Hook
  const pagination = usePagination(1, 10, (current, size) => {
    fetchData({
      pageNum: current,
      pageSize: size,
    });
  });
  const getActivityTypeList = async () => {
    const res = await activityGetActivityType();
    setActivityTypeList(res as any);
  };

  // 列表数据
  const [dataList, setDataList] = useState<PrizeSkuOrderPageResponseVO[]>([]);

  useEffect(() => {
    getActivityTypeList().then();
    fetchData().then();
  }, []);

  const fetchData = async (params?: any) => {
    const [createStartTime, createEndTime] = createTimeRange;
    try {
      setLoading(true);
      const { records, total } = await prizeSkuPrizeOrderPage({
        sendCode: sendCode.trim(),
        dealStatus,
        nickName: nickName.trim(),
        openId: openId.trim(),
        prizeName: prizeName.trim(),
        activityName: activityName.trim(),
        activityType,
        createStartTime: createStartTime ? dayjs(createStartTime).format('YYYY-MM-DD 00:00:00') : '',
        createEndTime: createEndTime ? dayjs(createEndTime).format('YYYY-MM-DD 23:59:59') : '',
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        ...params,
      });
      setDataList(records || []);
      pagination.setTotal(Number(total));
    } catch (err) {
      Message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    pagination.changePage(1);
  };

  const handleReset = async () => {
    setSendCode('');
    setDealStatus(-1);
    setUserNick('');
    setOpenId('');
    setPrizeName('');
    setActivityName('');
    setActivityType(-1);
    setCreateTimeRange([]);
    pagination.reset();
    // 明确传入重置后的查询参数
    fetchData({
      pageNum: 1,
      pageSize: 10,
      sendCode: '',
      dealStatus: -1,
      nickName: '',
      openId: '',
      prizeName: '',
      activityName: '',
      activityType: -1,
    }).then();
  };

  const handleExport = async () => {
    const [createStartTime, createEndTime] = createTimeRange;
    setLoading(true);
    try {
      const data: any = await prizeSkuPrizeOrderExport({
        sendCode: sendCode.trim(),
        dealStatus,
        nickName: nickName.trim(),
        openId: openId.trim(),
        prizeName: prizeName.trim(),
        activityName: activityName.trim(),
        activityType,
        createStartTime: createStartTime ? dayjs(createStartTime).format('YYYY-MM-DD 00:00:00') : '',
        createEndTime: createEndTime ? dayjs(createEndTime).format('YYYY-MM-DD 23:59:59') : '',
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
      });
      downloadExcel(data, `实物奖品订单${dayjs().format('YYYY-MM-DD HH:mm')}`);
    } catch (e) {
      Message.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  // 发货
  const handleSendGood = (record) => {
    const { sendCode } = record;
    Dialog.confirm({
      title: '提示',
      content: '是否确定发货？',
      onOk: async () => {
        try {
          await prizePrizeSkuDeal({
            sendCode,
          });
          await fetchData();
          Message.success('发货成功');
        } catch (e) {
          Message.error(e.message);
        }
      },
    });
  };
  const handleDownTemplate = async () => {
    setLoading(true);
    try {
      const data: any = await prizeSkuPrizeOrderDownload();
      downloadExcel(data, '实物奖品订单导入模板');
    } catch (e) {
      Message.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      spacing={16}
      padding={[0]}
    >
      <div style={{ color: 'var(--primary-color)' }}>
        注：此处仅修改订单发货状态（消费者在C端可查看发货状态），实际发货操作需商家在线下完成。
      </div>

      {/* 筛选 */}
      <Box
        direction="row"
        justify="space-between"
        align="flex-end"
      >
        <Box
          direction="row"
          wrap
          spacing={10}
        >
          <Input
            label="订单编号"
            value={sendCode}
            onChange={(val: string) => setSendCode(val)}
          />
          <Select
            label="发货状态"
            value={dealStatus}
            onChange={(val: number) => setDealStatus(val)}
            dataSource={[
                  { label: '全部', value: -1 },
                  { label: '未发货', value: 0 },
                  { label: '已发货', value: 1 },
                  { label: '已取消', value: 2 },
                ]}
          />
          <Input
            label="用户昵称"
            value={nickName}
            onChange={(val: string) => setUserNick(val)}
          />
          <Input
            label="openId"
            value={openId}
            onChange={(val: string) => setOpenId(val)}
          />
          <Input
            label="奖品名称"
            value={prizeName}
            onChange={(val: string) => setPrizeName(val)}
          />
          <Input
            label="关联活动名称"
            value={activityName}
            onChange={(val: string) => setActivityName(val)}
          />
          <Select
            label="活动类型"
            placeholder="活动类型"
            value={activityType}
            onChange={(val: number) => setActivityType(val)}
            style={{ width: 200 }}
          >
            <Select.Option value={-1}>全部类型</Select.Option>
            {
              activityTypeList?.map((item) => (
                <Select.Option key={item.value} value={item.value}>
                  {item.desc}
                </Select.Option>
              ))
            }
          </Select>
          <DatePicker2.RangePicker
            label="创建时间"
            value={createTimeRange}
            onChange={(val: any) => setCreateTimeRange(val)}
            style={{ width: 260 }}
          />
          <Button
            type="primary"
            onClick={handleSearch}
          >
            查询
          </Button>
          <Button onClick={handleReset}>重置</Button>
          <Auth authKey={'prize_physical_order_export'}>
            <Button
              style={{ margin: 5 }}
              type="secondary"
              onClick={handleExport}
              disabled={loading}
            >
              导出
            </Button>
          </Auth>
        </Box>
        <Box
          direction="row"
          spacing={16}
          align="center"
        >
          <ExcelImport
            action={`${process.env.ICE_BASE_URL}/vf/prize/importSendCodeExcel`}
            buttonText="导入订单批量发货"
            onDownloadTemplate={handleDownTemplate}
            loading={loading}
            onSuccess={async (res) => {
                  console.log(res, 'res');
                  await fetchData();
                }}
          />
        </Box>
      </Box>

      {/* 表格 */}
      <Table
        dataSource={dataList}
        hasBorder
        loading={loading}
      >
        <Table.Column align={'center'} title={'订单编号'} width={180} dataIndex={'sendCode'} />
        <Table.Column
          title="用户昵称"
          align={'center'}
          width={90}
          dataIndex="nickName"
          cell={(value) => maskSensitiveInfo(value, 1, 1)}
        />
        <Table.Column
          title="openId"
          align={'center'}
          width={80}
          dataIndex="openId"
          cell={(value) => maskSensitiveInfo(value, 1, 1)}
        />
        <Table.Column title={'创建时间'} align={'center'} width={160} dataIndex={'createTime'} cell={(value) => dayjs(value).format('YYYY-MM-DD HH:mm:ss')} />
        <Table.Column title={'奖品名称'} align={'center'} dataIndex={'prizeName'} />
        <Table.Column title={'获奖订单号(子单号)'} align={'center'} dataIndex={'awardSendCode'} />
        <Table.Column
          title="收件信息"
          align={'left'}
          width={220}
          cell={(value, index, record) => {
                const { realName, mobile, province, city, county, address } = record;
                const maskedMobile = maskSensitiveInfo(mobile, 3, 4);
                // 准备未加密的完整信息用于复制
                const fullAddressText = `${realName} ${mobile}\n${province}${city}${county} ${address}`;

                return (
                  <Box direction="column" spacing={4}>
                    <div>
                      <strong>{realName}</strong> {maskedMobile}
                      {
                            realName && (
                            <i
                              className="iconfont icon-fuzhi"
                              style={{ marginLeft: '8px', cursor: 'pointer', color: 'var(--primary-color)' }}
                              onClick={() => copyText(fullAddressText)}
                            />
                            )
                        }
                    </div>
                    <div>
                      <span style={{ color: '#333', fontSize: '12px' }}>
                        {province} {city} {county} {address}
                      </span>
                    </div>

                  </Box>
                );
              }}
        />
        <Table.Column
          align={'center'}
          title="关联活动"
          dataIndex="activityName"
        />
        <Table.Column
          align={'center'}
          title="发货状态"
          width={90}
          dataIndex="dealStatusName"
          cell={(value) => {
                if (value === '已发货') {
                  return <span style={{ color: 'var(--primary-color)' }}>{value}</span>;
                } else if (value === '待发货') {
                  return <span>{value}</span>;
                }
                return value;
              }}
        />
        <Table.Column title={'备注'} dataIndex="cancelReason" />
        <Table.Column
          title="操作"
          align={'center'}
          width={80}
          cell={(_value, _index, record) => {
                return (
                  <Button
                    type="primary"
                    text
                    disabled={['已发货', '已取消'].includes(record.dealStatusName)}
                    onClick={() => handleSendGood(record)}
                  >
                    发货
                  </Button>
                );
              }}
        />
      </Table>

      {/* 分页 */}
      <Box direction="row" justify="flex-end">
        <Pagination
          {...pagination.paginationProps}
          shape={'arrow-only'}
          totalRender={total => `共 ${total} 条`}
          pageSizeSelector="dropdown"
          pageSizePosition="end"
        />
      </Box>
    </Box>
  );
}
