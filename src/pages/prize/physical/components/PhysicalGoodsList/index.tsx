import { useState, useEffect } from 'react';
import { Box, Input, Button, Table, Pagination, Message, Dialog } from '@alifd/next';
import createPhysicalGood from '../CreatePhysicalGood/util';
import appendStock from '../AppendStock/util';
import { prizeDeleteSku, prizeGetPrizeSkuPage } from '@/api/vf';
import viewBindingActivities from '../ViewBindingActivities/util';
import choosePhysicalGood from '@/pages/prize/physical/components/ChoosePhysicalGood/choosePhysicalGood';
import { addDialogRef } from '@/utils/dialogMapper';
import usePagination from '@/hooks/usePagination';

interface PhysicalGoodsListProps {
  prizeType?: string;
  isChoosePrize?: boolean;
  onSelectPrize?: (prize: any) => void;
  activityType?: string;
}

export default function PhysicalGoodsList({
  prizeType,
  isChoosePrize = false,
  onSelectPrize,
  activityType }: PhysicalGoodsListProps) {
  // 查询条件
  const [assetName, setAssetName] = useState('');
  const [loading, setLoading] = useState<boolean>(false);

  // 使用分页Hook
  const pagination = usePagination(1, 5, (current, size) => {
    fetchPhysicalGoodsList({
      pageNum: current,
      pageSize: size,
    });
  });

  // 列表数据
  const [physicalGoodsList, setPhysicalGoodsList] = useState<any[]>([]);

  // 初始化或分页更新时加载数据
  useEffect(() => {
    fetchPhysicalGoodsList();
  }, []);

  // 拉取实物列表
  const fetchPhysicalGoodsList = async (params?: any) => {
    setLoading(true);
    try {
      const res = await prizeGetPrizeSkuPage({
        skuName: assetName.trim() || '',
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        ...params,
      });
      const { records = [], total = 0 } = res;
      setPhysicalGoodsList(records);
      pagination.setTotal(+total);
    } catch (err) {
      Message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  // 点击查询
  const handleSearch = () => {
    pagination.changePage(1);
  };

  // 重置
  const handleReset = async () => {
    // 1. 先重置查询项
    setAssetName('');
    // 2. 重置分页状态，但现在不会触发查询
    pagination.reset();
    // 3. 手动调用查询，确保使用重置后的空查询条件
    await fetchPhysicalGoodsList({
      pageNum: 1,
      pageSize: 10,
      skuName: '',
    });
  };

  const handleEditPhysicalGood = async (record: any) => {
    const result = await createPhysicalGood(record);
    if (result) {
      await fetchPhysicalGoodsList();
    }
  };

  // 点击"选择"时，弹出详细编辑弹窗
  const handleChoosePrize = async (record: any) => {
    const result = await choosePhysicalGood({ record, prizeType, activityType } as any);
    if (result) {
      // 用户在弹窗中点击"保存"后返回的表单值
      // 这里可传给父组件, 或在 choosePrize 里接收
      onSelectPrize && onSelectPrize(result);
    }
  };

  // 根据是否是"选择奖品"场景来渲染操作列
  const renderOperationColumn = (value, index, record) => {
    if (isChoosePrize) {
      // 场景：choosePrize 弹窗，只显示"选择"按钮
      return (
        <Button
          type="primary"
          text
          disabled={record.benefitStatus == 3 || record.remainPrizeQuantity * 1 <= 0}
          onClick={() => handleChoosePrize(record)}
        >
          选择
        </Button>
      );
    } else {
      return (<Box
        direction="row"
        spacing={10}
      >
        <Button
          type="primary"
          text
          onClick={() => handleEditPhysicalGood(record)}
        >
          编辑
        </Button>
        <Button
          type="primary"
          text
          onClick={async () => {
            const result = await appendStock(record);
            if (result) {
              // 如果更新库存成功，刷新列表
              fetchPhysicalGoodsList();
            }
          }}
        >
          追加库存
        </Button>
        <Button
          type="primary"
          text
          onClick={() => viewBindingActivities(record)}
        >
          查看绑定活动
        </Button>
        <Button
          type="primary"
          text
          disabled={record.quantityPreDeliver > 0}
          onClick={() => handleDeletePhysicalGood(record)}
        >
          删除
        </Button>
      </Box>);
    }
  };

  const handleDeletePhysicalGood = async (record: any) => {
    const dialogRef = Dialog.confirm({
      title: '提示',
      content: '是否删除当前实物？',
      onOk: async () => {
        try {
          await prizeDeleteSku({
            skuCode: record.skuCode,
          });
          Message.success('删除实物成功');
          await fetchPhysicalGoodsList({ pageNum: 1 });
        } catch (e) {
          Message.error(e.message);
        }
      },
    });
    addDialogRef(dialogRef);
  };

  // 新建实物奖品功能
  const handleCreatePhysicalGood = async () => {
    const result = await createPhysicalGood({});
    if (result) {
      await fetchPhysicalGoodsList();
    }
  };

  return (
    <Box
      spacing={16}
      style={{ height: '100%' }}
    >
      {/* 查询条件 */}
      <Box
        direction="row"
        justify="space-between"
      >
        <Box
          direction="row"
          spacing={10}
        >
          <Input
            label="实物奖品名称"
            placeholder="请输入奖品名称"
            style={{ width: 300 }}
            value={assetName}
            onChange={(val: string) => setAssetName(val)}
          />
          <Button
            type="primary"
            onClick={handleSearch}
          >
            查询
          </Button>
          <Button
            type="normal"
            onClick={handleReset}
          >
            重置
          </Button>
        </Box>
        <Button
          type="primary"
          onClick={handleCreatePhysicalGood}
        >
          新建实物奖品
        </Button>
      </Box>

      {/* 表格 */}
      <Box style={{ flex: 1, overflow: 'auto' }}>
        <Table
          dataSource={physicalGoodsList}
          hasBorder
          loading={loading}
        >
          <Table.Column
            title="奖品主图"
            dataIndex="skuMainPicture"
            cell={(value) => (
              <img
                src={value}
                alt={''}
                style={{ width: 60 }}
              />
            )}
          />
          <Table.Column
            title="实物奖品名称"
            dataIndex="benefitName"
          />
          <Table.Column
            title="奖品类型"
            cell={() => ('实物奖品')}
          />
          <Table.Column
            title="奖品价值"
            dataIndex="amount"
            cell={value => value && Number(value).toFixed(2)}
          />
          <Table.Column
            title="总库存"
            dataIndex="prizeQuantity"
          />
          <Table.Column
            title="冻结库存"
            dataIndex="bindActivityNum"
          />
          <Table.Column
            title="可用库存"
            dataIndex="remainPrizeQuantity"
          />
          <Table.Column
            title="已发放库存"
            dataIndex="hasSendNum"
          />
          <Table.Column
            title="操作"
            cell={renderOperationColumn}
          />
        </Table>
      </Box>
      <Box direction="row" justify="flex-end">
        <Pagination
          {...pagination.paginationProps}
          shape={'arrow-only'}
          totalRender={total => `共${total}条`}
          pageSizeSelector="dropdown"
          pageSizePosition="end"
        />
      </Box>
    </Box>
  );
}
