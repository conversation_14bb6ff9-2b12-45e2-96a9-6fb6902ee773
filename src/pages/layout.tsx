import { Outlet, useLocation } from 'ice';
import Header from '@/components/Header';
import Menu from '@/components/Menu';
import styles from './index.module.scss';

const EmptyLayout = () => {
  return <Outlet />;
};

const BasicLayout = () => {
  return (
    <div className={styles.layoutContainer}>
      <div className={styles.menuContainer}>
        <Menu />
      </div>
      <div className={styles.rightContainer}>
        <Header />
        <div className={styles.contentContainer}>
          <Outlet />
        </div>
      </div>
    </div>
  );
};
const Layout = () => {
  const location = useLocation();
  const emptyLayoutList = ['/login', '/shop'];
  const unusedLayout = emptyLayoutList.includes(location.pathname.replace(/\/$/, ''));
  return unusedLayout ? <EmptyLayout /> : <BasicLayout />;
};

export default Layout;
