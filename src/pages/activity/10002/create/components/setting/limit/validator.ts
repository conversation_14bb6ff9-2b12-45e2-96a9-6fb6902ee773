import { AppState } from '@/pages/activity/10002/create/type';

/**
 * 验证中奖限制
 * @param state 中奖限制数据
 * @returns 错误信息数组
 */
export function validateLimit(state: AppState): string[] {
    const errors: string[] = [];
    const { limit } = state;

    // 校验每人每天中奖次数是否小于等于每人累计中奖次数
    if (limit.limitType === 1 && limit.totalLimitType === 1) {
        if (limit.limitCount > limit.totalLimitCount) {
            errors.push('【每人每天中奖次数】需小于等于【每人累计中奖次数】');
        }
    }

    return errors;
}

