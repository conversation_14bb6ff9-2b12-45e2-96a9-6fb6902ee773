import { useActivity } from '@/pages/activity/10002/create/reducer';
import { validateLimit } from './validator';
import { LimitState, ModuleType } from '@/pages/activity/10002/create/type';


/**
 * 中奖限制模块的自定义Hook
 * 提供对中奖限制状态的访问和更新方法
 */
export function useLimit() {
  const { state, dispatch } = useActivity();

  // 更新中奖限制并自动重新验证
  const updateLimit = (data?: Partial<LimitState>) => {
    if (!data) return;
    // 先更新数据
    dispatch({
      type: 'UPDATE_LIMIT',
      payload: data,
    });

    // 获取更新后的数据
    const updatedStateInfo = {
      ...state,
      limit: {
        ...state.limit,
        ...data,
      },
    };

    // 重新验证并更新错误状态
    const errors = validateLimit(updatedStateInfo);
    dispatch({
      type: 'VALIDATE_MODULE',
      module: ModuleType.LIMIT,
      payload: errors,
    });
  };

  return {
    limit: state.limit,
    errors: state.errors[ModuleType.LIMIT] || [],
    updateLimit,
    dispatch,
  };
}
