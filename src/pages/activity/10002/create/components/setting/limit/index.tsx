import Container from '@/components/Container';
import { useLimit } from './hook';
import { Form, Radio } from '@alifd/next';
import NumberInput from '@/components/NumberInput/NumberInput';
import HelpTooltip from '@/components/HelpTooltip/HelpTooltip';

export default function Limit() {
  const { limit, errors, updateLimit } = useLimit();

  const hasLimitError = errors.some(err => err.includes('中奖次数'));
  return (
    <Container title="中奖限制" style={{ marginBottom: 10 }}>
      <Form.Item
        label="每人每天中奖限制"
        required
      >
        <Radio.Group
          value={limit.limitType}
          onChange={(value: number) => updateLimit({ limitType: value, limitCount: 1 })}
        >
          <Radio value={1}>限制</Radio>
          <Radio value={2}>不限制</Radio>
        </Radio.Group>
      </Form.Item>
      {
        limit.limitType === 1 &&
        <Form.Item
          label=" "
          validateState={hasLimitError ? 'error' : undefined}
          help={hasLimitError ? errors.find(err => err.includes('中奖次数')) : undefined}
        >
          用户每人每天最多中奖
          <NumberInput
            style={{ margin: '0 10px' }}
            min={1}
            max={99}
            value={limit.limitCount}
            onChange={(value: number) => updateLimit({ limitCount: value })}
          />
          次
        </Form.Item>
      }
      <Form.Item label="每人累计中奖限制" required>
        <Radio.Group
          value={limit.totalLimitType}
          onChange={(value: number) => updateLimit({ totalLimitType: value, totalLimitCount: 1 })}
        >
          <Radio value={1}>限制</Radio>
          <Radio value={2}>不限制</Radio>
        </Radio.Group>
      </Form.Item>
      {
        limit.totalLimitType === 1 && (
          <Form.Item label=" ">
            用户活动期间内累计最多中奖
            <NumberInput
              style={{ margin: '0 10px' }}
              min={1}
              max={99}
              value={limit.totalLimitCount}
              onChange={(value: number) => updateLimit({ totalLimitCount: value })}
            />
            次
          </Form.Item>
        )
      }

      <Form.Item label="开启获奖名单" required>
        <Radio.Group value={limit.openAwardList} onChange={(value: number) => updateLimit({ openAwardList: value })}>
          <Radio value={2}>不开启</Radio>
          <Radio value={1}>开启 <HelpTooltip content={'开启后C端将显示中奖名单轮播'} /></Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item label="奖品全部发完可继续抽奖" required>
        <Radio.Group value={limit.continueDraw} onChange={(value: number) => updateLimit({ continueDraw: value })}>
          <Radio value={2}>不开启  </Radio>
          <Radio value={1} >开启 <HelpTooltip content={'开启后用户依旧可抽奖（消耗抽奖次数），但无法中奖'} /></Radio>
        </Radio.Group>
      </Form.Item>
    </Container>
  );
}