import Container from '@/components/Container';
import { usePrize } from './hooks';
import { Box, Form } from '@alifd/next';
import PrizeWheelTable from '@/components/PrizeWheelTable';
import { useEffect, useState } from 'react';
import { useActivity } from '../../../reducer';

export default function Prize() {
  const { prize, updatePrize, errors } = usePrize();
  const { state } = useActivity();
  const { activityStatus, operationType } = state.extra;

  const [totalProbability, setTotalProbability] = useState(0);

  const hasProbabilityError = errors.some(err => err.includes('总概率'));
  const hasPrizeError = errors.some(err => err.includes('请至少'));

  useEffect(() => {
    // 奖品总概率计算，支持5位小数的精确计算
    const total = prize.reduce((acc, curr) => {
      // 确保是数字类型，处理无效值为0
      const probabilityValue = parseFloat(curr.probability) || 0;
      return parseFloat((acc + probabilityValue).toPrecision(10));
    }, 0);

    setTotalProbability(total);
  }, [prize]);

  return (
    <Container title="奖品设置">
      <Form.Item
        label="用户单次抽中奖品总概率"
        validateState={hasProbabilityError ? 'error' : undefined}
        help={hasProbabilityError ? errors.find(err => err.includes('总概率')) : undefined}
      >
        <Box margin={[6, 0, 0, 0]}>{totalProbability}%</Box>
      </Form.Item>
      <Form.Item
        label="奖品列表"
        required
        validateState={hasPrizeError ? 'error' : undefined}
        help={hasPrizeError ? errors.find(err => err.includes('请至少')) : undefined}
      >
        <Box margin={[6, 0, 0, 0]}>
          <PrizeWheelTable
            disabledTabs={[6]}
            status={[activityStatus, operationType]}
            prizeList={prize}
            onPrizeChange={(newPrizeList) => updatePrize(newPrizeList)}
          />
          <div className="form-extra">注：虚拟奖项一旦开奖自动发放，实物奖项需要用户在中奖后主动领取（需填写收货地址）</div>
        </Box>
      </Form.Item>
    </Container>
  );
}
