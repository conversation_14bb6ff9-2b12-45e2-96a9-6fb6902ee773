import { AppState } from '@/pages/activity/10002/create/type';
import { PrizeTypeEnum } from '@/utils';

/**
 * 验证奖品设置
 * @param state 奖品设置数据
 * @returns 错误信息数组
 */
export function validatePrize(state: AppState): string[] {
    const errors: string[] = [];
    const { prize } = state;

    const validPrize = prize.filter(item => item.lotteryType !== PrizeTypeEnum.THANKS.value);
    if (validPrize.length === 0) {
      errors.push('请至少添加一个奖品');
    }
    if (validPrize.length === 8) {
      errors.push('请至少保留一个谢谢参与');
    }
    const totalProbability = prize.reduce((acc, curr) => acc + curr.probability, 0);
    if (totalProbability > 99.999) {
        errors.push('总概率不能超过99.999%, 请重新调整奖品中奖概率');
    }
    return errors;
}

