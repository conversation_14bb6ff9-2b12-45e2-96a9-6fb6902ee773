import { useActivity } from '@/pages/activity/10002/create/reducer';
import { validatePrize } from './validator';
import { PrizeState, ModuleType } from '@/pages/activity/10002/create/type';


/**
 * 奖品设置模块的自定义Hook
 * 提供对奖品设置状态的访问和更新方法
 */
export function usePrize() {
  const { state, dispatch } = useActivity();

  // 更新抽奖机会并自动重新验证
  const updatePrize = (data?: Partial<PrizeState>) => {
    if (!data) return;
    // 先更新数据
    dispatch({
      type: 'UPDATE_PRIZE',
      payload: data,
    });

    // 获取更新后的数据
    const updatedStateInfo = {
      ...state,
      prize: data,
    };

    // 重新验证并更新错误状态
    const errors = validatePrize(updatedStateInfo);
    dispatch({
      type: 'VALIDATE_MODULE',
      module: ModuleType.PRIZE,
      payload: errors,
    });
  };

  return {
    prize: state.prize,
    errors: state.errors[ModuleType.PRIZE] || [],
    updatePrize,
    dispatch,
  };
}
