import Container from '@/components/Container';
import { useThreshold } from './hooks';
import { Button, Form, Radio } from '@alifd/next';
import openThresholdDialog from '@/components/Threshold/util';
import openCrowdDialog from '@/components/Crowd/util';
import ThresholdDesc from '@/components/Threshold/ThresholdDesc';
import CrowdDesc from '@/components/Crowd/CrowdDesc';
import { ACTIVITY_STATUS } from '@/utils/constant';
import { useActivity } from '../../../reducer';

export default function Threshold() {
  const { threshold, updateThreshold, errors } = useThreshold();
  const { state } = useActivity();
  const { thresholdType, thresholdInfo } = threshold;
  const { operationType, activityStatus } = state.extra;

  const isEdit = operationType === 'edit';
  const isView = operationType === 'view';
  const notStart = activityStatus !== ACTIVITY_STATUS.NOT_STARTED;
  const needDisable = (isEdit || isView) && notStart;

  /**
   * 切换活动门槛类型
   * @param value 活动门槛类型
   */
  const handleChange = (value: number) => {
    updateThreshold({ thresholdType: value, thresholdInfo: {} });
  };

  /**
   * 添加条件人群
   */
  const addConditionCrowd = async () => {
    const result = await openThresholdDialog();
    updateThreshold({ thresholdInfo: result });
  };

  /**
   * 添加CDP人群
   */
  const addCdpCrowd = async () => {
    const result = await openCrowdDialog();
    updateThreshold({ thresholdInfo: result });
  };

  /**
   * 编辑条件人群
   */
  const editConditionCrowd = async () => {
    const result = await openThresholdDialog(thresholdInfo);
    updateThreshold({ thresholdInfo: result });
  };

  /**
   * 编辑CDP人群
   */
  const editCdpCrowd = async () => {
    const result = await openCrowdDialog(thresholdInfo);
    updateThreshold({ thresholdInfo: result });
  };

  /**
   * 是否存在活动门槛错误
   */
  const isThresholdError = errors.find(error => error.includes('人群'));

  return (
    <Container title="活动门槛">
      <Form.Item label="参与人群" required>
        <Radio.Group value={thresholdType} onChange={handleChange}>
          <Radio value={1}>全部用户</Radio>
          <Radio value={2}>指定人群</Radio>
          <Radio value={3}>选择CDP人群</Radio>
          <Radio value={4}>排除CDP人群</Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item label=" " x-if={thresholdType !== 1 && !thresholdInfo.crowdName} validateState={isThresholdError ? 'error' : 'success'} help={isThresholdError ? errors.find(error => error.includes('人群')) : ''}>
        <Button x-if={thresholdType === 2} onClick={addConditionCrowd}>+ 添加条件人群</Button>
        <Button x-if={thresholdType === 3} onClick={addCdpCrowd}>+ 导入CDP人群</Button>
        <Button x-if={thresholdType === 4} onClick={addCdpCrowd}>+ 排除CDP人群</Button>
      </Form.Item>
      <Form.Item label=" " x-if={(thresholdType === 4 || thresholdType === 3) && thresholdInfo.crowdName}>
        <CrowdDesc crowdInfo={thresholdInfo} onEdit={editCdpCrowd} />
      </Form.Item>
      <Form.Item label=" " x-if={(thresholdType === 2) && thresholdInfo.crowdName}>
        <ThresholdDesc thresholdInfo={thresholdInfo} onEdit={editConditionCrowd} disabled={needDisable} />
      </Form.Item>
    </Container>
  );
}