import { AppState } from '@/pages/activity/10002/create/type';

/**
 * 验证门槛
 * @param state 门槛数据
 * @returns 错误信息数组
 */
export function validateThreshold(state: AppState): string[] {
    const errors: string[] = [];
    const { threshold } = state;
    const { thresholdType, thresholdInfo } = threshold;
    if (thresholdType !== 1 && !thresholdInfo.crowdName) {
        errors.push('请设置人群条件');
    }
    return errors;
}

