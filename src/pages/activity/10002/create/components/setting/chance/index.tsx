import Container from '@/components/Container';
import NumberInput from '@/components/NumberInput/NumberInput';
import { openTaskDialog, TaskTypeLabel } from '@/components/Task/util';
import TaskTable from '@/components/TaskTable';
import { Button, Form, Radio } from '@alifd/next';
import { useChance } from './hooks';
import { useActivity } from '../../../reducer';
import { ACTIVITY_STATUS } from '@/utils/constant';

export default function Chance() {
  const { chance, updateChance, errors } = useChance();
  const { chanceType, freeChance, taskChance, taskList } = chance;

  const { state } = useActivity();
  const { operationType, activityStatus } = state.extra;

  const isEdit = operationType === 'edit';
  const isView = operationType === 'view';
  const unStart = activityStatus !== ACTIVITY_STATUS.NOT_STARTED;

  const needDisable = (isEdit || isView) && unStart;

  const taskApply = Object.keys(TaskTypeLabel).map(Number);

  const addTask = async () => {
    const result = await openTaskDialog({ taskList, apply: taskApply });
    updateChance({ taskList: [...taskList, result] });
  };

  const handleTaskListChange = (newTaskList: any[]) => {
    updateChance({ taskList: newTaskList });
  };

  const hasTaskError = errors.find(error => error.includes('任务'));
  return (
    <Container title="抽奖机会">
      <Form.Item label="免费赠送" required>
        <Radio.Group
          value={chanceType}
          onChange={(value: number) => updateChance({
            chanceType: value,
            freeChance: value === 1 ? 0 : 1,
          }, false)}
        >
          <Radio value={1}>首次访问活动赠送</Radio>
          <Radio value={2}>每人每天访问活动赠送</Radio>
        </Radio.Group>
        <br />
        <NumberInput
          min={chanceType === 1 ? 0 : 1}
          max={99}
          style={{ width: 280, marginTop: 10, marginRight: 4 }}
          value={freeChance}
          onChange={(value: number) => updateChance({ freeChance: value })}
        />次
      </Form.Item>
      <Form.Item label="任务获取" required>
        <Radio.Group value={taskChance} onChange={(value: number) => updateChance({ taskChance: value })}>
          <Radio value={1}>设置</Radio>
          <Radio value={2}>不设置</Radio>
        </Radio.Group>
        <Button
          x-if={taskChance === 1}
          disabled={taskList.length >= taskApply.length}
          style={{ width: 100, float: 'right' }}
          type={'primary'}
          onClick={addTask}
        >添加任务({taskList.length}/{taskApply.length})</Button>
      </Form.Item>
      <Form.Item
        x-if={taskChance === 1}
        label=" "
        validateState={hasTaskError ? 'error' : undefined}
        help={hasTaskError ? errors.find(error => error.includes('任务')) : undefined}
      >
        <TaskTable
          disabled={needDisable}
          dataSource={taskList}
          onChange={handleTaskListChange}
        />
      </Form.Item>
    </Container>
  );
}
