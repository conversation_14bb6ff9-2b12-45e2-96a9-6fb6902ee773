import { AppState } from '@/pages/activity/10002/create/type';

/**
 * 验证抽奖机会
 * @param state 抽奖机会数据
 * @returns 错误信息数组
 */
export function validateChance(state: AppState): string[] {
    const errors: string[] = [];
    const { chance } = state;
    const { taskChance, taskList } = chance;
    if (taskChance === 1 && taskList.length === 0) {
        errors.push('请至少添加一个任务');
    }
    return errors;
}

