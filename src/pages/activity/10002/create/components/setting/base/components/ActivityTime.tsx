import { useEffect, useState } from 'react';
import { DatePicker2, Form, Box, Radio } from '@alifd/next';
import dayjs from 'dayjs';
import { getExpireDay, timeDiff } from '@/utils';
import { useBaseInfo } from '../hooks';
import { ExtraState } from '@/pages/activity/10002/create/type';
import { ACTIVITY_STATUS } from '@/utils/constant';


export default function ActivityTime({ operationType, activityStatus }: Pick<ExtraState, 'operationType' | 'activityStatus'>) {
  const { baseInfo, updateBaseInfo, errors } = useBaseInfo();
  const [selectedDays, setSelectedDays] = useState<any>(null);
  const expireDay = getExpireDay();
  const todayStart = dayjs().startOf('day');
  const isView = operationType === 'view';
  const isEdit = operationType === 'edit';
  const isCreate = operationType === 'add';
  const unStart = activityStatus !== ACTIVITY_STATUS.NOT_STARTED;

  useEffect(() => {
    const diffDays = dayjs(baseInfo.endTime).diff(dayjs(baseInfo.startTime), 'day');
    setSelectedDays(diffDays);

    if (isCreate) {
      // 若是创建模式且有效期>=15天，则填默认15天；
      if (expireDay < 15) {
        updateBaseInfo({
          endTime: dayjs(baseInfo.startTime).add(expireDay, 'day').format('YYYY-MM-DD HH:mm:ss'),
        });
      }
    }
  }, []);

  // 手动选择日期区间
  const handleDateChange = value => {
    if (value && value.length === 2) {
      const diffDays = dayjs(value[1]).diff(value[0], 'day');
      setSelectedDays(diffDays);

      updateBaseInfo({
        startTime: dayjs(value[0]).format('YYYY-MM-DD HH:mm:ss'),
        endTime: dayjs(value[1]).format('YYYY-MM-DD HH:mm:ss'),
      });
    }
  };

  // 快捷天数按钮点击
  const handleRadioChange = (value: number) => {
    setSelectedDays(value);
    // 更新结束时间
    updateBaseInfo({
      endTime: dayjs(baseInfo.startTime).add(value, 'day').format('YYYY-MM-DD HH:mm:ss'),
    });
  };

  // 计算活动持续时间显示
  const durationStr = timeDiff(baseInfo.startTime, baseInfo.endTime);

  // 检查是否有与时间相关的错误
  const hasTimeError = errors.some(err => err.includes('时间'));
  return (
    <>
      <Form.Item
        name="AF_baseInfo_activityTime"
        label="活动时间"
        extra={<p className="form-extra">注：活动结束时间不可超过订购有效期</p>}
        required
        requiredMessage="请选择活动时间"
        style={{ marginBottom: 0 }}
        validateState={hasTimeError ? 'error' : undefined}
        help={hasTimeError ? errors.find(err => err.includes('时间')) : undefined}
        disabled={false}
      >
        <DatePicker2.RangePicker
          showTime
          value={baseInfo.startTime && baseInfo.endTime ? [baseInfo.startTime, baseInfo.endTime] : undefined}
          onChange={handleDateChange}
          hasClear={false}
          format="YYYY-MM-DD HH:mm:ss"
          outputFormat="YYYY-MM-DD HH:mm:ss"
          timePanelProps={{ defaultValue: ['00:00:00', '23:59:59'] }}
          disabledDate={date => {
            return date.isBefore(todayStart) || date.isAfter(dayjs().add(expireDay, 'day'));
          }}
          style={{ width: 'var(--activity-form-item-width)' }}
          disabled={[(isEdit || isView) && unStart, isView]}
        />
        <Box
          style={{ display: 'inline-flex' }}
          padding={[0, 15]}
        >
          {`活动持续时间 ${baseInfo.startTime && baseInfo.endTime ? durationStr : ''}`}
        </Box>
      </Form.Item>
      <Form.Item
        label=" "
        disabled={isView}
      >
        <Radio.Group
          shape="button"
          onChange={handleRadioChange}
          value={selectedDays}
        >
          {[15, 30, 45, 90].map(day => (
            <Radio
              key={day}
              value={day}
              disabled={day > expireDay}
            >
              {`${day}天`}
            </Radio>
            ))}
        </Radio.Group>
      </Form.Item>
    </>
  );
}
