import { Form, Input } from '@alifd/next';
import { useBaseInfo } from '../hooks';

/**
 * 活动名称
 * 绑定到 baseInfo.activityName 字段
 */
export default function ActivityName({ operationType }: { operationType: string }) {
  const { baseInfo, updateBaseInfo, errors } = useBaseInfo();

  const handleChange = (value: string) => {
    updateBaseInfo({ activityName: value });
  };
  // 检查是否有与活动名称相关的错误
  const hasNameError = errors.some(err => err.includes('活动名称'));

  return (
    <Form.Item
      name="activityName"
      label="活动名称"
      extra={<p className="form-extra">注：将作为小程序名称，展示给消费者</p>}
      required
      requiredMessage={'活动名称不能为空'}
      pattern="^[\u4E00-\u9FA5A-Za-z0-9_\-/.]+$"
      patternMessage="不可输入特殊符号，支持中文、英文、数字及下划线"
      validateState={hasNameError ? 'error' : undefined}
      help={hasNameError ? errors.find(err => err.includes('活动名称')) : undefined}
      disabled={operationType === 'view'}
    >
      <Input
        maxLength={16}
        showLimitHint
        trim
        placeholder="请输入活动名称"
        composition
        style={{ width: 'var(--activity-form-item-width)' }}
        value={baseInfo.activityName}
        onChange={handleChange}
      />
    </Form.Item>
  );
}
