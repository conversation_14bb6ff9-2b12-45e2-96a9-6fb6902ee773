import { useActivity } from '@/pages/activity/10002/create/reducer';
import { ModuleType } from '@/pages/activity/10002/create/type';


/**
 * 活动规则模块的自定义Hook
 * 提供对活动规则状态的访问和更新方法
 */
export function useRule() {
  const { state, dispatch } = useActivity();

  // 更新活动规则并自动重新验证
  const updateRule = (data: string) => {
    // 先更新数据
    dispatch({
      type: 'UPDATE_RULE',
      payload: data,
    });
  };

  return {
    rule: state.rule,
    errors: state.errors[ModuleType.RULE] || [],
    updateRule,
    dispatch,
  };
}
