import { useActivity } from '../../reducer';
import { useBaseInfo } from '../setting/base/hooks';
import BlindBox from './blindBox';
import TurnTable from './turnTable';

export default function Decorate({ goNextStep, goPrevStep }) {
  const { baseInfo } = useBaseInfo();
  const { wheelType } = baseInfo;

  const { state } = useActivity();
  const { operationType } = state.extra;

  const isView = operationType === 'view';

  const needDisable = isView;

  const props = {
    goNextStep,
    goPrevStep,
    needDisable,
  };
  return (
    <div>
      <TurnTable x-if={wheelType === 1} {...props} />
      <BlindBox x-else {...props} />
    </div>
  );
}