import styles from '../index.module.scss';

interface AwardSectionProps {
  main: any;
}

export default function AwardSection({ main }: AwardSectionProps) {
  const mockAwards = new Array(4).fill(0);

  return (
    <div className={styles.award}>
      <img src={main.awardTitle} className={styles.awardTitle} alt="" />
      <div className={styles.awardContainer}>
        <img src={main.awardBg} className={styles.awardBg} alt="" />
        <div className={styles.awardContent}>
          {mockAwards.map((_, index) => (
            <div key={index} className={styles.awardContentItem}>
              <div className={styles.awardContentItemLeft}>
                <div className={styles.avatar} />
                <div>j***m</div>
              </div>
              <div className={styles.awardContentItemRight}>
                实物奖品
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}