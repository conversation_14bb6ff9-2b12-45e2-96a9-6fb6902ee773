import ImgUpload from '@/components/ImgUpload';
import { Box } from '@alifd/next';
import { MainState } from '../../../../type';
import ColorPickerFormItem from '@/components/ColorPickerFormItem/ColorPickerFormItem';

export default function Main(props) {
  const { tState, tDispatch, needDisable } = props;
  const { main } = tState;
  const {
    kv,
    ruleBtnText,
    ruleBtnBg,
    pageBg,
    remainText,
    remainCount,
    chanceBtn,
    awardBg,
    exposeTitle,
    awardTitle,
    prizeViewTitle,
    boxBg,
    boxIcon1,
    boxIcon2,
    boxIcon3,
    boxBtnUnselected,
    boxBtnSelected,
    boxBtnOpened,
    boxBtnBg,
    boxBtnText,
    exchangeBtnBg,
    exchangeBtnText,
    boxSelectedBg,
  } = main;

  const handleUpdateField = (field: keyof MainState, value: any) => {
    tDispatch({ type: 'UPDATE_MODULE', payload: { [field]: value } });
  };

  return (
    <Box direction="row" spacing={32}>
      <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>
        <ImgUpload
          label="活动KV图"
          required
          disabled={needDisable}
          img={{
            value: kv,
            width: 750,
          }}
          onSuccess={(url: string) => handleUpdateField('kv', url)}
          onReset={() => handleUpdateField('kv', -1)}
        />
        <ColorPickerFormItem
          label="规则按钮字色"
          disabled={needDisable}
          color={{ value: ruleBtnText }}
          onSetColor={(color: string) => handleUpdateField('ruleBtnText', color)}
          onReset={() => handleUpdateField('ruleBtnText', -1)}
        />
        <ColorPickerFormItem
          label="规则按钮背景色"
          disabled={needDisable}
          color={{ value: ruleBtnBg }}
          onSetColor={(color: string) => handleUpdateField('ruleBtnBg', color)}
          onReset={() => handleUpdateField('ruleBtnBg', -1)}
        />
        <ColorPickerFormItem
          label="页面背景色"
          disabled={needDisable}
          color={{ value: pageBg }}
          onSetColor={(color: string) => handleUpdateField('pageBg', color)}
          onReset={() => handleUpdateField('pageBg', -1)}
        />
        <ColorPickerFormItem
          label="剩余机会字色"
          disabled={needDisable}
          color={{ value: remainText }}
          onSetColor={(color: string) => handleUpdateField('remainText', color)}
          onReset={() => handleUpdateField('remainText', -1)}
        />
        <ColorPickerFormItem
          label="剩余机会数值"
          disabled={needDisable}
          color={{ value: remainCount }}
          onSetColor={(color: string) => handleUpdateField('remainCount', color)}
          onReset={() => handleUpdateField('remainCount', -1)}
        />
        <ImgUpload
          label="获取更多抽奖机会-按钮"
          required
          disabled={needDisable}
          img={{
            value: chanceBtn,
            width: 700,
            height: 100,
          }}
          onSuccess={(url: string) => handleUpdateField('chanceBtn', url)}
          onReset={() => handleUpdateField('chanceBtn', -1)}
        />
        <ImgUpload
          label="奖品一览标题"
          required
          disabled={needDisable}
          img={{
            value: prizeViewTitle,
            height: 55,
          }}
          onSuccess={(url: string) => handleUpdateField('prizeViewTitle', url)}
          onReset={() => handleUpdateField('prizeViewTitle', -1)}
        />
      </Box>
      <span />
      <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>
        <ImgUpload
          label="盲盒背景"
          required
          disabled={needDisable}
          img={{
            value: boxBg,
            width: 700,
            height: 1040,
          }}
          onSuccess={(url: string) => handleUpdateField('boxBg', url)}
          onReset={() => handleUpdateField('boxBg', -1)}
        />
        <ImgUpload
          label="盲盒图标（最多3中，随机分配）"
          required
          disabled={needDisable}
          img={{
            value: boxIcon1,
            width: 205,
            height: 205,
          }}
          onSuccess={(url: string) => handleUpdateField('boxIcon1', url)}
          onReset={() => handleUpdateField('boxIcon1', -1)}
        />
        <ImgUpload
          disabled={needDisable}
          img={{
            value: boxIcon2,
            width: 205,
            height: 205,
          }}
          onSuccess={(url: string) => handleUpdateField('boxIcon2', url)}
          onReset={() => handleUpdateField('boxIcon2', -1)}
        />
        <ImgUpload
          disabled={needDisable}
          img={{
            value: boxIcon3,
            width: 205,
            height: 205,
          }}
          onSuccess={(url: string) => handleUpdateField('boxIcon3', url)}
          onReset={() => handleUpdateField('boxIcon3', -1)}
        />
        <ColorPickerFormItem
          label="拆盲盒-按钮背景"
          disabled={needDisable}
          color={{ value: boxBtnBg }}
          onSetColor={(color: string) => handleUpdateField('boxBtnBg', color)}
          onReset={() => handleUpdateField('boxBtnBg', -1)}
        />
        <ColorPickerFormItem
          label="拆盲盒-按钮文字"
          disabled={needDisable}
          color={{ value: boxBtnText }}
          onSetColor={(color: string) => handleUpdateField('boxBtnText', color)}
          onReset={() => handleUpdateField('boxBtnText', -1)}
        />
        <ColorPickerFormItem
          label="换一批-按钮背景"
          disabled={needDisable}
          color={{ value: exchangeBtnBg }}
          onSetColor={(color: string) => handleUpdateField('exchangeBtnBg', color)}
          onReset={() => handleUpdateField('exchangeBtnBg', -1)}
        />
        <ColorPickerFormItem
          label="换一批-按钮文字"
          disabled={needDisable}
          color={{ value: exchangeBtnText }}
          onSetColor={(color: string) => handleUpdateField('exchangeBtnText', color)}
          onReset={() => handleUpdateField('exchangeBtnText', -1)}
        />
        <ColorPickerFormItem
          label="盲盒选中高亮色"
          disabled={needDisable}
          color={{ value: boxSelectedBg }}
          onSetColor={(color: string) => handleUpdateField('boxSelectedBg', color)}
          onReset={() => handleUpdateField('boxSelectedBg', -1)}
        />
      </Box>
      <span />
      <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>
        <ImgUpload
          label="盲盒按钮-未选择"
          required
          disabled={needDisable}
          img={{
            value: boxBtnUnselected,
            width: 156,
            height: 44,
          }}
          onSuccess={(url: string) => handleUpdateField('boxBtnUnselected', url)}
          onReset={() => handleUpdateField('boxBtnUnselected', -1)}
        />

        <ImgUpload
          label="盲盒按钮-已选择"
          required
          disabled={needDisable}
          img={{
            value: boxBtnSelected,
            width: 156,
            height: 44,
          }}
          onSuccess={(url: string) => handleUpdateField('boxBtnSelected', url)}
          onReset={() => handleUpdateField('boxBtnSelected', -1)}
        />
        <ImgUpload
          label="盲盒按钮-已拆盒"
          required
          disabled={needDisable}
          img={{
            value: boxBtnOpened,
            width: 156,
            height: 44,
          }}
          onSuccess={(url: string) => handleUpdateField('boxBtnOpened', url)}
          onReset={() => handleUpdateField('boxBtnOpened', -1)}
        />
        <ImgUpload
          label="活动推荐商品标题"
          required
          disabled={needDisable}
          img={{
            value: exposeTitle,
            height: 55,
          }}
          onSuccess={(url: string) => handleUpdateField('exposeTitle', url)}
          onReset={() => handleUpdateField('exposeTitle', -1)}
        />
        <ImgUpload
          label="获奖名单背景"
          required
          disabled={needDisable}
          img={{
            value: awardBg,
            width: 700,
            height: 425,
          }}
          onSuccess={(url: string) => handleUpdateField('awardBg', url)}
          onReset={() => handleUpdateField('awardBg', -1)}
        />
        <ImgUpload
          label="获奖名单标题"
          required
          disabled={needDisable}
          img={{
            value: awardTitle,
            height: 55,
          }}
          onSuccess={(url: string) => handleUpdateField('awardTitle', url)}
          onReset={() => handleUpdateField('awardTitle', -1)}
        />

      </Box>
    </Box>
  );
}