export interface ThemeState {
  current: 'main' | 'popup';
  main: MainState;
  popup: PopupState;
  card: CardState;
}

export interface CardState {
  cardBg: string;
  countDownColor: string;
}

export interface MainState {
  // 活动kv
  kv: string;
  // 规则按钮文字色
  ruleBtnText: string;
  // 规则按钮背景色
  ruleBtnBg: string;
  // 页面背景图
  pageBg: string;
  // 剩余次数文字色
  remainText: string;
  // 剩余次数数量色
  remainCount: string;
  // 抽奖按钮文字图
  chanceBtn: string;
  // 获奖名单背景图
  awardBg: string;
  // 推荐商品标题图
  exposeTitle: string;
  // 获奖名单标题图
  awardTitle: string;

  // 奖品一览标题
  prizeViewTitle: string;
  // 盲盒背景
  boxBg: string;
  // 盲盒图标
  boxIcon1: string;
  boxIcon2: string;
  boxIcon3: string;
  // 盲盒按钮-未选择
  boxBtnUnselected: string;
  // 盲盒按钮-已选择
  boxBtnSelected: string;
  // 盲盒按钮-已拆盒
  boxBtnOpened: string;
  // 拆盲盒-按钮背景
  boxBtnBg: string;
  // 拆盲盒-按钮文字
  boxBtnText: string;
  // 换一批-按钮背景
  exchangeBtnBg: string;
  // 换一批-按钮字色
  exchangeBtnText: string;
  // 盲盒选中高亮色
  boxSelectedBg: string;
}

export interface PopupState {
  // 弹窗背景图
  popupBg: string;
  // 弹窗按钮背景色
  popupBtnBg: string;
  // 弹窗按钮文字色
  popupBtnText: string;

  // 浮动弹窗背景图
  floatBg: string;
  // 浮动弹窗顶部标题
  floatTitle: string;
  // 浮层内容主标题
  floatConentTitle: string;
  // 浮层内容说明文案
  floatConentText: string;

  // 子任务背景图
  taskBg: string;
  // 引导按钮背景
  taskGuideBtnBg: string;
  // 引导按钮文字色
  taskGuideText: string;

  // 领取记录背景图
  receiveBg: string;
  // 收货地址背景色
  addressBg: string;
  // 收货地址文字色
  addressText: string;
}

type ThemeActionType = 'UPDATE_MODULE' | 'INIT_MODULE' | 'SET_CURRENT_MODULE' | 'UPDATE_THEME';

// 动作类型定义
export type UpdateModuleAction = {
  type: ThemeActionType;
  module?: 'main' | 'popup';
  payload?: Partial<ThemeState>;
};

export type ThemeAction = UpdateModuleAction;