import { useState } from 'react';

interface BoxItem {
  icon: string;
  status: string;
}

export const useBoxList = (icons: string[]) => {
  const getInitBoxList = (): BoxItem[] => {
    const arr = Array.from({ length: 9 }, () => {
      const iconIndex = Math.floor(Math.random() * 3);
      return {
        icon: icons[iconIndex],
        status: 'normal',
      };
    });
    const openedIndex = Math.floor(Math.random() * 9);
    let selectedIndex;
    do {
      selectedIndex = Math.floor(Math.random() * 9);
    } while (selectedIndex === openedIndex);
    arr[openedIndex].status = 'opened';
    arr[selectedIndex].status = 'selected';
    return arr;
  };

  const [boxList, setBoxList] = useState<BoxItem[]>(getInitBoxList);

  const handleBoxClick = (idx: number) => {
    setBoxList((prev) => {
      if (prev[idx].status === 'opened') return prev;
      return prev.map((box, i) => {
        if (i === idx) {
          return { ...box, status: 'selected' };
        }
        if (box.status === 'opened') return box;
        return { ...box, status: 'normal' };
      });
    });
  };

  return {
    boxList,
    handleBoxClick,
  };
};