import styles from '../index.module.scss';
import { PrizeTypeEnum } from '@/utils';

interface PrizeItem {
  lotteryType: number;
  showImage: string;
  lotteryName: string;
}

interface PrizeViewSectionProps {
  main: any;
  prize: PrizeItem[];
}

export default function PrizeViewSection({ main, prize }: PrizeViewSectionProps) {
  const filteredPrizes = prize.filter((item) => item.lotteryType != PrizeTypeEnum.THANKS.value);

  return (
    <div className={styles.prizeView}>
      <img className={styles.prizeViewTitle} src={main.prizeViewTitle} alt="" />
      <div className={styles.prizeViewContent}>
        {filteredPrizes.map((item, index) => (
          <div key={index} className={styles.prizeViewContentItem}>
            <img src={item.showImage} alt="" />
            <div className={styles.prizeViewContentItemName}>{item.lotteryName}</div>
          </div>
        ))}
      </div>
    </div>
  );
}