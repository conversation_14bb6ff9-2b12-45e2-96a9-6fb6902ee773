import { Box } from '@alifd/next';
import { PopupState } from '../../../../type';
import ImgUpload from '@/components/ImgUpload';
import ColorPickerFormItem from '@/components/ColorPickerFormItem/ColorPickerFormItem';

export default function Popup(props) {
  const { tState, tDispatch } = props;
  const { popup } = tState;
  const {
    popupBg,
    popupBtnBg,
    popupBtnText,
    floatBg,
    floatTitle,
    floatConentText,
    floatConentTitle,
    taskBg,
    taskGuideBtnBg,
    taskGuideText,
    receiveBg,
    addressBg,
    addressText,
  } = popup;

  const handleUpdateField = (field: keyof PopupState, value: any) => {
    tDispatch({ type: 'UPDATE_MODULE', payload: { [field]: value } });
  };
  return (
    <Box direction={'column'} spacing={16}>
      <Box direction="row" spacing={32}>
        <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>
          <ImgUpload
            label="弹窗背景图"
            required
            img={{
              value: popupBg,
              width: 543,
              height: 669,
            }}
            onSuccess={(url: string) => handleUpdateField('popupBg', url)}
            onReset={() => handleUpdateField('popupBg', -1)}
          />
          <ColorPickerFormItem
            label="弹窗按钮背景"
            color={{ value: popupBtnBg }}
            onSetColor={(color: string) => handleUpdateField('popupBtnBg', color)}
            onReset={() => handleUpdateField('popupBtnBg', -1)}
          />
          <ColorPickerFormItem
            label="弹窗按钮文字"
            color={{ value: popupBtnText }}
            onSetColor={(color: string) => handleUpdateField('popupBtnText', color)}
            onReset={() => handleUpdateField('popupBtnText', -1)}
          />
        </Box>
        <span />
        <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>
          <ImgUpload
            label="抽奖任务子项标题"
            required
            img={{
              value: taskBg,
              width: 674,
              height: 160,
            }}
            onSuccess={(url: string) => handleUpdateField('taskBg', url)}
            onReset={() => handleUpdateField('taskBg', -1)}
          />
          <ColorPickerFormItem
            label="任务引导按钮背景"
            color={{ value: taskGuideBtnBg }}
            onSetColor={(color: string) => handleUpdateField('taskGuideBtnBg', color)}
            onReset={() => handleUpdateField('taskGuideBtnBg', -1)}
          />
          <ColorPickerFormItem
            label="任务引导按钮文字"
            color={{ value: taskGuideText }}
            onSetColor={(color: string) => handleUpdateField('taskGuideText', color)}
            onReset={() => handleUpdateField('taskGuideText', -1)}
          />
        </Box>
        <span />
        <Box direction="column" spacing={16} margin={[16, 0, 0, 0]} >
          <ImgUpload
            label="领取记录/不满足条件-子项背景"
            required
            img={{
              value: receiveBg,
              width: 675,
              height: 154,
            }}
            onSuccess={(url: string) => handleUpdateField('receiveBg', url)}
            onReset={() => handleUpdateField('receiveBg', -1)}
          />
          <ColorPickerFormItem
            label="地址按钮背景"
            color={{ value: addressBg }}
            onSetColor={(color: string) => handleUpdateField('addressBg', color)}
            onReset={() => handleUpdateField('addressBg', -1)}
          />
          <ColorPickerFormItem
            label="地址按钮文字"
            color={{ value: addressText }}
            onSetColor={(color: string) => handleUpdateField('addressText', color)}
            onReset={() => handleUpdateField('addressText', -1)}
          />
        </Box>
      </Box>
      <Box direction="row" spacing={16}>
        <Box direction={'column'} spacing={16}>
          <h6>领奖记录/抽奖任务/不满足条件-浮层配置</h6>
          <ImgUpload
            label="浮层背景"
            required
            img={{
              value: floatBg,
              width: 750,
              height: 1096,
            }}
            onSuccess={(url: string) => handleUpdateField('floatBg', url)}
            onReset={() => handleUpdateField('floatBg', -1)}
          />
        </Box>

        <span />
        <Box direction={'column'} spacing={16} style={{ marginTop: 26 }}>
          <ColorPickerFormItem
            label="浮层顶部标题"
            color={{ value: floatTitle }}
            onSetColor={(color: string) => handleUpdateField('floatTitle', color)}
            onReset={() => handleUpdateField('floatTitle', -1)}
          />
          <ColorPickerFormItem
            label="浮层内容主标题"
            color={{ value: floatConentTitle }}
            onSetColor={(color: string) => handleUpdateField('floatConentTitle', color)}
            onReset={() => handleUpdateField('floatConentTitle', -1)}
          />
        </Box>
        <span />
        <Box style={{ marginTop: 26, marginLeft: 44 }}>
          <ColorPickerFormItem
            label="浮层内容说明文案"
            color={{ value: floatConentText }}
            onSetColor={(color: string) => handleUpdateField('floatConentText', color)}
            onReset={() => handleUpdateField('floatConentText', -1)}
          />
        </Box>

      </Box>
    </Box>

  );
}