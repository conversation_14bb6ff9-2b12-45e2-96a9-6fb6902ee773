import PhonePreview from '@/components/PhonePreview/PhonePreview';
import styles from './index.module.scss';
import { useEffect } from 'react';
import BlindBoxSection from './components/BlindBoxSection';
import PrizeViewSection from './components/PrizeViewSection';
import AwardSection from './components/AwardSection';
import ExposeSection from './components/ExposeSection';
import Popup from './components/Popup';
import { createMainStyles, scrollToTop } from './utils/styleUtils';

export default function BlindBoxPreview(props) {
  const { tState, state } = props;
  const { main, popup, current } = tState;
  const { recommendGoods, prize } = state;

  useEffect(() => {
    if (current === 'popup') {
      scrollToTop();
    }
  }, [current]);


  return (
    <PhonePreview
      width={290}
    >
      <div style={{ position: 'relative' }}>
        <div
          style={createMainStyles(main)}
          className={styles.main}
        >
          <img className={styles.kv} src={main.kv} alt="" />
          <div className={styles.btns}>
            <div>活动规则</div>
            <div>领奖记录</div>
          </div>

          <BlindBoxSection main={main} />
          <PrizeViewSection main={main} prize={prize} />

          <div className={styles.task}>
            <img src={main.chanceBtn} alt="" />
          </div>

          <AwardSection main={main} />
          <ExposeSection main={main} recommendGoods={recommendGoods} />
        </div>

        <Popup popup={popup} visible={current === 'popup'} />
      </div>
    </PhonePreview>
  );
}