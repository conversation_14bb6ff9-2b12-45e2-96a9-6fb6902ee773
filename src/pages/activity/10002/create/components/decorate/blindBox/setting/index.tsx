import { Tab } from '@alifd/next';
import BlindBoxTheme from './theme';
import BlindBoxExpose from './expose';
import BlindBoxShare from './share';
import BlindBoxCard from './theme/components/card';
import { useState } from 'react';

export default function BlindBoxSetting(props) {
  const { needDisable } = props;
  const [activeKey, setActiveKey] = useState('1');
  return (
    <Tab
      activeKey={activeKey}
      onChange={(key) => {
      setActiveKey(key);
    }}
    >
      <Tab.Item key={'1'} disabled={needDisable} title={'页面样式装修'}>
        <BlindBoxTheme x-if={activeKey === '1'} disabled={needDisable} {...props} />
      </Tab.Item>
      <Tab.Item key={'2'} disabled={needDisable} title={'推荐商品设置'}>
        <BlindBoxExpose x-if={activeKey === '2'} />
      </Tab.Item>
      <Tab.Item key={'3'} disabled={needDisable} title={'通用列表页设置'}>
        <BlindBoxCard x-if={activeKey === '3'} disabled={needDisable} {...props} />
      </Tab.Item>
      <Tab.Item key={'4'} disabled={needDisable} title={'分享设置'}>
        <BlindBoxShare x-if={activeKey === '4'} />
      </Tab.Item>
    </Tab>
  );
}