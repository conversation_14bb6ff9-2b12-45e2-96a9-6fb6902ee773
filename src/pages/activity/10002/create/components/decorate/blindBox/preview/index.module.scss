.main {
  position: relative;

  .kv {
    width: 100%;
  }

  .btns {
    position: absolute;
    right: 0;
    top: 100px;

    div {
      background-color: var(--ruleBtnBg);
      color: var(--ruleBtnText);
      border-top-left-radius: 10px;
      border-bottom-left-radius: 10px;
      margin-bottom: 8px;
      padding: 2px 8px;
      font-size: 10px;
    }
  }

  .box {
    width: 95%;
    margin: -15px auto 0;
    position: relative;

    .boxBg {
      width: 100%;
      display: block;
    }

    .boxList {
      position: absolute;
      top: 0;
      left: 0;
      padding: 6px;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 6px;
      width: 100%;

      .boxItem {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .boxIcon {
          width: 100%;
          display: block;
          border-radius: 6px;
        }

        .boxBtn {
          width: 80%;
          display: block;
          margin-top: 6px;
        }

        &.selected {
          .boxIcon {
            outline: 4px solid var(--boxSelectedBg);
            outline-offset: -4px;
          }
        }
      }
    }

    .boxFooter {
      position: absolute;
      left: 50%;
      bottom: 10px;
      transform: translateX(-50%);
      display: flex;
      flex-direction: column;
      align-items: center;


      .openBtn {
        background: var(--boxBtnBg);
        color: var(--boxBtnText);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        border-radius: 25px;
        padding: 2.5px 0px;
        width: 120px;
      }

      .remainText {
        font-size: 10px;
        color: var(--remainText);
        margin-top: 4px;
        font-weight: bold;

        span {
          color: var(--remainCount)
        }
      }
    }

    .exchangeBtn {
      position: absolute;
      bottom: 5px;
      right: 5px;
      background: var(--exchangeBtnBg);
      color: var(--exchangeBtnText);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 2px;
      font-weight: bold;
      border-radius: 15px;
      padding: 2.5px 5px;
      font-size: 10px;

      img {
        width: 15px;
      }
    }
  }

  .prizeView {
    width: 95%;
    margin: 15px auto;
    text-align: center;

    .prizeViewTitle {
      height: 20px;
    }

    .prizeViewContent {

      display: flex;
      align-items: center;
      gap: 5px;
      overflow-x: scroll;

      .prizeViewContentItem {
        background: #fff;
        padding: 10px 5px;
        border-radius: 5px;
        width: 55px;

        img {
          width: 40px;
          height: 40px;
          margin-bottom: 5px;
        }

        .prizeViewContentItemName {
          font-size: 10px;
          color: #333;
          font-weight: bold;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          zoom: 0.8;
          -webkit-text-size-adjust: none;
          text-size-adjust: none;
        }
      }
    }
  }

  .reamin {
    width: 100%;
    text-align: center;
    font-size: 12px;
    color: var(--remainText);
    margin-top: -20px;

    span {
      color: var(--remainCount);
    }
  }

  .task {
    width: 100%;
    text-align: center;
    margin-top: 10px;

    img {
      width: 95%;
    }
  }

  .award {
    width: 95%;
    text-align: center;
    margin: 10px auto;

    .awardTitle {
      height: 20px;
    }

    .awardContainer {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: relative;

      .awardBg {
        width: 100%;
        display: block;
      }

      .awardContent {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        align-items: center;
        width: 100%;
        padding: 10px;

        .awardContentItem {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          margin-bottom: 10px;
          position: relative;

          &:not(:last-child)::after {
            content: '';
            position: absolute;
            bottom: -5.5px;
            left: 0;
            right: 0;
            border-bottom: 1px dashed #eee;

          }
        }



        .awardContentItemLeft {
          display: flex;
          align-items: center;
          gap: 10px;

          .avatar {
            width: 29px;
            height: 29px;
            background: #f9c039;
            border-radius: 50%;
          }
        }
      }
    }

  }

  .expose {
    width: 95%;
    text-align: center;
    margin: 15px auto;


    .exposeTitle {
      height: 20px;
    }

    .exposeContent {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 6px;
      width: 100%;
      margin-top: 6px;

      height: 450px;
      overflow-y: scroll;


      .exposeContentItem {
        padding: 5px;
        width: 134px;
        height: 220px;
        background: #ecedee;


        .exposeContentItemImg {
          width: 95%;
          object-fit: contain;
          background: white;
          margin: 0 auto;

          img {
            width: 100%;
            display: block;
          }
        }

        .exposeContentItemName {
          width: 100%;
          height: 30px;
          font-size: 12px;
          text-align: left;
          color: #333;
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          line-clamp: 2;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          margin-top: 5px;
        }

        .exposeContentItemSellNum {
          text-align: left;
          font-size: 10px;
          font-weight: 500;
          color: #795500;

          span {
            font-size: 18px;
          }
        }

        .exposeContentItemBuy {
          margin: 5px auto 0;
          width: 100px;
          height: 25px;
          border-radius: 25px;
          background: #000;
          line-height: 25px;
          color: #fff;
          font-size: 13px;
        }
      }
    }
  }
}

.popup {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 10000;

  .popupBg {
    width: 250px;
    object-fit: contain;
    position: absolute;
    left: 50%;
    top: 160px;
    transform: translateX(-50%);
  }

  .popupInfo {
    width: 200px;
    position: absolute;
    top: 230px;
    left: 50%;
    z-index: 100001;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .popupInfoTitle {
      font-size: 20px;
    }

    .popupInfoDesc {
      font-size: 13px;
      color: #808285;
      margin-top: 20px;
    }

    .popupBtn {
      width: 150px;
      height: 30px;
      color: var(--popupBtnText);
      font-size: 14px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 50px;
      border-radius: 5px;
      background-color: var(--popupBtnBg);
    }
  }
}
