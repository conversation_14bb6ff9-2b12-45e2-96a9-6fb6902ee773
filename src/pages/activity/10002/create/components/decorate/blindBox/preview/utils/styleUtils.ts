import { CSSProperties } from 'react';

export const createMainStyles = (main: any) => ({
  '--ruleBtnText': main.ruleBtnText,
  '--ruleBtnBg': main.ruleBtnBg,
  '--pageBg': main.pageBg,
  '--remainText': main.remainText,
  '--remainCount': main.remainCount,
  '--boxBtnBg': main.boxBtnBg,
  '--boxBtnText': main.boxBtnText,
  '--exchangeBtnBg': main.exchangeBtnBg,
  '--exchangeBtnText': main.exchangeBtnText,
  '--boxSelectedBg': main.boxSelectedBg,
  background: main.pageBg,
  paddingBottom: 10,
} as CSSProperties);

export const scrollToTop = () => {
  const phonePreviewContainer = document.querySelector('.phone-preview-content');
  if (phonePreviewContainer) {
    phonePreviewContainer.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }
};