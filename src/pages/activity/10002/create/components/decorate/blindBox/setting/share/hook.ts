import { useActivity } from '@/pages/activity/10002/create/reducer';
import { ModuleType, ShareState } from '@/pages/activity/10002/create/type';
import { validateShare } from './validator';


/**
 * 分享模块的自定义Hook
 * 提供对分享状态的访问和更新方法
 */
export function useShare() {
  const { state, dispatch } = useActivity();

  // 更新分享并自动重新验证
  const updateShare = (data?: Partial<ShareState>) => {
    if (!data) return;
    // 先更新数据
    dispatch({
      type: 'UPDATE_SHARE',
      payload: data,
    });

    // 获取更新后的数据
    const updatedShare = {
      ...state,
      share: {
        ...state.share,
        ...data,
      },
    };

    // 重新验证并更新错误状态
    const errors = validateShare(updatedShare);
    dispatch({
      type: 'VALIDATE_MODULE',
      module: ModuleType.SHARE,
      payload: errors,
    });
  };

  return {
    share: state.share,
    errors: state.errors[ModuleType.SHARE] || [],
    updateShare,
    dispatch,
  };
}
