import {
  ThemeState,
  ThemeAction,
} from './type';


export let defaultThemeState: Omit<ThemeState, 'current'> = {
  main: {
    kv: 'https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E5%A4%A7%E8%BD%AC%E7%9B%98%E8%A3%85%E4%BF%AE/kv.jpg',
    ruleBtnText: '#ffffff',
    ruleBtnBg: '#605149',
    pageBg: '#000000',
    remainText: '#ffffff',
    remainCount: '#fbea00',
    chanceBtn: 'https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E5%A4%A7%E8%BD%AC%E7%9B%98%E8%A3%85%E4%BF%AE/%E4%BB%BB%E5%8A%A1%E6%8C%89%E9%92%AE.png',
    wheelBg: 'https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E5%A4%A7%E8%BD%AC%E7%9B%98%E8%A3%85%E4%BF%AE/%E5%A4%A7%E8%BD%AC%E7%9B%98%EF%BC%88%E8%B0%83%E6%95%B4%EF%BC%89.png',
    wheelPointer: 'https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E5%A4%A7%E8%BD%AC%E7%9B%98%E8%A3%85%E4%BF%AE/%E7%AE%AD%E5%A4%B4.png',
    wheelText: '#000000',
    awardBg: 'https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E5%A4%A7%E8%BD%AC%E7%9B%98%E8%A3%85%E4%BF%AE/%E8%8E%B7%E5%A5%96%E5%90%8D%E5%8D%95%E8%83%8C%E6%99%AF.png',
    exposeTitle: 'https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E5%A4%A7%E8%BD%AC%E7%9B%98%E8%A3%85%E4%BF%AE/%E6%8E%A8%E8%8D%90%E5%95%86%E5%93%81%E6%A0%87%E9%A2%98.png',
    awardTitle: 'https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E5%A4%A7%E8%BD%AC%E7%9B%98%E8%A3%85%E4%BF%AE/%E8%8E%B7%E5%A5%96%E5%90%8D%E5%8D%95%E6%A0%87%E9%A2%98.png',
  },
  popup: {
    popupBg: 'https://img.alicdn.com/imgextra/i3/155168396/O1CN01N4iZvJ2BtQPn6g2zX_!!155168396.png',
    popupBtnBg: '#5d3500',
    popupBtnText: '#ffffff',
    floatBg: 'https://img.alicdn.com/imgextra/i3/155168396/O1CN01vzz0e72BtQPq8c05A_!!155168396.png',
    floatTitle: '#000000',
    floatConentTitle: '#000000',
    floatConentText: '#777777',
    taskBg: 'https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E5%A4%A7%E8%BD%AC%E7%9B%98%E8%A3%85%E4%BF%AE/%E6%8A%BD%E5%A5%96%E4%BB%BB%E5%8A%A1%E5%AD%90%E9%A1%B9%E8%83%8C%E6%99%AF.png',
    taskGuideBtnBg: '#5d3500',
    taskGuideText: '#ffffff',
    receiveBg: 'https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E5%A4%A7%E8%BD%AC%E7%9B%98%E8%A3%85%E4%BF%AE/%E9%A2%86%E5%8F%96%E8%AE%B0%E5%BD%95%E5%AD%90%E9%A1%B9%E8%83%8C%E6%99%AF.png',
    addressBg: '#5d3500',
    addressText: '#ffffff',
  },
  card: {
    cardBg: 'https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E5%A4%A7%E8%BD%AC%E7%9B%98%E8%A3%85%E4%BF%AE/%E8%BD%AC%E7%9B%98%E5%88%97%E8%A1%A8.png',
    countDownColor: '#000000',
  },

};

// 添加辅助函数用于更新默认主题
export function updateDefaultTheme(newDefaults: Partial<Omit<ThemeState, 'current'>>) {
  Object.keys(newDefaults).forEach(moduleKey => {
    if (defaultThemeState[moduleKey]) {
      defaultThemeState[moduleKey] = {
        ...defaultThemeState[moduleKey],
        ...newDefaults[moduleKey],
      };
    }
  });
}

export const themeState: ThemeState = {
  current: 'main',
  ...defaultThemeState,
};
// 主题Reducer
export function themeReducer(state: ThemeState, action: ThemeAction): ThemeState {
  switch (action.type) {
    case 'UPDATE_MODULE': {
      const moduleKey = state.current;
      const processedPayload = { ...action.payload };

      // 遍历payload中的每个属性
      Object.keys(processedPayload).forEach((key) => {
        const defaultValue = defaultThemeState[moduleKey][key];
        switch (processedPayload[key]) {
          // 重置为默认值
          case -1:
            processedPayload[key] = defaultValue;
            break;
          default:
            break;
        }
      });
      return {
        ...state,
        [moduleKey]: {
          ...state[moduleKey],
          ...processedPayload,
        },
      };
    }
    case 'INIT_MODULE': {
      const result = {
        ...state,
        ...action.payload,
      };
      updateDefaultTheme(result);
      return result;
    }
    case 'SET_CURRENT_MODULE':
      return {
        ...state,
        current: action.module!,
      };
    case 'UPDATE_THEME':
      return {
        ...state,
        current: action.payload as any,
      };
    default:
      return state;
  }
}