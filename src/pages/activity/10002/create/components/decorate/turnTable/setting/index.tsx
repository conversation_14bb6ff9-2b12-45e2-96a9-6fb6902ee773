import { Tab } from '@alifd/next';
import TurnTableTheme from './theme';
import TurnTableCard from './theme/components/card';
import TurnTableExpose from './expose';
import TurnTableShare from './share';
import { useState } from 'react';

export default function TurnTableSetting(props) {
  const { needDisable } = props;
  const [activeKey, setActiveKey] = useState('1');
  return (
    <Tab
      activeKey={activeKey}
      onChange={(key) => {
      setActiveKey(key);
    }}
    >
      <Tab.Item key={'1'} disabled={needDisable} title={'页面样式装修'}>
        <TurnTableTheme x-if={activeKey === '1'} {...props} />
      </Tab.Item>
      <Tab.Item key={'2'} disabled={needDisable} title={'推荐商品设置'}>
        <TurnTableExpose x-if={activeKey === '2'} />
      </Tab.Item>
      <Tab.Item key={'3'} disabled={needDisable} title={'通用列表页设置'}>
        <TurnTableCard x-if={activeKey === '3'} {...props} />
      </Tab.Item>
      <Tab.Item key={'4'} disabled={needDisable} title={'分享设置'}>
        <TurnTableShare x-if={activeKey === '4'} />
      </Tab.Item>
    </Tab>
  );
}