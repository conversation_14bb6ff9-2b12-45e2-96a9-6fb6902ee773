import styles from '../index.module.scss';

interface RecommendGood {
  picUrl: string;
  name: string;
  sellNum: number;
}

interface ExposeSectionProps {
  main: any;
  recommendGoods: RecommendGood[];
}

export default function ExposeSection({ main, recommendGoods }: ExposeSectionProps) {
  return (
    <div className={styles.expose}>
      <img src={main.exposeTitle} className={styles.exposeTitle} alt="" />
      <div className={styles.exposeContent}>
        {recommendGoods.map((item, index) => (
          <div key={index} className={styles.exposeContentItem}>
            <div className={styles.exposeContentItemImg}>
              <img src={item.picUrl} alt="" />
            </div>
            <div className={styles.exposeContentItemName}>{item.name}</div>
            <div className={styles.exposeContentItemSellNum}>销量：<span>{item.sellNum}</span></div>
            <div className={styles.exposeContentItemBuy}>去购买</div>
          </div>
        ))}
      </div>
    </div>
  );
}