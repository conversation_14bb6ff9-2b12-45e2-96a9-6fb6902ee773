.main {
  position: relative;

  .kvContainer {
    min-height: 540px;

    .kv {
      width: 100%;
      display: block;
    }
  }


  .btns {
    position: absolute;
    right: 0;
    top: 100px;

    div {
      background-color: var(--ruleBtnBg);
      color: var(--ruleBtnText);
      border-top-left-radius: 10px;
      border-bottom-left-radius: 10px;
      margin-bottom: 8px;
      padding: 2px 8px;
      font-size: 10px;
    }
  }

  .wheel {
    position: absolute;
    left: 50%;
    top: 235px;
    transform: translateX(-50%);
    width: 100%;

    .wheelInner {
      position: relative;
      text-align: center;
      overflow: hidden;

      .wheelBg {
        width: 95%;
      }

      .wheelPointer {
        position: absolute;
        top: 70px;
        left: 50%;
        transform: translateX(-50%);
        width: 90px;
      }

      .prizeSectors {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 100%;
        height: 100%;
        border-radius: 50%;

        .prizeSector {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          transform-origin: center;

          .sectorContent {
            position: absolute;
            top: 8%;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;

            .prizeImage {
              width: 30px;
              height: 30px;
              object-fit: cover;
              border-radius: 4px;
              margin-top: 4px;
            }

            .prizeName {
              font-size: 12px;
              color: var(--wheelText);
              font-weight: 500;
              line-height: 1.2;
              word-break: break-all;
              max-width: 75px;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              line-clamp: 1;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
            }
          }
        }
      }
    }
  }

  .remain {
    width: 100%;
    text-align: center;
    font-size: 12px;
    color: var(--remainText);
    margin-top: 10px;

    span {
      color: var(--remainCount);
    }
  }

  .task {
    width: 100%;
    text-align: center;
    margin-top: 10px;

    img {
      width: 95%;
    }
  }

  .award {
    width: 95%;
    text-align: center;
    margin: 10px auto;

    .awardTitle {
      height: 20px;
    }

    .awardContainer {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: relative;

      .awardBg {
        width: 100%;
        display: block;
      }

      .awardContent {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        align-items: center;
        width: 100%;
        padding: 10px;

        .awardContentItem {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          margin-bottom: 10px;
          position: relative;

          &:not(:last-child)::after {
            content: '';
            position: absolute;
            bottom: -5.5px;
            left: 0;
            right: 0;
            border-bottom: 1px dashed #eee;

          }
        }



        .awardContentItemLeft {
          display: flex;
          align-items: center;
          gap: 10px;

          .avatar {
            width: 29px;
            height: 29px;
            background: #f9c039;
            border-radius: 50%;
          }
        }
      }
    }

  }

  .expose {
    width: 95%;
    text-align: center;
    margin: 15px auto;


    .exposeTitle {
      height: 20px;
    }

    .exposeContent {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 6px;
      width: 100%;
      margin-top: 6px;

      height: 450px;
      overflow-y: scroll;


      .exposeContentItem {
        padding: 5px;
        width: 134px;
        height: 220px;
        background: #ecedee;


        .exposeContentItemImg {
          width: 95%;
          object-fit: contain;
          background: white;
          margin: 0 auto;

          img {
            width: 100%;
            display: block;
          }
        }


        .exposeContentItemName {
          width: 100%;
          height: 30px;
          font-size: 12px;
          text-align: left;
          color: #333;
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          line-clamp: 2;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          margin-top: 5px;
        }

        .exposeContentItemSellNum {
          text-align: left;
          font-size: 10px;
          font-weight: 500;
          color: #795500;

          span {
            font-size: 18px;
          }
        }

        .exposeContentItemBuy {
          margin: 5px auto 0;
          width: 100px;
          height: 25px;
          border-radius: 25px;
          background: #000;
          line-height: 25px;
          color: #fff;
          font-size: 13px;
        }
      }
    }
  }
}

.popup {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 10000;

  .popupBg {
    width: 250px;
    object-fit: contain;
    position: absolute;
    left: 50%;
    top: 160px;
    transform: translateX(-50%);
  }

  .popupInfo {
    width: 200px;
    position: absolute;
    top: 230px;
    left: 50%;
    z-index: 100001;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .popupInfoTitle {
      font-size: 20px;
    }

    .popupInfoDesc {
      font-size: 13px;
      color: #808285;
      margin-top: 20px;
    }

    .popupBtn {
      width: 150px;
      height: 30px;
      color: var(--popupBtnText);
      font-size: 14px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 50px;
      border-radius: 5px;
      background-color: var(--popupBtnBg);
    }
  }
}
