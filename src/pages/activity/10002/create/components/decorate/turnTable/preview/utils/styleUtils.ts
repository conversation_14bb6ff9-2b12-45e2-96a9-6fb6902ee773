import { CSSProperties } from 'react';

export const createTurnTableMainStyles = (main: any) => ({
  '--ruleBtnText': main.ruleBtnText,
  '--ruleBtnBg': main.ruleBtnBg,
  '--pageBg': main.pageBg,
  '--remainText': main.remainText,
  '--remainCount': main.remainCount,
  '--chanceBtn': main.chanceBtn,
  '--wheelText': main.wheelText,
  '--awardBg': main.awardBg,
  '--exposeTitle': main.exposeTitle,
  '--awardTitle': main.awardTitle,
  background: main.pageBg,
  paddingBottom: 10,
} as CSSProperties);

export const scrollToTop = () => {
  const phonePreviewContainer = document.querySelector('.phone-preview-content');
  if (phonePreviewContainer) {
    phonePreviewContainer.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }
};