import PhonePreview from '@/components/PhonePreview/PhonePreview';
import styles from './index.module.scss';
import { useEffect } from 'react';
import TurnTableSection from './components/TurnTableSection';
import AwardSection from './components/AwardSection';
import ExposeSection from './components/ExposeSection';
import Popup from './components/Popup';
import { createTurnTableMainStyles, scrollToTop } from './utils/styleUtils';

export default function TurnTablePreview(props) {
  const { tState, state } = props;
  const { main, popup, current } = tState;
  const { prize, recommendGoods } = state;

  useEffect(() => {
    if (current === 'popup') {
      scrollToTop();
    }
  }, [current]);


  return (
    <PhonePreview
      width={290}
    >
      <div style={{ position: 'relative' }}>
        <div
          style={createTurnTableMainStyles(main)}
          className={styles.main}
        >
          <div className={styles.kvContainer}>
            <img className={styles.kv} src={main.kv} alt="" />
          </div>
          <div className={styles.btns}>
            <div>活动规则</div>
            <div>领奖记录</div>
          </div>
          <TurnTableSection main={main} prize={prize} />
          <div className={styles.task}>
            <img src={main.chanceBtn} alt="" />
          </div>

          <AwardSection main={main} />
          <ExposeSection main={main} recommendGoods={recommendGoods} />
        </div>

        <Popup popup={popup} visible={current === 'popup'} />
      </div>
    </PhonePreview>
  );
}