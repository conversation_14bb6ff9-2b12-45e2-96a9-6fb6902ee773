import { Box, Button, Message } from '@alifd/next';
import { useEffect, useReducer, useState } from 'react';
import { themeReducer, themeState } from './reducer';
import { useActivity } from '../../../reducer';
import { ModuleType } from '../../../type';
import { validateShare } from './setting/share/validator';
import { validateRecommendGoods } from './setting/expose/validator';
import { showErrorMessageDialog } from '@/utils';
import _ from 'lodash';
import Container from '@/components/Container';
import TurnTableSetting from './setting';
import TurnTablePreview from './preview';
import LzIcon from '@/components/Icons';
import styles from './index.module.scss';
import { createActivity, updateActivity } from '@/api/v10002';
import { history } from '@ice/runtime';

interface Validator {
  module: ModuleType;
  getErrors: () => string[];
}

export default function TurnTable({ goNextStep, goPrevStep, needDisable }) {
  // 装修reducer
  const [tState, tDispatch] = useReducer(themeReducer, themeState);
  // 活动reducer
  const { state, dispatch } = useActivity();
  // 是否展示右侧预览
  const [hidePreview, setHidePreview] = useState(false);
  // 获取当前操作状态
  const { operationType } = state.extra;
  // 是否编辑活动
  const isEdit = operationType === 'edit';
  const isView = operationType === 'view';

  const [loading, setLoading] = useState(false);


  // 因为分享数据/曝光商品要接入活动数据进行配置 故使用活动reducer进行管理
  const validators: Validator[] = [
    { module: ModuleType.SHARE, getErrors: () => validateShare(state) },
    { module: ModuleType.RECOMMEND_GOODS, getErrors: () => validateRecommendGoods(state) },
  ];


  const validateModules = () => {
    let isValid = true;
    const allErrors: string[] = [];
    // 遍历所有验证器获取错误状态
    validators.forEach((validator) => {
      const errors = validator.getErrors();
      dispatch({
        type: 'VALIDATE_MODULE',
        module: validator.module,
        payload: errors,
      });
      allErrors.push(...errors);
      // 如果有错误，则验证不通过
      if (errors.length > 0) {
        isValid = false;
      }
    });
    if (!isValid) {
      showErrorMessageDialog(allErrors);
    }
    return isValid;
  };


  // 上一步
  const handleBack = () => {
    goPrevStep();
  };
  // 下一步：收集数据调用活动保存/更新接口
  const handleSubmit = async () => {
    if (isView) {
      history?.push('/activity/list');
      return;
    }
    const _state = _.cloneDeep(tState);
    // 删除当前模块标识 防止查看/复制时切换至非base模块
    delete _state.current;
    const isValid = validateModules();
    if (isValid) {
      setLoading(true);
      try {
        const apiFn = [createActivity, updateActivity];
        const { activityUrl } = await apiFn[+isEdit]({
          activityData: state as any,
          decoData: JSON.stringify(tState),
        });
        dispatch({
          type: 'UPDATE_EXTRA',
          payload: {
            activityUrl,
          },
        });
        goNextStep();
      } catch (error) {
        Message.error(error.message);
      } finally {
        setLoading(false);
      }
    }
  };
  const props = {
    tState,
    tDispatch,
    state,
    dispatch,
    needDisable,
  };


  // 切换预览显示/隐藏状态
  const togglePreview = () => {
    setHidePreview(prev => !prev);
  };


  useEffect(() => {
    const decorateObj = JSON.parse(state.decorate);
    decorateObj.current = 'main';
    tDispatch({ type: 'INIT_MODULE', payload: decorateObj });
  }, []);
  return (
    <div>
      <Box direction="row">
        <Box flex={1} style={{ position: 'relative' }}>
          <Container style={{ marginBottom: 0 }}>
            <Button
              onClick={() => {
                window.open('https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E7%9B%B2%E7%9B%92%E8%A3%85%E4%BF%AE/psd.zip');
              }}
              style={{ position: 'absolute', top: 30, right: 20, zIndex: 1000 }}
              type={'primary'}
              text
            >下载设计素材（PSD）</Button>
            <TurnTableSetting {...props} />
          </Container>
        </Box>

        <div className={`${styles.collapse} ${hidePreview ? styles.hidePreview : ''}`}>
          <div
            className={styles.icon}
            onClick={togglePreview}
          >
            <LzIcon
              type="to-right"
              size="xl"
            />
          </div>
        </div>
        <Box style={{ width: 350 }} x-if={!hidePreview}>
          <TurnTablePreview {...props} />
        </Box>
      </Box>
      <Box
        direction="row"
        justify="center"
        spacing={16}
        style={{
          position: 'fixed',
          bottom: 12,
          left: '50%',
          transform: 'translateX(calc(-50% + 110px))',
        }}
      >
        <Button onClick={handleBack}>
          上一步
        </Button>
        <Button loading={loading} type="primary" onClick={handleSubmit}>
          {isView ? '返回活动列表' : '下一步：完成创建并投放'}
        </Button>
      </Box>
    </div>
  );
}
