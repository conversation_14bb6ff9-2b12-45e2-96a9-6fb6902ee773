import { useRef, useState, useCallback, useEffect } from 'react';
import styles from '../index.module.scss';
import { usePrizeSectors } from '../hooks/usePrizeSectors';

// 常量定义
const SECTORS_COUNT = 8;
const SECTOR_ANGLE = 360 / SECTORS_COUNT;
const ROTATION_SPEED = 360; // 每秒转速
const EXTRA_ROTATIONS = 3; // 额外转圈数
const DECELERATION_DURATION = 7000; // 减速动画时长(ms)
const API_DELAY = 2000; // 接口延迟时间(ms)
const INITIAL_CHANCES = 0; // 初始抽奖次数


/**
 * 转盘组件
 * @description 为了C端交互，b端也实现了抽奖逻辑，但是怕客户要别的交互初始次数默认0，调试可以增加次数进行调试。
 * @param main 主配置
 * @param prize 奖品配置
 * @returns
 */
export default function TurnTableSection({ main, prize }) {
  const { prizeSectors } = usePrizeSectors(prize);

  const wheelRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();

  // 状态管理
  const [isSpinning, setIsSpinning] = useState(false);
  const [remainingChances, setRemainingChances] = useState(INITIAL_CHANCES);
  const [currentRotation, setCurrentRotation] = useState(0);

  // 模拟接口调用获取中奖奖品
  const mockLotteryAPI = useCallback((): Promise<{ prizeIndex: number }> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const prizeIndex = Math.floor(Math.random() * SECTORS_COUNT);
        console.log('prizeIndex', prizeIndex);
        resolve({ prizeIndex });
      }, API_DELAY);
    });
  }, []);

  // 计算目标角度 - 让指针指向奖品的正中间
  const calculateTargetAngle = useCallback((prizeIndex: number, fromAngle?: number) => {
    const baseAngle = fromAngle ?? currentRotation;

    // 计算奖品在转盘上的角度位置
    const prizeAngle = prizeIndex * SECTOR_ANGLE + SECTOR_ANGLE / 2;
    const targetAngle = 360 - prizeAngle;

    // 标准化当前角度到0-360范围
    const normalizedBaseAngle = ((baseAngle % 360) + 360) % 360;

    // 计算从当前位置到目标位置需要转动的角度
    let angleDiff = targetAngle - normalizedBaseAngle;
    if (angleDiff <= 0) {
      angleDiff += 360;
    }

    // 计算最终角度（包含额外转圈）
    const finalAngle = baseAngle + EXTRA_ROTATIONS * 360 + angleDiff;

    // 调试信息
    console.log('角度计算调试:', {
      // 奖品索引
      prizeIndex,
      // 奖品角度
      prizeAngle,
      // 目标角度
      targetAngle,
      // 基础角度
      baseAngle,
      // 标准化后的基础角度
      normalizedBaseAngle,
      // 角度差
      angleDiff,
      // 额外转圈数
      extraRotations: EXTRA_ROTATIONS,
      // 最终角度
      finalAngle,
    });

    return finalAngle;
  }, [currentRotation]);

  // 连续转动动画函数
  const startContinuousSpinning = useCallback((startAngle: number) => {
    let currentAngle = startAngle;
    let lastTime = performance.now();

    const animate = (currentTime: number) => {
      const deltaTime = (currentTime - lastTime) / 1000;
      currentAngle += ROTATION_SPEED * deltaTime;

      if (wheelRef.current) {
        wheelRef.current.style.transform = `rotate(${currentAngle}deg)`;
      }

      lastTime = currentTime;
      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);
    return () => currentAngle;
  }, []);

  // 停止连续转动
  const stopContinuousSpinning = useCallback(() => {
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
      animationRef.current = undefined;
    }
  }, []);

  // 处理指针点击事件
  const handlePointerClick = useCallback(async () => {
    // 检查是否可以抽奖
    if (isSpinning || remainingChances <= 0) {
      return;
    }

    setIsSpinning(true);

    try {
      // 开始连续转动
      const wheelElement = wheelRef.current;
      if (wheelElement) {
        wheelElement.style.transition = 'none';
      }

      const getCurrentAngle = startContinuousSpinning(currentRotation);

      // 并行调用接口获取中奖奖品
      const result = await mockLotteryAPI();

      // 接口返回后，停止连续转动并开始减速到目标位置
      stopContinuousSpinning();

      if (wheelElement) {
        const currentAngle = getCurrentAngle();
        const finalAngle = calculateTargetAngle(result.prizeIndex, currentAngle);

        // 缓慢减速停止到目标位置
        wheelElement.style.transition = `transform ${DECELERATION_DURATION}ms cubic-bezier(0.23, 1, 0.32, 1)`;
        wheelElement.style.transform = `rotate(${finalAngle}deg)`;
        setCurrentRotation(finalAngle);

        // 等待减速动画完成后显示结果
        setTimeout(() => {
          setIsSpinning(false);
          setRemainingChances(prev => prev - 1);
          // 显示中奖结果
          const prizeName = prizeSectors[result.prizeIndex]?.prize?.lotteryName || '未知奖品';
          console.log('prizeName', prizeName);
        }, DECELERATION_DURATION);
      }
    } catch (error) {
      stopContinuousSpinning();
      setIsSpinning(false);
    }
  }, [
    isSpinning,
    remainingChances,
    currentRotation,
    startContinuousSpinning,
    stopContinuousSpinning,
    mockLotteryAPI,
    calculateTargetAngle,
    prizeSectors,
  ]);

  // 组件卸载时清理动画
  useEffect(() => {
    return () => {
      stopContinuousSpinning();
    };
  }, [stopContinuousSpinning]);

  // 渲染奖品扇形区域
  const renderPrizeSectors = () => (
    <div className={styles.prizeSectors}>
      {prizeSectors.map((sector) => (
        <div
          key={sector.id}
          className={styles.prizeSector}
          style={sector.style}
        >
          <div className={styles.sectorContent}>
            <div className={styles.prizeName}>
              {sector.prize.lotteryName}
            </div>
            {sector.prize.showImage && (
              <img
                className={styles.prizeImage}
                src={sector.prize.showImage}
                alt={sector.prize.lotteryName}
              />
            )}
          </div>
        </div>
      ))}
    </div>
  );

  // 计算指针样式类名
  const getPointerClassName = () => {
    const classes = [styles.wheelPointer];
    if (isSpinning) classes.push(styles.spinning);
    if (remainingChances <= 0) classes.push(styles.disabled);
    return classes.join(' ');
  };

  return (
    <div className={styles.wheel}>
      <div className={styles.wheelInner}>
        {/* 转动容器：包含转盘背景和奖品 */}
        <div
          ref={wheelRef}
          style={{
            position: 'relative',
            transform: `rotate(${currentRotation}deg)`,
            transition: 'none',
          }}
        >
          <img className={styles.wheelBg} src={main.wheelBg} alt="" />
          {renderPrizeSectors()}
        </div>

        {/* 指针保持不动，添加点击事件 */}
        <img
          className={getPointerClassName()}
          src={main.wheelPointer}
          alt=""
          style={{
            cursor: remainingChances > 0 ? 'pointer' : 'not-allowed',
          }}
          onClick={handlePointerClick}
        />
      </div>

      <div className={styles.remain}>
        剩余抽奖次数：<span>{remainingChances}</span>次
      </div>
    </div>
  );
}