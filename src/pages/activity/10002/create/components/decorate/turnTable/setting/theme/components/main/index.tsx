import ImgUpload from '@/components/ImgUpload';
import { Box } from '@alifd/next';
import { MainState } from '../../../../type';
import ColorPickerFormItem from '@/components/ColorPickerFormItem/ColorPickerFormItem';

export default function Main(props) {
  const { tState, tDispatch, needDisable } = props;
  const { main } = tState;
  const {
    kv,
    ruleBtnText,
    ruleBtnBg,
    pageBg,
    remainText,
    remainCount,
    chanceBtn,
    wheelBg,
    wheelPointer,
    wheelText,
    awardBg,
    exposeTitle,
    awardTitle,
  } = main;

  const handleUpdateField = (field: keyof MainState, value: any) => {
    tDispatch({ type: 'UPDATE_MODULE', payload: { [field]: value } });
  };

  return (
    <Box direction="row" spacing={32}>
      <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>
        <ImgUpload
          label="活动KV图"
          required
          disabled={needDisable}
          img={{
            value: kv,
            width: 750,
          }}
          onSuccess={(url: string) => handleUpdateField('kv', url)}
          onReset={() => handleUpdateField('kv', -1)}
        />
        <ColorPickerFormItem
          label="规则按钮字色"
          disabled={needDisable}
          color={{ value: ruleBtnText }}
          onSetColor={(color: string) => handleUpdateField('ruleBtnText', color)}
          onReset={() => handleUpdateField('ruleBtnText', -1)}
        />
        <ColorPickerFormItem
          label="规则按钮背景色"
          disabled={needDisable}
          color={{ value: ruleBtnBg }}
          onSetColor={(color: string) => handleUpdateField('ruleBtnBg', color)}
          onReset={() => handleUpdateField('ruleBtnBg', -1)}
        />
        <ColorPickerFormItem
          label="页面背景色"
          disabled={needDisable}
          color={{ value: pageBg }}
          onSetColor={(color: string) => handleUpdateField('pageBg', color)}
          onReset={() => handleUpdateField('pageBg', -1)}
        />
        <ColorPickerFormItem
          label="剩余机会字色"
          disabled={needDisable}
          color={{ value: remainText }}
          onSetColor={(color: string) => handleUpdateField('remainText', color)}
          onReset={() => handleUpdateField('remainText', -1)}
        />
        <ColorPickerFormItem
          label="剩余机会数值"
          disabled={needDisable}
          color={{ value: remainCount }}
          onSetColor={(color: string) => handleUpdateField('remainCount', color)}
          onReset={() => handleUpdateField('remainCount', -1)}
        />
      </Box>
      <span />
      <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>
        <ImgUpload
          disabled={needDisable}
          label="大转盘背景"
          required
          img={{
            value: wheelBg,
            width: 705,
            height: 705,
          }}
          onSuccess={(url: string) => handleUpdateField('wheelBg', url)}
          onReset={() => handleUpdateField('wheelBg', -1)}
        />
        <ImgUpload
          disabled={needDisable}
          label="大转盘指针按钮"
          required
          img={{
            value: wheelPointer,
            width: 210,
            height: 268,
          }}
          onSuccess={(url: string) => handleUpdateField('wheelPointer', url)}
          onReset={() => handleUpdateField('wheelPointer', -1)}
        />
        <ImgUpload
          label="获取更多抽奖机会-按钮"
          required
          disabled={needDisable}
          img={{
            value: chanceBtn,
            width: 700,
            height: 100,
          }}
          onSuccess={(url: string) => handleUpdateField('chanceBtn', url)}
          onReset={() => handleUpdateField('chanceBtn', -1)}
        />
        <ColorPickerFormItem
          label="转盘文案字色"
          disabled={needDisable}
          color={{ value: wheelText }}
          onSetColor={(color: string) => handleUpdateField('wheelText', color)}
          onReset={() => handleUpdateField('wheelText', -1)}
        />
      </Box>
      <span />
      <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>
        <ImgUpload
          label="活动推荐商品标题"
          disabled={needDisable}
          required
          img={{
            value: exposeTitle,
            height: 55,
          }}
          onSuccess={(url: string) => handleUpdateField('exposeTitle', url)}
          onReset={() => handleUpdateField('exposeTitle', -1)}
        />
        <ImgUpload
          label="获奖名单背景"
          disabled={needDisable}
          required
          img={{
            value: awardBg,
            width: 700,
            height: 425,
          }}
          onSuccess={(url: string) => handleUpdateField('awardBg', url)}
          onReset={() => handleUpdateField('awardBg', -1)}
        />
        <ImgUpload
          label="获奖名单标题"
          disabled={needDisable}
          required
          img={{
            value: awardTitle,
            height: 55,
          }}
          onSuccess={(url: string) => handleUpdateField('awardTitle', url)}
          onReset={() => handleUpdateField('awardTitle', -1)}
        />
      </Box>
    </Box>
  );
}