import { Box, Radio } from '@alifd/next';
import Popup from './components/popup';
import Main from './components/main';
import { useEffect } from 'react';

export default function TurnTableTheme(props) {
  const { tState, tDispatch, needDisable } = props;
  const { current } = tState;

  const handleChange = (value: string) => {
    tDispatch({
      type: 'UPDATE_THEME',
      payload: value,
    });
  };

  useEffect(() => {
    tDispatch({
      type: 'UPDATE_THEME',
      payload: 'main',
    });
  }, []);

  return (
    <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>
      <Radio.Group shape="button" disabled={needDisable} value={current} onChange={handleChange}>
        <Radio value={'main'}>主页面</Radio>
        <Radio value={'popup'}>弹窗</Radio>
      </Radio.Group>
      {
        current === 'main' ? <Main {...props} /> : <Popup {...props} />
      }
    </Box>
  );
}