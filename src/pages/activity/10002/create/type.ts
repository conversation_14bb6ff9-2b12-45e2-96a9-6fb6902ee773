export enum ModuleType {
  BASE = 'base',
  THRESHOLD = 'threshold',
  CHANCE = 'chance',
  PRIZE = 'prize',
  LIMIT = 'limit',
  RULE = 'rule',
  SHARE = 'share',
  RECOMMEND_GOODS = 'recommendGoods',
}
// Action类型定义
type ActionType =
  | 'UPDATE_BASE'
  | 'UPDATE_THRESHOLD'
  | 'UPDATE_CHANCE'
  | 'UPDATE_PRIZE'
  | 'UPDATE_LIMIT'
  | 'UPDATE_RULE'
  | 'UPDATE_SHARE'
  | 'UPDATE_RECOMMEND_GOODS'
  | 'UPDATE_DECORATE'
  | 'VALIDATE_MODULE'
  | 'UPDATE_EXTRA'
  | 'CLEAR_ERRORS'
  | 'INIT_MODULE';

export interface Action {
  type: ActionType;
  payload?: any;
  module?: ModuleType;
}

// 基础信息模块状态定义
export interface BaseInfoState {
  activityType: number;
  activityName: string;
  startTime: string;
  endTime: string;
  templateCode: number;
  activityId?: string;
  wheelType: number;
}
export interface ExtraState {
  operationType: 'edit' | 'add' | 'copy' | 'view';
  originalEndTime: string;
  // 活动状态 1未开始 2进行中 3已结束
  activityStatus: number;
  activityUrl: string;
}

export interface ThresholdState {
  thresholdType: number;
  thresholdInfo: any;
}

export interface ChanceState {
  // 免费赠送 1 首次访问活动赠送 2 每人每天访问赠送
  chanceType: number;
  // 免费赠送次数
  freeChance: number;
  // 任务获取 1 设置 2 不设置
  taskChance: number;
  taskList: any[];
}

export type PrizeState = any[];
export interface LimitState {
  // 每人每天中奖限制 1 限制 2 不限制
  limitType: number;
  // 每人每天中奖次数
  limitCount: number;
  // 每人累计中奖限制 1 限制 2 不限制
  totalLimitType: number;
  // 每人累计中奖次数
  totalLimitCount: number;
  // 开启获奖名单 1 开启 2 不开启
  openAwardList: number;
  // 奖品全部发完可继续抽奖 1 开启 2不开启
  continueDraw: number;
}

export interface SkuItem {
  numIid: string;
  name: string;
  picUrl: string;
  sellNum: number;
}

export interface ShareState {
  shareTitle: string;
  shareContent: string;
  mpImg: string;
}

export interface AppState {
  extra: ExtraState;
  base: BaseInfoState;
  threshold: ThresholdState;
  chance: ChanceState;
  prize: PrizeState;
  limit: LimitState;
  rule: string;
  decorate: string;
  share: ShareState;
  recommendGoods: SkuItem[];
  errors: Record<ModuleType, string[]>;
}
