import React, { createContext, useReducer, ReactNode, useContext } from 'react';
import { Action, AppState, ModuleType } from './type';
import dayjs from 'dayjs';
import { getExpireDay } from '@/utils';

const expireDay = getExpireDay();
// 初始状态
const initialState: AppState = {
  extra: {
    // 操作类型 add 新增 edit 编辑 view 预览 copy 复制
    operationType: 'add',
    // 原始结束时间 用于复制活动时，判断不可选择原始活动之前的时间
    originalEndTime: dayjs().add(expireDay > 15 ? 15 : expireDay, 'day').format('YYYY-MM-DD 00:00:00'),
    // 活动状态 1未开始 2进行中 3已结束
    activityStatus: 1,
    // 活动地址
    activityUrl: '',
  },
  base: {
    // 活动类型
    activityType: 10002,
    // 模板ID
    templateCode: 1001,
    // 活动名称
    activityName: `大转盘抽奖${dayjs().format('YYYY-MM-DD')}`,
    // 活动开始时间
    startTime: dayjs().format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: dayjs().add(expireDay > 15 ? 15 : expireDay, 'day').format('YYYY-MM-DD 00:00:00'),
    // 1 大转盘 2 抽盲盒
    wheelType: 1,
  },
  threshold: {
    thresholdType: 1,
    thresholdInfo: {
      crowdName: '',
      conditions: {},
    },
  },
  chance: {
    // 免费赠送类型 1 首次访问活动赠送 2每人每天访问活动赠送
    chanceType: 1,
    // 免费赠送次数
    freeChance: 0,
    // 任务获取 1设置2不设置
    taskChance: 1,
    taskList: [],
  },
  prize: [],
  limit: {
    // 每人每天中间限制 1限制 2不限制
    limitType: 2,
    // 每人每天中间限制次数
    limitCount: 1,
    // 每人累计中奖限制 1限制 2不限制
    totalLimitType: 2,
    // 活动内累计最多中奖次数
    totalLimitCount: 1,
    // 开启获奖名单 1开启 2不开启
    openAwardList: 1,
    // 奖品发完可继续抽奖 1开启 2不开启
    continueDraw: 2,
  },
  rule: '',
  recommendGoods: [],
  decorate: '',
  share: {
    shareTitle: '好运来袭，快来试试手气哦！',
    shareContent: '我抽中惊喜大奖啦，你也快来试试吧！',
    mpImg: 'https://novae.oss-cn-zhangjiakou.aliyuncs.com/vf/%E5%A4%A7%E8%BD%AC%E7%9B%98%E8%A3%85%E4%BF%AE/%E5%88%86%E4%BA%AB%E5%9B%BE.png',
  },
  errors: {
    [ModuleType.BASE]: [],
    [ModuleType.THRESHOLD]: [],
    [ModuleType.CHANCE]: [],
    [ModuleType.PRIZE]: [],
    [ModuleType.LIMIT]: [],
    [ModuleType.RULE]: [],
    [ModuleType.SHARE]: [],
    [ModuleType.RECOMMEND_GOODS]: [],
  },
};


// Reducer函数
function reducer(state: AppState, action: Action): AppState {
  switch (action.type) {
    case 'UPDATE_BASE':
      return {
        ...state,
        base: {
          ...state.base,
          ...action.payload,
        },
      };
    case 'UPDATE_THRESHOLD':
      console.log('UPDATE_THRESHOLD', {
        ...state.threshold,
        ...action.payload,
      });
      return {
        ...state,
        threshold: {
          ...state.threshold,
          ...action.payload,
        },
      };
    case 'UPDATE_CHANCE':
      console.log('UPDATE_CHANCE', {
        ...state.chance,
        ...action.payload,
      });
      return {
        ...state,
        chance: {
          ...state.chance,
          ...action.payload,
        },
      };
    case 'UPDATE_PRIZE':
      console.log('UPDATE_PRIZE', action.payload);
      return {
        ...state,
        prize: action.payload,
      };
    case 'UPDATE_LIMIT':
      console.log('UPDATE_LIMIT', {
        ...state.limit,
        ...action.payload,
      });
      return {
        ...state,
        limit: {
          ...state.limit,
          ...action.payload,
        },
      };
    case 'UPDATE_RULE':
      return {
        ...state,
        rule: action.payload,
      };
    case 'UPDATE_SHARE':
      return {
        ...state,
        share: {
          ...state.share,
          ...action.payload,
        },
      };
    case 'UPDATE_RECOMMEND_GOODS':
      return {
        ...state,
        recommendGoods: action.payload,
      };
      case 'UPDATE_DECORATE':
        console.log('action.payload', action.payload);
        return {
          ...state,
          decorate: action.payload,
        };
    case 'VALIDATE_MODULE':
      if (!action.module) return state;
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.module]: action.payload,
        },
      };
    case 'CLEAR_ERRORS':
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.module as ModuleType]: [],
        },
      };
    case 'UPDATE_EXTRA':
      console.log('UPDATE_EXTRA', {
        extra: {
          ...state.extra,
          ...action.payload,
        },
      });
      return {
        ...state,
        extra: {
          ...state.extra,
          ...action.payload,
        },
      };
    case 'INIT_MODULE': {
      console.log('INIT_MODULE', {
        ...state,
        ...action.payload.activityData,
        decorate: action.payload.decorationData,
      });
      return {
        ...state,
        ...action.payload.activityData,
        decorate: action.payload.decorationData,
      };
    }
    default:
      return state;
  }
}

// 创建Context
interface ActivityContextType {
  state: AppState;
  dispatch: React.Dispatch<Action>;
}

export const ActivityContext = createContext<ActivityContextType>({
  state: initialState,
  dispatch: () => null,
});

// Context Provider组件
interface ActivityProviderProps {
  children: ReactNode;
}

export function ActivityProvider({ children }: ActivityProviderProps) {
  const [state, dispatch] = useReducer(reducer, initialState);

  return (
    <ActivityContext.Provider value={{ state, dispatch }}>
      {children}
    </ActivityContext.Provider>
  );
}

// 自定义Hook方便使用Context
export function useActivity() {
  const context = useContext(ActivityContext);
  if (!context) {
    // 改成中文
    throw new Error('useActivity必须使用ActivityProvider包裹');
  }
  return context;
}
