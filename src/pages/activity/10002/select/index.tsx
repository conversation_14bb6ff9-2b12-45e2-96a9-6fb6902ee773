import styles from './index.module.scss';
import Intro from '@/components/Intro';
import Index from '@/components/Container';
import ActivitySkinSelector from './components/ActivitySkinSelector/ActivitySkinSelector';


const instructions = `经典抽奖活动，可选择大转盘，盲盒等不同样式，设置不同奖品，不同中奖率；买家在抖音端抽奖。增强互动，增加粘度，提升转化率。
1、活动效果：会员拉新、新会员促活、提升会员复购转化
2、活动门槛支持设置会员和粉丝，满足活动门槛的买家才可参与活动；奖品支持红包、优惠券、实物奖品、会员专享券、积分`;

export default function CustomActivity() {
  return (
    <div className={styles.container}>
      <Intro
        activityName="抽奖活动"
        docLink="https://gyj4qdmjsv.feishu.cn/wiki/UDKWw35f5im9frkRDNtcg4cgn8b?from=from_copylink"
        instructions={instructions}
      />
      <Index
        title="选择模板"
        style={{ padding: 20, marginTop: 20 }}
      >
        <ActivitySkinSelector activityType={10002} />
      </Index>
    </div>
  );
}
