import styles from './index.module.scss';
import Intro from '@/components/Intro';
import Index from '@/components/Container';
import { useState } from 'react';
import ActivitySkinSelector from './components/ActivitySkinSelector/ActivitySkinSelector';


const instructions = `通过设置奖品池的形式让用户预约奖品，用户达成领奖条件后即可获得奖励
1、活动效果：会员拉新、新会员促活、提升会员复购转化
2、活动门槛支持设置会员和粉丝，满足活动门槛的买家才可参与活动；奖品支持红包、优惠券、实物奖品、会员专享券、积分`;

export default function CustomActivity() {
  // TODO
  const [qrcodeSrc, setQrcodeSrc] = useState('');

  return (
    <div className={styles.container}>
      <Intro
        activityName="自定义活动"
        docLink="https://gyj4qdmjsv.feishu.cn/wiki/UDKWw35f5im9frkRDNtcg4cgn8b?from=from_copylink"
        instructions={instructions}
        qrcodeSrc={qrcodeSrc}
      />
      <Index
        title="选择模板"
        style={{ padding: 20, marginTop: 20 }}
      >
        <ActivitySkinSelector activityType={1} />
      </Index>
    </div>
  );
}
