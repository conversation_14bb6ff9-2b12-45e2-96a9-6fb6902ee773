import React, { useState, useEffect } from 'react';
import PhonePreview from '@/components/PhonePreview/PhonePreview';
import { Button } from '@alifd/next';
import SkinList from '../SkinList/SkinList';
import { history } from 'ice';

import '../ActivitySkinSelector.scss';

export default function TemplateItem({ template }) {
  const [selectedSkinIndex, setSelectedSkinIndex] = useState(0);

  const onSelectSkin = async index => {
    setSelectedSkinIndex(index);
    const { atmosphereJson, cardJson } = template[index].json;
    const { templateId } = template[index];
    console.log('选择模板', template[index], atmosphereJson, cardJson);
    history?.push('/activity/custom/create/', { atmosphereJson, cardJson, templateId });
  };

  return (
    <div className="template-item">
      <PhonePreview width={180}>
        <img
          className="selected-image"
          alt={''}
          src={template[selectedSkinIndex]?.json?.previewImg}
        />
      </PhonePreview>
      <div className="template-content">
        <span className="template-content-title">可选皮肤：</span>
        <SkinList
          template={template}
          selectedSkinIndex={selectedSkinIndex}
          setSelectedSkinIndex={setSelectedSkinIndex}
        />
        <div className="template-tags">
          <span className="status-red">{'全店促销'}</span>
          <span className="status-normal">{'日常促复购'}</span>
        </div>
        <div className="select-button">
          <Button
            type="primary"
            onClick={() => onSelectSkin(selectedSkinIndex)}
          >
            立即创建
          </Button>
        </div>
      </div>
    </div>
  );
}
