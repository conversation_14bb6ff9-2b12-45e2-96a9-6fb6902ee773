import React, { useState, useEffect } from 'react';
import TemplateItem from './TemplateItem/TemplateItem';
import { templateGetTempList, templateGetStyleList } from '@/api/b';
import { getActivityTypeLabel } from '@/utils';
import { Loading, Message } from '@alifd/next';

export default function ActivitySkinSelector(props) {
  const { activityType } = props;
  const [loading, setLoading] = useState(false);

  const [templateList, setTemplateList] = useState<any>([]);

  useEffect(() => {
    fetchTemplateList().then();
  }, []);

  async function fetchTemplateList() {
    setLoading(true);
    try { // 获取到当前活动的模板列表
      const { list }: any = await templateGetTempList({
        pageNo: 1,
        pageSize: 10,
        activityType,
      } as any);
      // 获取到每个模板的样式列表
      Promise.all(
        list.map(item =>
          templateGetStyleList({
            pageNo: 1,
            pageSize: 1000,
            activityType,
            templateId: item.templateId,
          } as any),
        ),
      ).then(styleRes => {
        const templateStyleList = styleRes.map(item => item.list);
        templateStyleList.forEach(i1 => {
          i1.forEach(i2 => {
            i2.json = i2.json ? JSON.parse(i2.json) : {};
          });
        });
        console.log(`活动类型-${getActivityTypeLabel(activityType)}-模板列表：`, templateStyleList);
        setTemplateList(templateStyleList);
      });
    } catch (e) {
      Message.error(e.message);
    } finally {
      setLoading(false);
    }
  }

  return (
    <Loading visible={loading} style={{ width: '100%', height: '100%' }} className="skin-selector">
      {templateList.map((template, index) => (
        <TemplateItem
          key={index}
          template={template}
        />
      ))}
    </Loading>
  );
}
