import { useState } from 'react';
import { Button, <PERSON>con, Slider } from '@alifd/next';

import '../ActivitySkinSelector.scss';

export default function SkinList({ template, selectedSkinIndex, setSelectedSkinIndex }) {
  const [sliderIndex, setSliderIndex] = useState(0);

  const handlePrev = () => {
    if (sliderIndex > 0) {
      setSliderIndex(sliderIndex - 1);
    }
  };

  const handleNext = () => {
    if (sliderIndex < template.length - 3) {
      setSliderIndex(sliderIndex + 1);
    }
  };

  return (
    <div className="skin-list">
      <Slider
        activeIndex={sliderIndex}
        onChange={setSliderIndex}
        className="skin-slider"
        slidesToShow={3}
        arrows={false}
        dots={false}
        infinite={false}
      >
        {template.map((skin, index) => (
          <div
            key={index}
            className={`skin-item ${index === selectedSkinIndex ? 'selected' : ''}`}
            onClick={() => setSelectedSkinIndex(index)}
          >
            <div
              className={`skin-item-img template-${template[0].templateId}`}
              style={{ backgroundImage: `url(${skin.json.previewImg})` }}
            />
          </div>
        ))}
      </Slider>
      {sliderIndex > 0 && (
        <Button
          type="primary"
          iconSize="large"
          text
          className="arrow-button left"
          onClick={handlePrev}
        >
          <Icon type="arrow-left" />
        </Button>
      )}
      {sliderIndex < template.length - 3 && (
        <Button
          type="primary"
          iconSize="large"
          text
          className="arrow-button right"
          onClick={handleNext}
        >
          <Icon type="arrow-right" />
        </Button>
      )}
    </div>
  );
}
