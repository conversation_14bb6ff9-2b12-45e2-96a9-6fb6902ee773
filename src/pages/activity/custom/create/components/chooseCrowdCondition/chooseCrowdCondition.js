import { Dialog } from '@alifd/next';
import React from 'react';
import CrowdConditionModalContent from './CrowdConditionModalContent.jsx';
import { addDialogRef } from '@/utils/dialogMapper';

export default function chooseCrowdCondition(initialValues = {}) {
  return new Promise(resolve => {
    const dialogRef = Dialog.show({
      v2: true,
      title: '选择人群条件',
      width: 800,
      height: '90vh',
      centered: true,
      closeMode: ['close'],
      className: 'choose-crowd-condition-dialog',
      content: (
        <CrowdConditionModalContent
          initialValues={initialValues}
          onResolve={result => {
            resolve(result);
            dialogRef.hide();
          }}
        />
      ),
      footer: false,
    });
    addDialogRef(dialogRef);
  });
}
