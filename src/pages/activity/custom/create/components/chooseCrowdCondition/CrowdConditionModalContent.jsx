import React, { useState, useEffect } from 'react';
import {
  Form,
  Field,
  Input,
  Message,
  Checkbox,
  DatePicker2,
  Radio,
  Box,
  Button,
  Card,
} from '@alifd/next';
import dayjs from 'dayjs';
import Container from '@/components/Container';
import NumberInput from '@/components/NumberInput/NumberInput';
import { isVoid, showErrorMessageDialog } from '@/utils';
import ProductPicker from '../ProductPicker';
import './CrowdConditionModalContent.less';

export default function CrowdConditionModalContent({ initialValues, onResolve }) {
  const [crowdList, setCrowdList] = useState([]); // 存储 crowdInfo 数组

  const [forceChecked, setForceChecked] = useState({});
  // 使用 Field 初始化表单数据，并填充 initialValues
  const crowdConditionField = Field.useField({ values: initialValues });

  useEffect(() => {
    // 回显 crowdName
    crowdConditionField.setValue('crowdName', initialValues.crowdName || '');
    // 处理 crowdInfo 列表回显
    if (Array.isArray(initialValues.crowdInfo)) {
      const list = initialValues.crowdInfo.map(item => {
        const config = JSON.parse(item.configJson || '{}');
        const { isChecked = false, startTime = '', endTime = '', checkList = [] } = config;

        // 公共：勾选状态
        crowdConditionField.setValue(`crowdInfo_${item.id}`, !!isChecked);

        // 时间类条件
        if (['joinMemberDateCrowdImpl', 'lastTradeDateCrowdImpl'].includes(item.interfaceClass)) {
          crowdConditionField.setValue(`crowdInfo_${item.id}_startTime`, startTime);
          crowdConditionField.setValue(`crowdInfo_${item.id}_endTime`, endTime);
        }

        // 会员等级
        if (item.interfaceClass === 'gradeLevelCrowdImpl') {
          checkList.forEach((opt, idx) => {
            crowdConditionField.setValue(`crowdInfo_${item.id}_opt_${idx}`, opt.isChecked);
          });
        }

        // 交易条件：订单周期
        if (item.interfaceClass === 'tradeCycleCrowdImpl') {
          // checkList[0]: 在 xx 天内累计订单
          crowdConditionField.setValue(`crowdInfo_${item.id}_checkList_0_isChecked`, checkList[0]?.isChecked || false);
          crowdConditionField.setValue(`crowdInfo_${item.id}_checkList_0_inDays`, checkList[0]?.inDays || 1);
          // checkList[1]: 指定时间内累计订单
          crowdConditionField.setValue(`crowdInfo_${item.id}_checkList_1_isChecked`, checkList[1]?.isChecked || false);
          crowdConditionField.setValue(`crowdInfo_${item.id}_checkList_1_startTime`, checkList[1]?.startTime || '');
          crowdConditionField.setValue(`crowdInfo_${item.id}_checkList_1_endTime`, checkList[1]?.endTime || '');
        }

        // 交易条件：订单状态
        if (item.interfaceClass === 'tradeStatusCrowdImpl') {
          // checkList[0]: 已付款未关闭
          crowdConditionField.setValue(`crowdInfo_${item.id}_checkList_0_isChecked`, checkList[0]?.isChecked || false);
          // checkList[1]: 已收货
          crowdConditionField.setValue(`crowdInfo_${item.id}_checkList_1_isChecked`, checkList[1]?.isChecked || false);
        }

        // 交易条件：累计订单金额
        if (item.interfaceClass === 'tradePaymentCrowdImpl') {
          crowdConditionField.setValue(`crowdInfo_${item.id}_countPayment`, config.countPayment || '');
        }

        // 交易条件：累计订单数
        if (item.interfaceClass === 'tradeNumCrowdImpl') {
          crowdConditionField.setValue(`crowdInfo_${item.id}_countNum`, config.countNum || '');
        }

        // 交易条件：购买指定商品
        if (item.interfaceClass === 'tradeSkuCrowdImpl') {
          crowdConditionField.setValue(`crowdInfo_${item.id}_skuList`, config.skuList || []);
        }

        return { ...item, parsedConfig: config };
      });
      setCrowdList(list);
    }
  }, [initialValues]);

  useEffect(() => {
    // 判断是否触发了"强依赖条件"的勾选（订单金额、订单数、指定商品）
    const isTriggerChecked = () =>
      crowdList.some(
        item =>
          ['tradePaymentCrowdImpl', 'tradeNumCrowdImpl', 'tradeSkuCrowdImpl'].includes(item.interfaceClass) &&
          crowdConditionField.getValue(`crowdInfo_${item.id}`), // 是否被勾选
      );

    // 如果任一强依赖条件被勾选，则需要强制勾选其依赖项
    const needForceCheck = isTriggerChecked();

    const forceState = {}; // 用于记录哪些项需要强制勾选并禁用

    // 遍历所有人群条件项
    crowdList.forEach(item => {
      // 如果是依赖项（订单周期、订单状态）
      if (['tradeCycleCrowdImpl', 'tradeStatusCrowdImpl'].includes(item.interfaceClass)) {
        // 若强依赖条件被勾选，则将当前依赖项设置为 true
        if (needForceCheck) {
          crowdConditionField.setValue(`crowdInfo_${item.id}`, true);
        }
        // 记录当前项是否需要禁用（用于 UI 控制）
        forceState[item.id] = needForceCheck;
      }
    });

    // 更新需要禁用的选项状态，避免用户取消强依赖条件所需的依赖项
    setForceChecked(forceState);

    // 依赖项是所有 crowd 条件的勾选状态拼接成的字符串（用以监听变化）
  }, [crowdList.map(item => crowdConditionField.getValue(`crowdInfo_${item.id}`)).join('|')]);

  function renderCheckbox(item) {
    // 获取当前条件项是否已勾选
    const isChecked = crowdConditionField.getValue(`crowdInfo_${item.id}`);

    // 如果当前项是"订单周期"或"订单状态"，且被强制依赖（不可取消勾选），则禁用复选框
    const disabled =
      ['tradeCycleCrowdImpl', 'tradeStatusCrowdImpl'].includes(item.interfaceClass) && forceChecked[item.id];

    // 渲染一个复选框组件
    return (
      <Checkbox
        name={`crowdInfo_${item.id}`} // 表单字段名
        checked={isChecked} // 当前是否勾选
        disabled={disabled} // 是否禁用（根据是否是被强依赖的项）
        onChange={val => {
          // 用户手动切换勾选状态
          crowdConditionField.setValue(`crowdInfo_${item.id}`, val);

          // 如果取消了"购买指定商品"这一条件，则同时清空已选择的商品列表
          if (!val && item.interfaceClass === 'tradeSkuCrowdImpl') {
            crowdConditionField.setValue(`crowdInfo_${item.id}_skuList`, []);
          }
        }}
      >
        {item.crowdName /* 显示条件名称 */}
      </Checkbox>
    );
  }
  // 辅助函数：获取时间范围（时间类型人群条件专用）
  function getRangeValue(itemId) {
    // 取出开始和结束时间
    const s = crowdConditionField.getValue(`crowdInfo_${itemId}_startTime`);
    const e = crowdConditionField.getValue(`crowdInfo_${itemId}_endTime`);

    // 如果任一时间为空，则返回空数组，表示未选择时间范围
    if (!s || !e) return [];

    // 返回 dayjs 对象数组，供 RangePicker 使用
    return [dayjs(s), dayjs(e)];
  }

  // 辅助函数：设置时间范围（当 RangePicker 的值发生变化时调用）
  function handleRangeChange(val, itemId) {
    // 如果两个时间都有值且都不是 void（null、undefined、空字符串等）
    if (val.every(v => !isVoid(v))) {
      // 格式化并更新表单字段
      crowdConditionField.setValue(`crowdInfo_${itemId}_startTime`, dayjs(val[0]).format('YYYY-MM-DD HH:mm:ss'));
      crowdConditionField.setValue(`crowdInfo_${itemId}_endTime`, dayjs(val[1]).format('YYYY-MM-DD HH:mm:ss'));
    } else {
      // 否则清空起止时间，防止残留无效值
      crowdConditionField.setValue(`crowdInfo_${itemId}_startTime`, '');
      crowdConditionField.setValue(`crowdInfo_${itemId}_endTime`, '');
    }
  }

  // 辅助：订单周期第二个 time range
  function getTradeCycleRangeValue(itemId) {
    const s = crowdConditionField.getValue(`crowdInfo_${itemId}_checkList_1_startTime`);
    const e = crowdConditionField.getValue(`crowdInfo_${itemId}_checkList_1_endTime`);
    if (!s || !e) return [];
    return [dayjs(s), dayjs(e)];
  }
  function handleTradeCycleRangeChange(val, itemId) {
    if (val.every(v => !isVoid(v))) {
      crowdConditionField.setValue(
        `crowdInfo_${itemId}_checkList_1_startTime`,
        dayjs(val[0]).format('YYYY-MM-DD HH:mm:ss'),
      );
      crowdConditionField.setValue(
        `crowdInfo_${itemId}_checkList_1_endTime`,
        dayjs(val[1]).format('YYYY-MM-DD HH:mm:ss'),
      );
    } else {
      crowdConditionField.setValue(`crowdInfo_${itemId}_checkList_1_startTime`, '');
      crowdConditionField.setValue(`crowdInfo_${itemId}_checkList_1_endTime`, '');
    }
  }

  // 点击"确定"
  function handleOk() {
    crowdConditionField.validate((errors, values) => {
      if (errors) return;
      const errorMessages = [];
      const updatedCrowdList = crowdList.map(item => {
        const checked = values[`crowdInfo_${item.id}`];

        // 针对时间类型
        if (['joinMemberDateCrowdImpl', 'lastTradeDateCrowdImpl'].includes(item.interfaceClass)) {
          let newStartTime = '';
          let newEndTime = '';
          if (checked) {
            newStartTime = values[`crowdInfo_${item.id}_startTime`] || '';
            newEndTime = values[`crowdInfo_${item.id}_endTime`] || '';
            if (!newStartTime || !newEndTime) {
              errorMessages.push(`请填写完整的【${item.crowdName}】时间区间`);
            }
          }
          return {
            ...item,
            configJson: JSON.stringify({
              isChecked: checked,
              startTime: checked ? newStartTime : item.parsedConfig.startTime,
              endTime: checked ? newEndTime : item.parsedConfig.endTime,
            }),
          };
        } else if (item.interfaceClass === 'gradeLevelCrowdImpl') {
          // 会员等级
          let newCheckList = [];
          if (checked) {
            newCheckList = (item.parsedConfig?.checkList || []).map((opt, index) => ({
              ...opt,
              isChecked: !!values[`crowdInfo_${item.id}_opt_${index}`],
            }));
            if (!newCheckList.some(opt => opt.isChecked)) {
              errorMessages.push(`请至少选择一个【${item.crowdName}】的选项`);
            }
          }
          return {
            ...item,
            configJson: JSON.stringify({
              isChecked: checked,
              checkList: checked ? newCheckList : item.parsedConfig.checkList,
            }),
          };
        } else if (item.interfaceClass === 'tradeCycleCrowdImpl') {
          let newCheckList = (item.parsedConfig?.checkList || []).map((sub, idx) => {
            if (idx === 0) {
              return {
                ...sub,
                isChecked: !!values[`crowdInfo_${item.id}_checkList_0_isChecked`],
                inDays: values[`crowdInfo_${item.id}_checkList_0_inDays`],
              };
            }
            if (idx === 1) {
              const st = values[`crowdInfo_${item.id}_checkList_1_startTime`] || '';
              const et = values[`crowdInfo_${item.id}_checkList_1_endTime`] || '';
              return {
                ...sub,
                isChecked: !!values[`crowdInfo_${item.id}_checkList_1_isChecked`],
                startTime: st,
                endTime: et,
              };
            }
            return sub;
          });
          if (checked) {
            const sub0 = newCheckList[0];
            const sub1 = newCheckList[1];
            if (!sub0.isChecked && !sub1.isChecked) {
              errorMessages.push(`请选择【${item.crowdName}】的订单周期条件`);
            }
            if (sub1.isChecked && (!sub1.startTime || !sub1.endTime)) {
              errorMessages.push(`请填写完整的【${item.crowdName}】指定时间区间`);
            }
          }
          return {
            ...item,
            configJson: JSON.stringify({
              isChecked: checked,
              checkList: newCheckList,
            }),
          };
        } else if (item.interfaceClass === 'tradeStatusCrowdImpl') {
          let newCheckList = (item.parsedConfig?.checkList || []).map((sub, idx) => {
            if (idx === 0) {
              return { ...sub, isChecked: !!values[`crowdInfo_${item.id}_checkList_0_isChecked`] };
            }
            if (idx === 1) {
              return { ...sub, isChecked: !!values[`crowdInfo_${item.id}_checkList_1_isChecked`] };
            }
            return sub;
          });
          if (checked && !newCheckList[0].isChecked && !newCheckList[1].isChecked) {
            errorMessages.push(`请选择【${item.crowdName}】的订单状态条件`);
          }
          return {
            ...item,
            configJson: JSON.stringify({
              isChecked: checked,
              checkList: newCheckList,
            }),
          };
        } else if (item.interfaceClass === 'tradePaymentCrowdImpl') {
          if (checked && isVoid(values[`crowdInfo_${item.id}_countPayment`])) {
            errorMessages.push(`请填写【${item.crowdName}】的累计订单金额`);
          }
          return {
            ...item,
            configJson: JSON.stringify({
              isChecked: checked,
              countPayment: checked ? values[`crowdInfo_${item.id}_countPayment`] : item.parsedConfig.countPayment,
            }),
          };
        } else if (item.interfaceClass === 'tradeNumCrowdImpl') {
          if (checked && isVoid(values[`crowdInfo_${item.id}_countNum`])) {
            errorMessages.push(`请填写【${item.crowdName}】的累计订单数`);
          }
          return {
            ...item,
            configJson: JSON.stringify({
              isChecked: checked,
              countNum: checked ? values[`crowdInfo_${item.id}_countNum`] : item.parsedConfig.countNum,
            }),
          };
        } else if (item.interfaceClass === 'tradeSkuCrowdImpl') {
          // 购买指定商品
          const skuList = values[`crowdInfo_${item.id}_skuList`] || [];
          if (checked && skuList.length === 0) {
            errorMessages.push(`请至少选择一件【${item.crowdName}】`);
          }
          return {
            ...item,
            configJson: JSON.stringify({
              isChecked: checked,
              skuList: checked ? skuList : item.parsedConfig.skuList,
            }),
          };
        } else {
          // 其他类型
          return {
            ...item,
            configJson: JSON.stringify({ isChecked: checked }),
          };
        }
      });

      // 必须至少填写一个条件
      const anyConditionChecked = updatedCrowdList.some(item => {
        const config = JSON.parse(item.configJson || '{}');
        return config.isChecked;
      });
      if (!anyConditionChecked) {
        showErrorMessageDialog(['请至少填写一项人群条件']);
        return;
      }

      if (errorMessages.length > 0) {
        showErrorMessageDialog(errorMessages);
        return;
      }
      values.crowdInfo = updatedCrowdList;
      onResolve(values);
    });
  }
  function handleCancel() {
    onResolve(null);
  }

  return (
    <Box className="crowd-condition-modal-content">
      <Box
        className="crowd-condition-modal-content-top"
        padding={15}
      >
        <Form
          field={crowdConditionField}
          className="crowd-name-form"
        >
          <Form.Item
            name="crowdName"
            label="人群名称"
            required
            requiredMessage="人群名称不能为空"
          >
            <Input
              trim
              composition
              showLimitHint
              maxLength={50}
            />
          </Form.Item>
        </Form>
        <Message type="warning">注意：所有条件默认取交集，即：满足所配置全部条件的用户视为可参与活动</Message>
      </Box>

      <Box
        className="crowd-list"
        padding={[0, 15]}
      >
        {/* 用户条件 */}
        <Container
          title="用户条件"
          style={{ margin: 0, padding: '20px 10px 0' }}
        >
          <Form
            field={crowdConditionField}
            className="crowd-info-form"
          >
            {crowdList
              .filter(item => item.configType === 1)
              .map(item => {
                const isChecked = crowdConditionField.getValue(`crowdInfo_${item.id}`);
                return (
                  <Form.Item
                    label=" "
                    size="medium"
                    key={item.id}
                  >
                    {renderCheckbox(item)}
                    {item.remark && <span>{item.remark}</span>}
                    {['joinMemberDateCrowdImpl', 'lastTradeDateCrowdImpl'].includes(item.interfaceClass) &&
                      isChecked && (
                        <DatePicker2.RangePicker
                          style={{ width: 320, marginLeft: 12 }}
                          showTime
                          format="YYYY-MM-DD HH:mm:ss"
                          outputFormat="YYYY-MM-DD HH:mm:ss"
                          timePanelProps={{ defaultValue: ['00:00:00', '23:59:59'] }}
                          value={getRangeValue(item.id)}
                          onChange={val => handleRangeChange(val, item.id)}
                        />
                      )}
                    {item.interfaceClass === 'gradeLevelCrowdImpl' && isChecked && (
                      <div style={{ display: 'inline-block', marginLeft: 12 }}>
                        {(item.parsedConfig?.checkList || []).map((opt, index) => (
                          <Checkbox
                            key={index}
                            checked={crowdConditionField.getValue(`crowdInfo_${item.id}_opt_${index}`)}
                            onChange={val => crowdConditionField.setValue(`crowdInfo_${item.id}_opt_${index}`, val)}
                            style={{ marginRight: 12 }}
                          >
                            {opt.lable}
                          </Checkbox>
                        ))}
                      </div>
                    )}
                  </Form.Item>
                );
              })}
          </Form>
        </Container>

        {/* 交易条件 */}
        <Container
          title="交易条件"
          style={{ margin: 0, padding: '0 10px' }}
        >
          <Form
            field={crowdConditionField}
            className="crowd-info-form"
          >
            {crowdList
              .filter(item => item.configType === 2)
              .map(item => {
                const isChecked = crowdConditionField.getValue(`crowdInfo_${item.id}`);
                return (
                  <Form.Item
                    label=" "
                    size="medium"
                    key={item.id}
                  >
                    {renderCheckbox(item)}

                    {/* 订单周期 */}
                    {item.interfaceClass === 'tradeCycleCrowdImpl' && isChecked && (
                      <Box>
                        <Radio
                          checked={crowdConditionField.getValue(`crowdInfo_${item.id}_checkList_0_isChecked`)}
                          onChange={val => {
                            crowdConditionField.setValue(`crowdInfo_${item.id}_checkList_0_isChecked`, val);
                            if (val) {
                              crowdConditionField.setValue(`crowdInfo_${item.id}_checkList_1_isChecked`, false);
                            }
                          }}
                        >
                          在
                          <NumberInput
                            min={1}
                            style={{ width: 80, margin: '0 4px' }}
                            value={crowdConditionField.getValue(`crowdInfo_${item.id}_checkList_0_inDays`)}
                            onChange={v => crowdConditionField.setValue(`crowdInfo_${item.id}_checkList_0_inDays`, v)}
                          />
                          天内累计订单
                        </Radio>
                        <Radio
                          checked={crowdConditionField.getValue(`crowdInfo_${item.id}_checkList_1_isChecked`)}
                          onChange={val => {
                            crowdConditionField.setValue(`crowdInfo_${item.id}_checkList_1_isChecked`, val);
                            if (val) {
                              crowdConditionField.setValue(`crowdInfo_${item.id}_checkList_0_isChecked`, false);
                            }
                          }}
                          style={{ marginTop: 12 }}
                        >
                          指定时间内累计订单
                          <DatePicker2.RangePicker
                            style={{ width: 320, marginLeft: 12 }}
                            showTime
                            format="YYYY-MM-DD HH:mm:ss"
                            outputFormat="YYYY-MM-DD HH:mm:ss"
                            timePanelProps={{ defaultValue: ['00:00:00', '23:59:59'] }}
                            value={getTradeCycleRangeValue(item.id)}
                            onChange={val => handleTradeCycleRangeChange(val, item.id)}
                          />
                        </Radio>
                      </Box>
                    )}

                    {/* 订单状态 */}
                    {item.interfaceClass === 'tradeStatusCrowdImpl' && isChecked && (
                      <Box direction="row">
                        <Radio
                          checked={crowdConditionField.getValue(`crowdInfo_${item.id}_checkList_0_isChecked`)}
                          onChange={val => {
                            crowdConditionField.setValue(`crowdInfo_${item.id}_checkList_0_isChecked`, val);
                            if (val) {
                              crowdConditionField.setValue(`crowdInfo_${item.id}_checkList_1_isChecked`, false);
                            }
                          }}
                        >
                          {item.parsedConfig?.checkList[0]?.lable}
                        </Radio>
                        <Radio
                          checked={crowdConditionField.getValue(`crowdInfo_${item.id}_checkList_1_isChecked`)}
                          onChange={val => {
                            crowdConditionField.setValue(`crowdInfo_${item.id}_checkList_1_isChecked`, val);
                            if (val) {
                              crowdConditionField.setValue(`crowdInfo_${item.id}_checkList_0_isChecked`, false);
                            }
                          }}
                          style={{ marginLeft: 12 }}
                        >
                          {item.parsedConfig?.checkList[1]?.lable}
                        </Radio>
                      </Box>
                    )}

                    {/* 累计订单金额 */}
                    {item.interfaceClass === 'tradePaymentCrowdImpl' && isChecked && (
                      <Box>
                        <NumberInput
                          min={1}
                          value={crowdConditionField.getValue(`crowdInfo_${item.id}_countPayment`)}
                          onChange={value => crowdConditionField.setValue(`crowdInfo_${item.id}_countPayment`, value)}
                          innerAfter="元"
                          style={{ width: 150 }}
                        />
                      </Box>
                    )}

                    {/* 累计订单数 */}
                    {item.interfaceClass === 'tradeNumCrowdImpl' && isChecked && (
                      <Box>
                        <NumberInput
                          min={1}
                          value={crowdConditionField.getValue(`crowdInfo_${item.id}_countNum`)}
                          onChange={value => crowdConditionField.setValue(`crowdInfo_${item.id}_countNum`, value)}
                          innerAfter="笔"
                          style={{ width: 150 }}
                        />
                      </Box>
                    )}

                    {/* 购买指定商品 */}
                    {item.interfaceClass === 'tradeSkuCrowdImpl' && isChecked && (
                      <Box>
                        <ProductPicker
                          min={1}
                          max={500}
                          selectedItems={(crowdConditionField.getValue(`crowdInfo_${item.id}_skuList`) || []).map(
                            numIid => ({ numIid }),
                          )}
                          onSelectedProducts={selected => {
                            // 从 selected 中抽取 numIid 构成 skuList 数组
                            const skuList = selected.map(product => product.numIid);
                            crowdConditionField.setValue(`crowdInfo_${item.id}_skuList`, skuList);
                          }}
                        />
                      </Box>
                    )}
                  </Form.Item>
                );
              })}
          </Form>
        </Container>
      </Box>

      {/* 按钮区 */}
      <Box
        direction="row"
        justify="center"
        spacing={16}
        padding={15}
        className="crowd-condition-modal-content-footer"
      >
        <Button
          type="primary"
          onClick={handleOk}
        >
          确定
        </Button>
        <Button
          type="normal"
          onClick={handleCancel}
        >
          取消
        </Button>
      </Box>
    </Box>
  );
}

export function CrowdSummaryDesc({ crowdName, crowdInfo = [], extra }) {
  if (!Array.isArray(crowdInfo)) return null;

  const lines = [];

  for (const item of crowdInfo) {
    const config = JSON.parse(item.configJson || '{}');
    if (!config.isChecked) continue;

    switch (item.interfaceClass) {
      case 'joinMemberDateCrowdImpl':
      case 'lastTradeDateCrowdImpl':
        if (config.startTime && config.endTime) {
          lines.push(`${item.crowdName} ${config.startTime} ~ ${config.endTime}`);
        }
        break;
      case 'gradeLevelCrowdImpl': {
        const levels = config.checkList
          ?.filter(opt => opt.isChecked)
          .map(opt => opt.lable)
          .join('、');
        if (levels) {
          lines.push(`${item.crowdName}：${levels}`);
        }
        break;
      }
      case 'tradeCycleCrowdImpl': {
        const cycle = [];
        if (config.checkList?.[0]?.isChecked && config.checkList[0].inDays) {
          cycle.push(`在${config.checkList[0].inDays}天内`);
        }
        if (config.checkList?.[1]?.isChecked && config.checkList[1].startTime && config.checkList[1].endTime) {
          cycle.push(`${config.checkList[1].startTime} ~ ${config.checkList[1].endTime}`);
        }
        if (cycle.length) {
          lines.push(`${item.crowdName}：${cycle.join('，')}`);
        }
        break;
      }
      case 'tradeStatusCrowdImpl': {
        const status = config.checkList
          ?.filter(opt => opt.isChecked)
          .map(opt => opt.lable)
          .join('、');
        if (status) {
          lines.push(`${item.crowdName}：${status}`);
        }
        break;
      }
      case 'tradePaymentCrowdImpl':
        if (config.countPayment != null) {
          lines.push(`${item.crowdName} ≥ ${config.countPayment}元`);
        }
        break;
      case 'tradeNumCrowdImpl':
        if (config.countNum != null) {
          lines.push(`${item.crowdName} ≥ ${config.countNum}单`);
        }
        break;
      case 'tradeSkuCrowdImpl': {
        const skuLen = config.skuList?.length || 0;
        if (skuLen > 0) {
          lines.push(`${item.crowdName} 已选${skuLen}件商品`);
        }
        break;
      }
      default:
        lines.push(item.crowdName);
    }
  }

  if (!lines.length) return null;

  return (
    <Card
      title={crowdName}
      showTitleBullet={false}
      contentHeight={130}
      subTitle={`（共${lines.length}项）`}
      extra={extra}
      style={{ minWidth: 300 }}
    >
      {lines.map((line, index) => (
        <p
          key={index}
          style={{ lineHeight: 2, fontSize: 14 }}
        >
          {line}
        </p>
      ))}
    </Card>
  );
}
