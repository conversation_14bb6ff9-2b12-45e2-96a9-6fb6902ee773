[{"configJson": "{\"isChecked\":false}", "interfaceClass": "newUserCrowdImpl", "orderBy": 1, "remark": "(近3月内无支付成功订单即为新客，支付后退款视为未支付)", "id": 1, "configType": 1, "activityType": "1", "crowdName": "新客户"}, {"configJson": "{\"isChecked\":false}", "interfaceClass": "oldUserCrowdImpl", "orderBy": 2, "remark": "(近3月内有支付成功订单即为老客，支付后退款视为未支付)", "id": 2, "configType": 1, "activityType": "1", "crowdName": "老客户"}, {"configJson": "{\"isChecked\":false,\"startTime\":\"\",\"endTime\":\"\"}", "interfaceClass": "joinMemberDateCrowdImpl", "orderBy": 3, "remark": null, "id": 3, "configType": 1, "activityType": "1", "crowdName": "入会时间"}, {"configJson": "{\"isChecked\":false,\"startTime\":\"\",\"endTime\":\"\"}", "interfaceClass": "lastTradeDateCrowdImpl", "orderBy": 4, "remark": null, "id": 4, "configType": 1, "activityType": "1", "crowdName": "最后一次下单时间"}, {"configJson": "{\"checkList\":[{\"grade\":0,\"lable\":\"非会员\",\"isChecked\":false},{\"grade\":1,\"lable\":\"普通会员\",\"isChecked\":false},{\"grade\":2,\"lable\":\"金卡\",\"isChecked\":false},{\"grade\":3,\"lable\":\"黑卡\",\"isChecked\":false}],\"isChecked\":false}", "interfaceClass": "gradeLevelCrowdImpl", "orderBy": 5, "remark": null, "id": 5, "configType": 1, "activityType": "1", "crowdName": "会员等级"}, {"configJson": "{\"isChecked\":false,\"checkList\":[{\"inDays\":0,\"isChecked\":false},{\"startTime\":\"\",\"endTime\":\"\",\"isChecked\":false}]}", "interfaceClass": "tradeCycleCrowdImpl", "orderBy": 6, "remark": null, "id": 6, "configType": 2, "activityType": "1", "crowdName": "订单周期"}, {"configJson": "{\"isChecked\":false,\"checkList\":[{\"lable\":\"已付款未关闭\",\"isChecked\":false},{\"lable\":\"已收货\",\"isChecked\":false}]}", "interfaceClass": "tradeStatusCrowdImpl", "orderBy": 7, "remark": null, "id": 7, "configType": 2, "activityType": "1", "crowdName": "订单状态"}, {"configJson": "{\"isChecked\":false,\"countPayment\":\"\"}", "interfaceClass": "tradePaymentCrowdImpl", "orderBy": 8, "remark": null, "id": 8, "configType": 2, "activityType": "1", "crowdName": "累计订单金额"}, {"configJson": "{\"isChecked\":false,\"countNum\":\"\"}", "interfaceClass": "tradeNumCrowdImpl", "orderBy": 9, "remark": null, "id": 9, "configType": 2, "activityType": "1", "crowdName": "累计订单数"}, {"configJson": "{\"isChecked\":false,\"skuList\":[]}", "interfaceClass": "tradeSkuCrowdImpl", "orderBy": 10, "remark": null, "id": 10, "configType": 2, "activityType": "1", "crowdName": "购买指定商品"}]