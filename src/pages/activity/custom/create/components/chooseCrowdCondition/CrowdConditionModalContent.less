
.choose-crowd-condition-dialog {
  display: inline-flex !important;
  flex-direction: column;

  .next-dialog-body {
    flex: 1;
    padding: 0 !important;
  }
}

.crowd-condition-modal-content {
  height: 100%;
}

.crowd-condition-modal-content-top {
  border-bottom: 1px solid #ccc;
}

.crowd-list {
  flex: 1;
  overflow-y: auto;
}

.crowd-condition-modal-content-footer {
  border-top: 1px solid #ccc;
}

.crowd-name-form {
  flex: 1;

  .next-form-item {
    display: flex;
  }

  .next-form-item-label {
    flex-shrink: 0;
    width: 80px;
  }

  .next-form-item-control {
    flex: 1;
  }

  .form-item-text {
    line-height: 42px;
  }
}

.crowd-info-form {
  flex: 1;

  .next-form-item {
    display: flex;
  }

  .next-form-item-label {
    flex-shrink: 0;
    width: 20px;
  }

  .next-form-item-control {
    display: flex;
    flex: 1;
    align-items: baseline;
  }

  .form-item-text {
    line-height: 42px;
  }
}
