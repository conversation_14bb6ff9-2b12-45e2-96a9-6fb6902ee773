import React, { useState } from 'react';
import { Dialog, Button, Box, Input, Pagination, Icon } from '@alifd/next';

/**
 * 已选商品管理弹窗
 * @param {Object} props
 * @param {Array} props.items - 当前已选商品列表
 * @param {Function} props.onSave - 用户点击“保存”时，将最新列表回传
 * @param {Function} props.onClose - 用户点击关闭弹窗
 * @param {String} props.itemIdentifier - 商品唯一标识字段
 */
export default function SelectedProductsDialog({ items, onSave, onClose, itemIdentifier, disabled }) {
  const [searchValue, setSearchValue] = useState('');
  const [filteredList, setFilteredList] = useState(items); // 当前搜索过滤后的商品列表
  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(10); // 默认 10 个商品 1 页

  // 根据 searchValue 对原列表进行过滤
  // 支持通过英文逗号分隔多个关键字，关键字可匹配 title 或 numIid
  function doFilter(value, rawList) {
    const words = value
      .split(',')
      .map(str => str.trim())
      .filter(Boolean);
    if (words.length === 0) {
      return rawList;
    }
    return rawList.filter(item => {
      // 如果任何一个 word 匹配到，就保留
      return words.some(word => {
        // 模糊匹配 numIid 或 title
        const numIidStr = String(item[itemIdentifier] || '');
        const titleStr = String(item.title || '');
        return numIidStr.includes(word) || titleStr.includes(word);
      });
    });
  }

  // 初始化时即可做一次过滤（若不想自动过滤可忽略）
  React.useEffect(() => {
    setFilteredList(doFilter(searchValue, items));
  }, [items]);

  // 当用户输入查询关键字
  function handleSearchChange(value) {
    setSearchValue(value);
  }

  // 点击查询
  function handleSearch() {
    const flist = doFilter(searchValue, items);
    setFilteredList(flist);
    setPageNo(1);
  }

  // 清空全部商品
  function handleClearAll() {
    setFilteredList([]);
    setSearchValue('');
    setPageNo(1);
  }

  // 删除单个商品
  function handleRemove(product) {
    const updated = filteredList.filter(p => p[itemIdentifier] !== product[itemIdentifier]);
    setFilteredList(updated);
  }

  // 用户点击“保存”，将编辑后的列表传给父层
  function handleSave() {
    onSave(filteredList);
    onClose();
  }

  // 分页处理
  const startIndex = (pageNo - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const pageData = filteredList.slice(startIndex, endIndex);

  return (
    <Dialog
      v2
      title="已选商品"
      visible
      width="auto"
      onClose={onClose}
      className="products-dialog"
      footer={false}
    >
      <Box
        spacing={16}
        className="product-content"
      >
        {/* 搜索栏 */}
        <Box
          direction="row"
          spacing={10}
          margin={[0, 0, 10, 0]}
        >
          <Input
            placeholder="请输入关键词或商品ID，多个商品用','分隔"
            value={searchValue}
            onChange={handleSearchChange}
            style={{ width: 300 }}
            trim
            composition
          />
          <Button onClick={handleSearch}>查询</Button>
          <Button
            onClick={handleClearAll}
            disabled={disabled}
          >
            清空全部商品
          </Button>
        </Box>

        <div className="product-grid-list">
          {pageData.map(product => (
            <div
              key={product.numIid}
              className={'product-item'}
            >
              <div className="product-img">
                <img
                  src={product.picUrl}
                  alt={product.title}
                />
                {/* 删除按钮 */}
                <Button
                  className="delete-product-item-icon"
                  type="primary"
                  disabled={disabled}
                  text
                  iconSize="small"
                  onClick={() => handleRemove(product)}
                >
                  <Icon type="ashbin" />
                </Button>
              </div>
              <span className="product-title">{product.title}</span>
              <div className="product-info">
                <span className="product-price" />
                <div className="product-stock">
                  销量
                  <span className="product-stock-num">{product.sellNum || 0}</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        <Box
          direction="row"
          justify="space-between"
          align="center"
          padding={[15, 0]}
        >
          <Pagination
            current={pageNo}
            pageSize={pageSize}
            total={filteredList.length}
            shape={'arrow-only'}
            totalRender={total => `已选商品数： ${total} `}
            onChange={page => setPageNo(page)}
            onPageSizeChange={size => {
              setPageSize(size);
              setPageNo(1);
            }}
            pageSizeSelector="dropdown"
            pageSizePosition="end"
          />

          {/* 弹窗底部按钮 */}
          <Box
            direction="row"
            justify="end"
            spacing={10}
          >
            <Button onClick={onClose}>取消</Button>
            <Button
              type="primary"
              disabled={disabled}
              onClick={handleSave}
            >
              保存
            </Button>
          </Box>
        </Box>
      </Box>
    </Dialog>
  );
}
