.products-dialog {
  display: inline-flex !important;
  flex-direction: column;

  .next-dialog-body {
    flex: 1;
  }
}

.product-content {
  height: 100%;
}

.product-selected-num {
  color: var(--color-primary);
}

.product-grid-list {
  display: grid;
  flex: 1;
  grid-template-columns: repeat(5, 1fr);
  gap: 15px;
  overflow-y: auto;
  place-items: flex-start;
  min-width: 1000px;
  min-height: 300px;
}

.product-item {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 184px;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 12px;

  .delete-product-item-icon {
    position: absolute;
    top: 5px;
    right: 5px;
    color: #999;
  }

  &.disabled {
    background-color: #f1f1f1;
    cursor: not-allowed;

    .product-img {
      cursor: not-allowed;
    }
  }

  &.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 5px var(--primary-color-light);
  }

  .product-img {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 168px;
    height: 168px;
    overflow: hidden;
    border-radius: 12px;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .checkbox {
      position: absolute;
      top: 5px;
      right: 5px;
    }
  }

  .product-title {
    display: -webkit-box;
    width: 100%;
    height: 36px;
    margin: 6px 0;
    padding: 0 6px;
    overflow: hidden;
    color: rgb(17 17 17);
    font-size: 12px;
    line-height: 18px;
    text-overflow: ellipsis;
    word-break: break-all;
    line-clamp: 2;
    -webkit-line-clamp: 2;
    box-orient: vertical;
  }

  .product-info {
    display: flex;
    justify-content: space-between;
    padding: 0 6px;
  }

  .product-price {
    color: rgb(255 80 0);
    font-size: 16px;
  }

  .product-stock {
    display: flex;
    align-items: center;
    color: rgb(153 153 153);
    font-size: 12px;
  }

  .product-stock-num {
    margin-left: 5px;
  }
}