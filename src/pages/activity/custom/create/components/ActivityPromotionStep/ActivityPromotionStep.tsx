import { useEffect, useState } from 'react';
import { Button, Message } from '@alifd/next';
import Container from '@/components/Container';
import FixedBottomBox from '@/components/FixedBottomBox/FixedBottomBox';
import PromotionChannel from '../PromotionChannel/PromotionChannel';
import './ActivityPromotionStep.scss';
import { activityCustomizeActivityGetActivityInfo } from '@/api/b';
import { history } from 'ice';
import { QRCodeSVG } from 'qrcode.react';
import constant from '@/utils/constant';

const defaultQRCodeUrl = {
  45693550: 'https://m.zjbyte.net/share/douyin/?token=RjQ1M0FBRDZGMDZCNzZleW96bzh4&share_channel=scan',
  20421521: 'https://m.zjbyte.net/share/douyin/?token=QTYwMURCQTRBQzcwNzZleW96cDBw&share_channel=scan',
  7778309: 'https://m.zjbyte.net/share/douyin/?token=QjU3MzE2RjE5ODRFNzZleW96c2V4&share_channel=scan',
};

export default function ActivityPromotionStep(props) {
  const { field } = props;
  const appInfo = JSON.parse(localStorage.getItem(constant.LZ_CURRENT_SHOP) || '{}');

  const activityId = field.getValue('baseInfo.activityId');
  const AF_Act_Is_Create = field.getValue('AF_Act_Is_Create');

  const [activityInfo, setActivityInfo] = useState<any>(null);

  useEffect(() => {
    fetchActivityInfo();
  }, []);

  async function fetchActivityInfo() {
    try {
      const res = await activityCustomizeActivityGetActivityInfo({ activityId });
      setActivityInfo(res);
    } catch (e) {
      Message.error(e.message);
    }
  }

  if (!activityId || !activityInfo) return null;
  return (
    <div className="activity-promotion">
      <Container>
        <div className="qr-img-container">
          {/* <img className="qr-img" src={activityInfo?.qrCodeUrl} /> */}
          <QRCodeSVG
            value={activityInfo?.actUrl ? activityInfo?.actUrl : defaultQRCodeUrl[appInfo.shopId]}
            className="qr-img"
          />
          <div className="qr-img-des">
            <p className="qr-img-message">{`活动${AF_Act_Is_Create ? '创建' : '更新'}成功`}</p>
            <p className="qr-img-tips text-primary">
              重要提示：您的活动属于无线互动类，需选择推广渠道并投放，活动才能透出哦~
            </p>
            <p className="qr-img-use-taobao">
              <img src="https://img.alicdn.com/imgextra/i1/155168396/O1CN01qZhZQI2BtQ7srifM1_!!155168396.png" />
              <span>请使用抖音APP扫一扫预览</span>
            </p>
          </div>
        </div>
      </Container>
      <Container
        title="推广渠道"
        subtitle={
          <Button
            type="primary"
            text
            onClick={() => window.open('https://www.yuque.com/luzekejialiyewu/rrwrq6/tlpc9t', '_blank')}
          >
            {'推广渠道介绍 >'}
          </Button>
        }
      >
        <PromotionChannel />
      </Container>
      <FixedBottomBox>
        <Button
          type="primary"
          size="large"
          onClick={() => {
            history?.push('/activity/list');
          }}
        >
          暂不投放
        </Button>
      </FixedBottomBox>
    </div>
  );
}
