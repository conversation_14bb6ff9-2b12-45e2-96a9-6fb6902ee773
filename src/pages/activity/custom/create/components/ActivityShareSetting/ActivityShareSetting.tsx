import { Box, Form, Input } from '@alifd/next';
import ImgUploadFromItem from '@/components/ImgUpload';
export default function ActivityShareSetting({ field }) {
  const AF_Act_Is_View = field.getValue('AF_Act_Is_View');

  return (
    <Box padding={[20, 0, 0]}>
      <Form.Item
        name="AF_baseInfo_shareTitle"
        label="分享标题"
        required
        requiredMessage={'分享标题不能为空'}
        disabled={AF_Act_Is_View}
      >
        <Input
          maxLength={20}
          showLimitHint
          trim
          composition
          value={field.getValue('baseInfo.shareTitle')}
          onChange={val => field.setValue('baseInfo.shareTitle', val)}
          style={{ width: 'var(--activity-form-item-width)' }}
        />
      </Form.Item>
      <Form.Item
        name="AF_baseInfo_shareContent"
        label="分享内容"
        required
        requiredMessage={'分享内容不能为空'}
        disabled={AF_Act_Is_View}
      >
        <Input.TextArea
          maxLength={50}
          rows={3}
          cutString
          showLimitHint
          trim
          composition
          placeholder="请输入"
          style={{ width: 490 }}
          value={field.getValue('baseInfo.shareContent')}
          onChange={val => field.setValue('baseInfo.shareContent', val)}
        />
      </Form.Item>
      <ImgUploadFromItem
        name="AF_baseInfo_shareImg"
        label="分享图片"
        required
        requiredMessage="分享图片不能为空"
        disabled={AF_Act_Is_View}
        img={{
          value: field.getValue('baseInfo.shareImage'),
          width: 512,
          height: 512,
        }}
        onSuccess={url => {
          field.setValue('baseInfo.shareImage', url);
          field.validate('AF_baseInfo_shareImg');
          field.setError('AF_baseInfo_shareImg', '');
        }}
        onReset={url => {
          field.setValue('baseInfo.shareImage', url);
        }}
      />
    </Box>
  );
}
