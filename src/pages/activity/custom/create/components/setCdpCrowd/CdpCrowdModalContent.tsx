import React from 'react';
import { Form, Field, Input, Message, Button, Box } from '@alifd/next';
import './CdpCrowdModalContent.scss';

export default function CdpCrowdModalContent({ initialValues, onResolve }) {
  const cdpCrowdField = Field.useField({ values: initialValues });

  // 点击“保存”
  function handleOk() {
    cdpCrowdField.validate((errors, values) => {
      if (!errors) {
        onResolve(values);
      }
    });
  }

  // 点击“取消”或关闭弹窗
  function handleCancel() {
    onResolve(null);
  }

  return (
    <Box>
      <Form
        field={cdpCrowdField}
        className="cdp-crowd-form"
      >
        <Form.Item
          name="crowdName"
          label="人群名称"
          required
          extra={<p className="form-extra">仅做标识显示用，不参与业务逻辑</p>}
          requiredMessage="请输入人群名称"
        >
          <Input
            placeholder="请输入人群名称"
            trim
            maxLength={50}
            showLimitHint
            composition
          />
        </Form.Item>

        <Form.Item
          name="cdpCrowdId"
          label="人群id"
          required
          extra={<p className="form-extra">请务必输入CDP中的人群id，否则将造成门槛校验错误</p>}
          requiredMessage="请输入CDP人群id"
        >
          <Input
            placeholder="请输入CDP人群id"
            trim
            composition
          />
        </Form.Item>

        <Box
          direction="row"
          justify="center"
          spacing={10}
        >
          <Button onClick={handleCancel}>取消</Button>
          <Button
            type="primary"
            onClick={handleOk}
          >
            保存
          </Button>
        </Box>
      </Form>
    </Box>
  );
}
