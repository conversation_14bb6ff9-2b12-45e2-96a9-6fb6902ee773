import { Dialog } from '@alifd/next';
import React from 'react';
import CdpCrowdModalContent from './CdpCrowdModalContent';
import { addDialogRef } from '@/utils/dialogMapper';

export default function setCdpCrowd(initialValues = {}) {
  return new Promise(resolve => {
    const dialogRef = Dialog.show({
      v2: true,
      title: '配置CDP人群',
      width: 500,
      centered: true,
      closeMode: ['close'],
      content: (
        <CdpCrowdModalContent
          initialValues={initialValues}
          onResolve={result => {
            resolve(result);
            dialogRef.hide();
          }}
        />
      ),
      footer: false,
    });
    addDialogRef(dialogRef);
  });
}
