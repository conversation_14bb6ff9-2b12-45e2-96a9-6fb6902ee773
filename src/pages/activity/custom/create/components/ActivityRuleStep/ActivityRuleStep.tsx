// 活动规则设置
import { Box, Button, Form, Input } from '@alifd/next';
import Container from '@/components/Container';
import FixedBottomBox from '@/components/FixedBottomBox/FixedBottomBox';

import generateActivityRule from './generateActivityRule';
import { showErrorMessageDialog } from '@/utils';

export default function ActivityRuleStep(props) {
  const { goNextStep, goPrevStep, field } = props;
  const AF_Act_Is_View = field.getValue('AF_Act_Is_View');

  function clickSubmit() {
    if (AF_Act_Is_View) return goNextStep();
    field.validatePromise().then(({ errors }) => {
      console.log('errors: ', errors);
      if (errors) return showErrorMessageDialog(errors);
      goNextStep();
    });
  }
  function clickReturn() {
    goPrevStep();
  }
  return (
    <>
      <Container title="活动规则">
        <Box
          direction="row"
          justify="flex-end"
          align="center"
          spacing={10}
          margin={[0, 0, 10, 0]}
        >
          <span style={{ color: 'var(--color-text-secondary)' }}>
            提交活动前请核对活动规则，可根据实际情况，对内容进行适当编辑
          </span>
          <Button
            type="secondary"
            onClick={async () => {
              await generateActivityRule(field);
              field.validate('AF_baseInfo_rule');
            }}
          >
            生成规则
          </Button>
        </Box>
        <Form.Item
          name="AF_baseInfo_rule"
          label=""
          required
          requiredMessage={'请生成活动规则'}
          disabled={AF_Act_Is_View}
        >
          <Input.TextArea
            maxLength={2000}
            rows={24}
            cutString
            showLimitHint
            composition
            value={field.getValue('baseInfo.rule')}
            onChange={val => field.setValue('baseInfo.rule', val)}
            placeholder="请输入活动规则"
          />
        </Form.Item>
      </Container>

      <FixedBottomBox>
        <Button
          type="normal"
          size="large"
          onClick={clickReturn}
        >
          上一步
        </Button>
        <Button
          type="primary"
          size="large"
          onClick={clickSubmit}
        >
          下一步：氛围定制
        </Button>
      </FixedBottomBox>
    </>
  );
}
