import { PrizeTypeEnum } from '@/utils';

export default function generateCustomActivityRule(field) {
  return new Promise((resolve) => {
    const values = field.getValues();
    const {
      baseInfo: { startTime, endTime },
      crowdType,
      crowdName,
      playInfo,
      activityStairs,
    } = values;

    // 活动时间
    const activityTimeStr = `${startTime}至${endTime}`;

    // 活动对象：如果 crowdType 为1或没有 crowdName，则显示“全部用户”，否则显示 crowdName
    const audience = crowdType === 1 || !crowdName ? '全部用户' : crowdName;

    // 下单时间
    const orderTimeStr =
      playInfo.orderTimeType === 0 ? '活动时间内' : `${playInfo.orderTimeStart}至${playInfo.orderTimeEnd}`;

    // 活动商品：依据 playInfo.skuType 判断
    const skuTypeMap = {
      1: '全店范围商品',
      2: '排除部分商品',
      3: '指定商品',
    };
    const productType = skuTypeMap[playInfo.skuType];

    // 订单状态：支付成功或确认收货后+N天
    const orderStatus = playInfo.orderStatus === 0 ? '支付成功' : `确认收货后+${playInfo.orderStatus}天`;

    // 仅统计活动报名后的订单
    const orderAfterSign = playInfo.orderAfterSign ? '仅统计活动报名后的订单' : '';

    // 奖品信息：遍历活动阶梯，构造奖品描述（带阶梯名称）
    let prizeLines: any[] = [];
    const preEmpty = '              ';
    if (Array.isArray(activityStairs)) {
      activityStairs.forEach(stair => {
        if (Array.isArray(stair.lottery) && stair.lottery.length > 0) {
          const stairTitle = stair.name;
          const lines = stair.lottery.map(lottery => {
            return `${preEmpty}${lottery.lotteryName}：数量：${lottery.prizeNum}份，价值：${lottery.price}${
              lottery.lotteryType === PrizeTypeEnum.MEMBER_POINT.value ? '积分' : '元'
            }/份`;
          });
          prizeLines.push(`${preEmpty}${stairTitle}\n${lines.join('\n')}`);
        }
      });
    }
    const prizeInfo = prizeLines.join('\n');


    // 构造最终规则文本（仅作为示例）
    const rule = `
1、活动时间：
   ${activityTimeStr}；
2、活动对象：
   ${audience}可参加；
3、参与规则：
   （1）下单时间：${orderTimeStr}；
   （2）活动商品：${productType}；
   （3）订单状态：${orderStatus}；
   ${orderAfterSign ? `（4）${orderAfterSign}；` : ''}
4、领奖说明：
   （1）奖品数量有限，先到先得；
   （2）活动结束后，未领奖用户视为放弃领奖；
   （3）满足阶梯条件后，该阶梯下所有奖品均可领取；
   （4）用户参与活动中，如确认收货后有退货行为，则商家有权取消获奖资格；
   （5）奖品类型及数量价值：
${prizeInfo}
5、若用户存在刷奖等恶意行为，一经发现将取消抽奖资格（如奖品已经发放，有权追回奖品）；
奖品发放形式：
  实物奖品：
  （1）实物奖采取【单独寄送/随单发货】方式发放，获奖用户需在开奖后填写姓名、联系电话、详细地址信息。如因用户原因无法联系上，即奖品作废不再补发，或如用户未填写真实有效的信息或填写收货信息不详，均视为放弃奖品；
  （2）中奖客户信息收集：页面弹屏提示中奖客户提供收货地址、号码和奖项；(活动结束未填写对应收货信息，视为放弃)；
  虚拟奖品：
  （1）优惠券：领奖后奖品自动发放至抖音账户；
  （2）积分：领奖后奖品自动发放至抖音账户；
【活动参与主体资格】
  （1）每位自然人用户仅能使用一个抖音账号参与活动，手机号码等任意信息一致或指向同一用户，视为同一用户，则第一个参与本活动的账号参与结果有效，其他账号参与本活动均视为无效；若发现同一位用户使用不同账号重复参与活动，承办方有权取消其获奖资格；
【注意事项】
  （1）活动过程中，凡以不正当手段（如作弊领取、恶意套取、刷信誉、虚假交易、扰乱系统、实施网络攻击等）参与本次活动的用户，商家有权终止其参与活动，并取消其参与资格（如优惠已发放，商家有权追回），如给商家造成损失的，商家将保留向违规用户继续追索的权利；
  （2）如遇不可抗力(包括但不限于重大灾害事件、活动受政府机关指令需要停止举办或调整的、活动中存在大面积作弊行为、活动遭受严重网络攻击或因系统故障导致活动中奖名单大批量出错，活动不能正常进行的)，商家有权取消、修改或暂停本活动；
  （3）是否获得优惠以活动发布者后台统计结果为准；
  `.trim();

    // 保存生成的规则到表单字段 'baseInfo.rule'
    field.setValue('baseInfo.rule', rule);
    resolve(true);
  });
}
