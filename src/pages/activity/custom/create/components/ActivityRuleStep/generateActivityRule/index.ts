import { ActivityTypeMapEnum } from '@/utils';

import generateCustomActivityRule from './generateCustomActivityRule';

export default function generateActivityRule(field: any) {
  const values = field.getValues();
  const { activityType } = values.baseInfo;

  // 自定义活动
  if (activityType === ActivityTypeMapEnum.CUSTOMIZE_ACTIVITY.value) {
    return generateCustomActivityRule(field);
  }
  return Promise.resolve();
}
