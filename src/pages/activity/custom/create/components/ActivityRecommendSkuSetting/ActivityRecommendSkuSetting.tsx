import { Box, Button, Form, Icon, Input } from '@alifd/next';
import ProductPicker from '@/pages/activity/custom/create/components/ProductPicker';
/*
推荐商品设置
*/
export default function ActivityRecommendSkuSetting({ field }) {
  const selectedProducts = field.getValue('activityProduct') || [];
  const AF_Act_Is_View = field.getValue('AF_Act_Is_View');

  // 删除商品
  const handleDeleteProduct = numIid => {
    const current = selectedProducts;
    const filtered = current.filter(item => item.numIid !== numIid);
    field.setValue('activityProduct', filtered);
  };

  return (
    <Form.Item
      name="AF_activityProduct"
      label=""
      required
      validator={() => {
        const selected = field.getValue('activityProduct') || [];
        if (!selected.length) {
          return Promise.reject('请至少选择一个推荐商品');
        }
        return Promise.resolve();
      }}
    >
      <Input
        htmlType="hidden"
        value={'AF_activityProduct'}
      />

      <Box
        padding={[20, 0]}
        spacing={16}
      >
        {!AF_Act_Is_View && (
          <ProductPicker
            key={selectedProducts.length}
            min={1}
            max={50}
            selectedItems={selectedProducts}
            onSelectedProducts={selected => {
              field.setValue('activityProduct', selected);
              field.validate('AF_activityProduct');
            }}
          />
        )}
        <Box
          direction="row"
          wrap
          spacing={15}
        >
          {selectedProducts.map(product => (
            <div
              key={product.numIid}
              className={'product-item'}
            >
              <div className="product-img">
                <img
                  src={product.picUrl}
                  alt={product.title}
                />
                {/* 删除按钮 */}
                {!AF_Act_Is_View && (
                  <Button
                    className="delete-product-item-icon"
                    type="primary"
                    text
                    iconSize="small"
                    onClick={() => handleDeleteProduct(product.numIid)}
                  >
                    <Icon type="ashbin" />
                  </Button>
                )}
              </div>
              <span className="product-title">{product.title}</span>
              <div className="product-info">
                <span className="product-price" />
                <div className="product-stock">
                  销量
                  <span className="product-stock-num">{product.sellNum || 0}</span>
                </div>
              </div>
            </div>
          ))}
        </Box>
      </Box>
    </Form.Item>
  );
}
