import { Button } from '@alifd/next';
import React from 'react';
import './PromotionShopHomePage.scss';
import { history } from 'ice';

export default function PromotionShopHomePage() {
  return (
    <div className="promotion-shop-home-page">
      <div className="promotion-des-img-container">
        <img
          className="promotion-des-img"
          alt={''}
          src="https://novae.oss-cn-zhangjiakou.aliyuncs.com/douyin/images/home.png"
        />
      </div>
      <div className="footer">
        <div>店铺首页banner</div>
        <div>
          <Button
            type="primary"
            size={'small'}
            onClick={() => {
              window.open('https://fxg.jinritemai.com/login', '_blank');
            }}
          >
            去投放
          </Button></div>
        <div>
          <Button
            size={'small'}
            type={'primary'}
            text
            onClick={() => {
            window.open('https://gyj4qdmjsv.feishu.cn/wiki/LgaDwy5rGiKgrskaATccyBUOnLg?from=from_copylink', '_blank');
          }}
          >查看投放教程</Button>
        </div>
      </div>
    </div>
  );
}
