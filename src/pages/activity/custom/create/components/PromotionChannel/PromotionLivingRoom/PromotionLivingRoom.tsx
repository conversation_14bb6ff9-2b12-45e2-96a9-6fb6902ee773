import { Button } from '@alifd/next';
import React from 'react';
import './PromotionLivingRoom.scss';
import { history } from 'ice';

export default function PromotionLivingRoom() {
  return (
    <div className="promotion-living-room">
      <div className="promotion-des-img-container">
        <img
          className="promotion-des-img"
          src="https://novae.oss-cn-zhangjiakou.aliyuncs.com/douyin/images/snow.png"
        />
        <div className="footer">
          <div>直播间小雪花</div>
          <div>
            <Button
              type="primary"
              size={'small'}
              onClick={() => {
                history?.push('/activity/promotion', { tab: 2 });
              }}
            >
              去投放
            </Button></div>
          <div>
            <Button
              size={'small'}
              type={'primary'}
              text
              onClick={() => {
              window.open('https://gyj4qdmjsv.feishu.cn/wiki/LgaDwy5rGiKgrskaATccyBUOnLg?from=from_copylink', '_blank');
            }}
            >查看投放教程</Button>
          </div>
        </div>
      </div>
    </div>
  );
}
