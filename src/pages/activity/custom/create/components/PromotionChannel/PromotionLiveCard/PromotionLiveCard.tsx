import { Button, Slider } from '@alifd/next';
import React from 'react';
import './PromotionLiveCard.scss';
import { history } from 'ice';

export default function PromotionLiveCard() {
  const imgList = [
    'https://novae.oss-cn-zhangjiakou.aliyuncs.com/douyin/images/feed.png',
  ];

  return (
    <div className="promotion-live-card" style={{ position: 'relative' }}>
      <div className="promotion-des-img-container">
        <Slider
          className="promotion-des-img-slider"
          arrows={false}
          autoplay
          dots={false}
          autoplaySpeed={8000}
        >
          {imgList.map((item, index) => (
            <div key={index}>
              <img
                className="promotion-des-img"
                src={item}
              />
            </div>
          ))}
        </Slider>
      </div>
      <div className="footer">
        <div>首页feed流</div>
        <div>
          <Button
            type="primary"
            size={'small'}
            onClick={() => {
              history?.push('/activity/promotion', { tab: 3 });
            }}
          >
            去投放
          </Button></div>
        <div>
          <Button
            size={'small'}
            type={'primary'}
            text
            onClick={() => {
            window.open('https://gyj4qdmjsv.feishu.cn/wiki/LgaDwy5rGiKgrskaATccyBUOnLg?from=from_copylink', '_blank');
          }}
          >查看投放教程</Button>
        </div>
      </div>
    </div>
  );
}
