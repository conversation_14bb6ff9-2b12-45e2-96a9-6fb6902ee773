import { Dialog } from '@alifd/next';
import PromotionChannel from './PromotionChannel';
import { addDialogRef } from '@/utils/dialogMapper';

export default function showPromotionChannel() {
  return new Promise(resolve => {
    const dialogRef = Dialog.show({
      v2: true,
      title: '推广渠道',
      width: 'auto',
      centered: true,
      closeMode: ['close'],
      content: <PromotionChannel />,
      footer: false,
    });
    addDialogRef(dialogRef);
    resolve(true);
  });
}
