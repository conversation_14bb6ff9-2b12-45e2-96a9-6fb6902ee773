.promotion-tab-item {
  position: relative;
  display: flex;

  .promotion-title {
    padding: 20px 12px 12px;
    font-size: 14px;
  }

  .new-type-img {
    position: absolute;
    top: -5px;
    right: -6px;
    width: 52px;
  }
}

.promotion-des-img-container {
  flex-shrink: 0;
  width: 150px;

  .promotion-des-img-slider {
    width: 100%;
  }

  .promotion-des-img {
    width: 100%;
  }

  .promotion-des-img-slider-tips {
    color: #999;
  }
}

.promotion-detail {
  padding-left: 50px;
}

.promotion-title {
  h3 {
    display: flex;
    align-items: center;
    margin: 0;

    span {
      margin-right: 10px;
      font-size: 14px;
    }

    i {
      padding: 0 7px;
      color: #fff;
      font-size: 13px;
      font-style: normal;
      line-height: 1.8;
      border-radius: 6px;
    }
  }

  p {
    margin: 0;
    padding: 5px 0;
    color: #999;
    font-size: 14px;
  }
}

.promotion-operation {
  h3 {
    margin: 0;
    padding: 5px 0;
    font-size: 14px;
  }
}

.operation-step {
  margin: 0;
  padding-top: 3px;
  color: #999;
  font-size: 14px;
  line-height: 1.3;
}

.promotion-operation-buttons {
  margin-top: 15px;
}
