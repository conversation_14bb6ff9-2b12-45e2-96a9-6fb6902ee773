import { Box, Button } from '@alifd/next';
import Timeline from '@/components/Timeline/Timeline';
import './PromotionWangwang.scss';

export default function PromotionWangwang() {
  const operations = [
    <p className="operation-step">前往超级追单应用—点击「创建活动-满就送」菜单页面</p>,
    <p className="operation-step">找到要投放的超级复购活动</p>,
    <p className="operation-step">配置超级追单活动的相关内容，完成活动创建</p>,
    <p className="operation-step">消费者下单后可在旺旺消息中收到超级复购的活动卡片</p>,
  ];

  return (
    <div className="promotion-wangwang">
      <Box direction="row" justify="space-between" align="center">
        <div className="promotion-title">
          <h3>
            <span>简介</span>
            <i style={{ backgroundColor: '#A116D9' }}>超级追单用户专享</i>
          </h3>
          <p>对已下单的优质用户通过旺旺消息主动触达，形成用户购后链路闭环，有效提升复购率</p>
        </div>
        <Button
          type="primary"
          onClick={() => {
            window.open(
              'https://fuwu.taobao.com/ser/detail.htm?spm=a1z13.pc_search_result.1234-fwlb.6.62cb5acaMi82Ou&service_code=FW_GOODS-1001104801&tracelog=search&from_key=%E8%B6%85%E7%BA%A7%E8%BF%BD%E5%8D%95',
              '_blank',
            );
          }}
        >
          前往超级追单
        </Button>
      </Box>
      <div className="promotion-operation">
        <h3>投放方法：</h3>
        <Timeline list={operations} />
      </div>
      <img
        style={{ width: '100%' }}
        src="https://img.alicdn.com/imgextra/i3/155168396/O1CN01ISHSBj2BtQ6fzGEe2_!!155168396.png"
      />
    </div>
  );
}
