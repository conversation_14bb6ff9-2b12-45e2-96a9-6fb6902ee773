import { Input, Button } from '@alifd/next';
import './ActivityQrcode.scss';
import { copyText } from '@/utils';
import { QRCodeSVG } from 'qrcode.react';
import constant from '@/utils/constant';

const defaultQRCodeUrl = {
  45693550: 'https://m.zjbyte.net/share/douyin/?token=RjQ1M0FBRDZGMDZCNzZleW96bzh4&share_channel=scan',
  20421521: 'https://m.zjbyte.net/share/douyin/?token=QTYwMURCQTRBQzcwNzZleW96cDBw&share_channel=scan',
  7778309: 'https://m.zjbyte.net/share/douyin/?token=QjU3MzE2RjE5ODRFNzZleW96c2V4&share_channel=scan',
};

export default function ActivityQrcode({ activityInfo }) {
  const appInfo = JSON.parse(localStorage.getItem(constant.LZ_CURRENT_SHOP) || '{}');

  return (
    <div className="activity-qrcode-box">
      {/* <img className="activity-qrcode-image" alt={''} src={activityInfo?.qrCodeUrl} /> */}
      <QRCodeSVG
        value={activityInfo?.actUrl ? activityInfo?.actUrl : defaultQRCodeUrl[appInfo.shopId]}
        className="activity-qrcode-image"
      />
      <div className="activity-qrcode-tip">抖音APP扫码可预览活动</div>
      {activityInfo?.actUrl && (
        <div className="activity-qrcode-row">
          <Input className="activity-qrcode-input" value={activityInfo?.actUrl} readOnly />
          <Button
            type="primary"
            size="small"
            className="activity-qrcode-btn"
            onClick={() => copyText(activityInfo?.actUrl)}
          >
            复制活动链接
          </Button>
        </div>
      )}
      {/* <div className="activity-qrcode-row"> */}
      {/*   <Input */}
      {/*     className="activity-qrcode-input" */}
      {/*     value={activityInfo?.shortUrl} */}
      {/*     readOnly */}
      {/*   /> */}
      {/*   <Button */}
      {/*     type="primary" */}
      {/*     size="small" */}
      {/*     className="activity-qrcode-btn" */}
      {/*     onClick={() => copyText(activityInfo?.shortUrl)} */}
      {/*   > */}
      {/*     复制淘短链 */}
      {/*   </Button> */}
      {/* </div> */}
    </div>
  );
}
