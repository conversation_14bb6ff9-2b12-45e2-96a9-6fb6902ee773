import { Dialog } from '@alifd/next';
import React from 'react';
import ActivityQrcode from './ActivityQrcode';
import { addDialogRef } from '@/utils/dialogMapper';

export default function showActivityQrcode({ activityInfo }) {
  return new Promise((resolve) => {
    const dialogRef = Dialog.show({
      v2: true,
      title: '活动二维码',
      width: 500,
      centered: true,
      closeMode: ['close'],
      content: <ActivityQrcode activityInfo={activityInfo} />,
      footer: false,
    });
    addDialogRef(dialogRef);
    resolve(true);
  });
}
