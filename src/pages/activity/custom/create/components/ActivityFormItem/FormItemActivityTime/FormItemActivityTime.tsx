import { useEffect, useState, useRef } from 'react';
import { DatePicker2, Form, Box, Message, Radio } from '@alifd/next';
import dayjs from 'dayjs';
import { timeDiff } from '@/utils';
import constant from '@/utils/constant';

/**
 * 活动开始时间、活动结束时间
 * 绑定到 baseInfo.startTime baseInfo.endTime 字段
 */
export default function FormItemActivityTime(props) {
  const { field } = props;

  const AF_Act_Is_Create = field.getValue('AF_Act_Is_Create');
  const AF_Act_In_Progress = field.getValue('AF_Act_In_Progress');
  const AF_Act_Is_View = field.getValue('AF_Act_Is_View');
  const AF_Act_Form_Disabled = field.getValue('AF_Act_Form_Disabled');
  const AF_Act_Is_Copy = field.getValue('AF_Act_Is_Copy');
  const AF_Act_Is_Edit = field.getValue('AF_Act_Is_Edit');

  // 应用过期时间
  const [expiredDate, setExpiredDate] = useState<any>(null);
  // 计算最大可选天数
  const [maxDays, setMaxDays] = useState<any>(null);
  // 快捷天数配置的选中值
  const [selectedDays, setSelectedDays] = useState<any>(null);

  // 从 appInfo 缓存中获取 orderExpireTime（插件有效期）
  const appInfo = JSON.parse(localStorage.getItem(constant.LZ_CURRENT_SHOP) || '{}');
  // 从表单里读取当前 start/end
  const startTime = field.getValue('baseInfo.startTime');
  const endTime = field.getValue('baseInfo.endTime');

  // 用 ref 保存“原始”开始/结束，挂载后不再变
  const originalEndRef = useRef(endTime);

  // 今天的起始时间（00:00:00）
  const todayStart = dayjs().startOf('day');


  const renderActivityTime = () => {
    const { orderExpireTime } = appInfo;
    setExpiredDate(orderExpireTime);
    const maxDay = orderExpireTime ? dayjs(orderExpireTime).diff(todayStart, 'day') : Infinity;
    setMaxDays(maxDay);

    const freshStart = field.getValue('baseInfo.startTime');
    const freshEnd = field.getValue('baseInfo.endTime');

    if (freshStart && freshEnd) {
      field.setValue('baseInfo.startTime', freshStart);
      field.setValue('baseInfo.endTime', freshEnd);
      const diffDays = dayjs(freshEnd).diff(freshStart, 'day');
      setSelectedDays(diffDays);
      return;
    }
    if (AF_Act_Is_Create) {
      // 若是创建模式且有效期>=15天，则填默认15天；
      if (maxDay >= 15) {
        const defaultStart = todayStart.format('YYYY-MM-DD HH:mm:ss');
        const defaultEnd = todayStart.add(15, 'day').format('YYYY-MM-DD HH:mm:ss');
        field.setValue('baseInfo.startTime', defaultStart);
        field.setValue('baseInfo.endTime', defaultEnd);
        setSelectedDays(15);
      } else {
        const defaultStart = todayStart.format('YYYY-MM-DD HH:mm:ss');
        field.setValue('baseInfo.startTime', defaultStart);
        field.setValue('baseInfo.endTime', '');
        setSelectedDays(null);
      }
    }
  };

  useEffect(() => {
    renderActivityTime();
  }, []);


  // 手动选择日期区间
  const handleDateChange = value => {
    if (value && value.length === 2) {
      field.setValue('baseInfo.startTime', value[0]);
      field.setValue('baseInfo.endTime', value[1]);
      const diffDays = dayjs(value[1]).diff(value[0], 'day');
      setSelectedDays(diffDays);
    }
  };

  // 快捷天数按钮点击
  const handleRadioChange = value => {
    const days = parseInt(value, 10);
    if (maxDays !== Infinity && days > maxDays) {
      Message.error(`最大天数不能超过 ${maxDays} 天`);
      return;
    }
    setSelectedDays(days);
    const newStart = todayStart.format('YYYY-MM-DD HH:mm:ss');
    const newEnd = todayStart.add(days, 'day').format('YYYY-MM-DD HH:mm:ss');
    field.setValue('baseInfo.startTime', newStart);
    field.setValue('baseInfo.endTime', newEnd);
  };

  // 计算活动持续时间显示
  const durationStr = timeDiff(startTime, endTime);
  // if (!expiredDate || !maxDays) return null;
  return (
    <>
      {/* 日期范围选择 */}
      <Form.Item
        name="AF_baseInfo_activityTime"
        label="活动时间"
        extra={<p className="form-extra">注：活动结束时间不可超过订购有效期</p>}
        required
        requiredMessage="请选择活动时间"
        validator={(_, value: any) => {
          if (value && value.length === 2) {
            if (dayjs(value[1]).isAfter(dayjs(expiredDate))) {
              return Promise.reject('活动结束时间不能晚于订购有效期');
            }
            // 如果是编辑模式，且修改后的结束时间早于原始结束时间，则提示错误
            if (AF_Act_Is_Edit && dayjs(value[1]).isBefore(dayjs(originalEndRef.current))) {
              return Promise.reject(`修改活动结束时间不能早于原活动结束时间${dayjs(originalEndRef.current).format('YYYY-MM-DD HH:mm:ss')}`);
            }
          }
          return Promise.resolve();
        }}
        style={{ marginBottom: 0 }}
        disabled={AF_Act_Is_View}
      >
        <DatePicker2.RangePicker
          showTime
          value={[startTime, endTime]}
          onChange={handleDateChange}
          hasClear={false}
          format="YYYY-MM-DD HH:mm:ss"
          outputFormat="YYYY-MM-DD HH:mm:ss"
          disabled={(AF_Act_In_Progress && !AF_Act_Is_Copy) ? [true, false] : [false, false]}
          timePanelProps={{ defaultValue: ['00:00:00', '23:59:59'] }}
          disabledDate={date => {
            return date.isAfter(dayjs(expiredDate));
          }}
          style={{ width: 'var(--activity-form-item-width)' }}
        />
        <Box
          style={{ display: 'inline-flex' }}
          padding={[0, 15]}
        >
          {`活动持续时间 ${durationStr}`}
        </Box>
      </Form.Item>

      {/* 快捷天数按钮 */}
      {!AF_Act_Form_Disabled && (
        <Form.Item
          label=" "
          extra={<p className="form-extra-red">
            小提示：活动时长建议配置≥15天，买家需参与并确认收货后才可领奖，若时长较短，可能出现符合领奖条件的用户无法领奖，设置较长的活动时间复购效果更好
          </p>}
        >
          <Radio.Group
            shape="button"
            onChange={handleRadioChange}
            disabled={maxDays < 15} // 如果有效期不足15天，则整体禁用
            value={selectedDays}
          >
            {[15, 30, 45, 90].map(days => (
              <Radio
                key={days}
                value={days}
              >
                {`${days}天`}
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>
      )}
    </>
  );
}
