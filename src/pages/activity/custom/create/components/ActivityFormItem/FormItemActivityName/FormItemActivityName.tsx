import { Form, Input } from '@alifd/next';
import React from 'react';

/**
 * 活动名称
 * 绑定到 baseInfo.activityName 字段
 */
export default function FormItemActivityName(props) {
  const { field } = props;
  const AF_Act_Is_View = field.getValue('AF_Act_Is_View');
  return (
    <Form.Item
      name="AF_baseInfo_activityName"
      label="活动名称"
      extra={<p className="form-extra">（将作为小程序名称，展示给消费者）</p>}
      required
      requiredMessage={'活动名称不能为空'}
      pattern="^[\u4E00-\u9FA5A-Za-z0-9_\-/.]+$"
      patternMessage="不可输入特殊符号，支持中文、英文、数字及下划线"
      disabled={AF_Act_Is_View}
    >
      <Input
        maxLength={16}
        showLimitHint
        trim
        composition
        value={field.getValue('baseInfo.activityName')}
        onChange={val => field.setValue('baseInfo.activityName', val)}
        style={{ width: 'var(--activity-form-item-width)' }}
      />
    </Form.Item>
  );
}
