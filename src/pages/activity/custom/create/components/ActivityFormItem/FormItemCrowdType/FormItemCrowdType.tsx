import { Button, Box, Form, Radio, Card } from '@alifd/next';
import chooseCrowdCondition from '../../chooseCrowdCondition/chooseCrowdCondition.js';
import setCdpCrowd from '../../setCdpCrowd/setCdpCrowd.js';
import { CrowdSummaryDesc } from '../../chooseCrowdCondition/CrowdConditionModalContent.jsx';

/**
 * 活动门槛 : 参与人群
 * 绑定到 crowdType 字段
 */
export default function FormItemCrowdType(props) {
  const { field } = props;

  const AF_Act_Form_Disabled = field.getValue('AF_Act_Form_Disabled');

  const radioOptions = [
    { value: 1, label: '全部用户' },
    { value: 2, label: '指定条件人群' },
    { value: 3, label: '选择CDP人群' },
    { value: 4, label: '排除CDP人群' },
  ];

  // 获取当前选中的 crowdType
  const crowdTypeValue = field.getValue('crowdType');

  // 当切换 crowdType 时，清空相关字段
  const handleCrowdTypeChange = value => {
    const crowdNameValue = value === 1 ? '全部用户' : '';
    // 清空条件人群、CDP 人群相关信息，以免之前的值残留
    field.setValue('crowdName', crowdNameValue);
    field.setValue('cdpCrowdId', '');
    // field.setValue('crowdInfo', []);
    // 同时更新 crowdType 字段
    field.setValue('crowdType', value);

    if (value === 2) {
      const crowdInfo = field.getValue('crowdInfo');
      crowdInfo.forEach(item => {
        const parsed = JSON.parse(item.configJson || '{}');
        parsed.isChecked = false;
        item.configJson = JSON.stringify(parsed);
      });
      field.setValue('crowdInfo', crowdInfo);
    }
  };

  // 打开“添加条件人群”弹窗
  const openCrowdConditionModal = async () => {
    const initialValues = {
      crowdName: field.getValue('crowdName') || '',
      crowdInfo: field.getValue('crowdInfo') || [],
    };
    const result = await chooseCrowdCondition(initialValues);
    console.log('添加条件人群 填写结果：', result);
    if (result) {
      field.setValue('crowdName', result.crowdName);
      field.setValue('crowdInfo', result.crowdInfo);
      field.validate('AF_crowdType'); // 触发校验，清除错误信息
    }
  };

  // 打开“配置CDP人群”弹窗（用于导入）
  const openCdpCrowdModal = async () => {
    const initialVals = {
      crowdName: field.getValue('crowdName') || '',
      cdpCrowdId: field.getValue('cdpCrowdId') || '',
    };
    const result = await setCdpCrowd(initialVals);
    console.log('CDP人群 填写结果：', result);
    if (result) {
      // 将填写结果保存到表单中
      field.setValue('crowdName', result.crowdName);
      field.setValue('cdpCrowdId', result.cdpCrowdId);
      field.validate('AF_crowdType'); // 触发校验，清除错误信息
    }
  };

  return (
    <>
      <Form.Item
        name="AF_crowdType"
        label="活动门槛"
        required
        validator={(_, value) => {
          if (value === 2 && !field.getValue('crowdName')) {
            return Promise.reject('请选择条件人群');
          }
          if ((value === 3 || value === 4) && (!field.getValue('crowdName') || !field.getValue('cdpCrowdId'))) {
            return Promise.reject('请配置CDP人群');
          }
          return Promise.resolve();
        }}
        autoValidate={false}
        disabled={AF_Act_Form_Disabled}
      >
        <Radio.Group
          value={field.getValue('crowdType')}
          onChange={handleCrowdTypeChange}
        >
          {radioOptions.map(option => (
            <Radio
              value={option.value}
              key={option.value}
            >
              {option.label}
            </Radio>
          ))}
        </Radio.Group>
      </Form.Item>
      <Form.Item
        label=" "
        size="medium"
      >
        {/* 根据 crowdTypeValue 不同，展示不同按钮 */}
        {crowdTypeValue === 2 && (
          <Box align="flex-start">
            {Array.isArray(field.getValue('crowdInfo')) && field.getValue('crowdName') &&
            field.getValue('crowdInfo').some(item => {
              try {
                const parsed = JSON.parse(item.configJson || '{}');
                return parsed.isChecked;
              } catch {
                return false;
              }
            }) ? (
              <CrowdSummaryDesc
                crowdName={field.getValue('crowdName')}
                crowdInfo={field.getValue('crowdInfo')}
                extra={
                  <Button
                    text
                    type="primary"
                    style={{ marginBottom: 10 }}
                    onClick={openCrowdConditionModal}
                    disabled={AF_Act_Form_Disabled}
                  >
                    <i style={{ fontSize: 18 }} className="iconfont icon-bianji" />
                  </Button>
                }
              />
            ) : (
              <Button
                type="normal"
                onClick={openCrowdConditionModal}
                disabled={AF_Act_Form_Disabled}
              >
                +添加条件人群
              </Button>
            )}
          </Box>
        )}
        {[3, 4].includes(crowdTypeValue) && (
          <Box align="flex-start">
            {field.getValue('crowdName') && field.getValue('cdpCrowdId') ? (
              <Card
                title={field.getValue('crowdName')}
                showTitleBullet={false}
                contentHeight={80}
                style={{ width: 300 }}
                extra={
                  <Button
                    text
                    type="primary"
                    style={{ marginBottom: 10 }}
                    onClick={openCdpCrowdModal}
                    disabled={AF_Act_Form_Disabled}
                  >
                    <i style={{ fontSize: 18 }} className="iconfont icon-bianji" />
                  </Button>
                }
              >
                <p style={{ lineHeight: 2, fontSize: 14 }}>CDP人群ID：{field.getValue('cdpCrowdId')}</p>
              </Card>
            ) : (
              <Button
                type="normal"
                onClick={openCdpCrowdModal}
                disabled={AF_Act_Form_Disabled}
              >
                {crowdTypeValue === 3 ? '+导入CDP人群' : '+排除CDP人群'}
              </Button>
            )}
          </Box>
        )}
      </Form.Item>
    </>
  );
}
