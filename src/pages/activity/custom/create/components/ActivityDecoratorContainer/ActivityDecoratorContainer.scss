.activity-decorator-page {
  display: flex;
  padding: 20px 0;

  // 当右侧预览被隐藏时，添加 hide-preview 类效果
  &.hide-preview {
    .container-right {
      display: none;
    }
    // 如果需要同时调整其他元素的样式，也可以在这里做处理
    .collapse {
      .icon {
        margin-left: 0;
        transform: rotate(180deg) translateX(50%);
      }
    }
  }

  .container-left {
    display: flex;
    flex: 1;
    flex-direction: column;
  }

  .collapse {
    position: relative;
    width: 1px;
    margin: 0 20px;
    background-color: #eee;

    .icon {
      position: absolute;
      top: 150px;
      left: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      background: #fff;
      border: 1px solid #eee;
      border-radius: 50%;
      box-shadow: 0 0 4px 0 rgba(0, 0, 0, 10%);
      transform: translate(-50%, -50%);
      cursor: pointer;
      transition: transform 200ms ease;

      // 当切换时旋转180度
      &.rotate {
        transform: rotate(180deg) translate(-50%, -50%);
      }

      img {
        width: 45%;
        margin-left: 2px;
        transition: all 200ms;
      }
    }
  }

  .container-right {
    flex-basis: 300px; // 可根据需要调整右侧预览宽度
  }
}
