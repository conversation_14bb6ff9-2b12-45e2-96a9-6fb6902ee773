import React, { useState } from 'react';
import { Box } from '@alifd/next';
import LzIcon from '@/components/Icons';
import './ActivityDecoratorContainer.scss';

export default function ActivityDecoratorContainer({ left, right }) {
  // 状态：是否隐藏右侧预览
  const [hidePreview, setHidePreview] = useState(false);

  // 切换预览显示/隐藏状态
  const togglePreview = () => {
    setHidePreview(prev => !prev);
  };

  return (
    <div className={`activity-decorator-page ${hidePreview ? 'hide-preview' : ''}`}>
      <div className="container-left">{left}</div>

      <div className="collapse">
        <div
          className="icon"
          onClick={togglePreview}
        >
          <LzIcon
            type="to-right"
            size="xl"
          />
        </div>
      </div>

      {/* 根据 hidePreview 状态决定是否展示右侧预览容器 */}
      {!hidePreview && <div className="container-right">{right}</div>}
    </div>
  );
}
