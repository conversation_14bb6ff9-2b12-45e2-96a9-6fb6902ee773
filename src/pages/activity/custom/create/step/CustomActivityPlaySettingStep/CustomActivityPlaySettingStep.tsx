import { Button } from '@alifd/next';
import FixedBottomBox from '@/components/FixedBottomBox/FixedBottomBox';

import CustomActivityBaseInfo from './CustomActivityBaseInfo/CustomActivityBaseInfo';
import CustomActivityCondition from './CustomActivityCondition/CustomActivityCondition';
import CustomActivityPlayInfo from './CustomActivityPlayInfo/CustomActivityPlayInfo';
import CustomActivityGiftRule from './CustomActivityGiftRule/CustomActivityGiftRule';
import CustomActivityStairs from './CustomActivityStairs/CustomActivityStairs.jsx';

import { showErrorMessageDialog } from '@/utils';

const formItems = [
  { component: CustomActivityBaseInfo }, // 基本信息
  { component: CustomActivityCondition }, // 活动门槛
  { component: CustomActivityPlayInfo }, // 玩法设置
  { component: CustomActivityGiftRule }, // 领奖规则
  { component: CustomActivityStairs }, // 奖品阶梯设置
];

// 自定义活动 - 第1步 - 基础玩法设置
export default function CustomActivityPlaySettingStep(props) {
  const { goNextStep, field } = props;
  const AF_Act_Is_View = field.getValue('AF_Act_Is_View');

  function clickSubmit() {
    if (AF_Act_Is_View) return goNextStep();
    field.validatePromise().then(({ errors }) => {
      console.log('errors: ', errors);
      if (errors) return showErrorMessageDialog(errors);
      goNextStep();
    });
  }

  return (
    <>
      {formItems.map((item, index) => (
        <item.component
          field={field}
          key={index}
        />
      ))}

      <FixedBottomBox>
        <Button
          type="primary"
          size="large"
          onClick={clickSubmit}
        >
          下一步：活动规则
        </Button>
      </FixedBottomBox>
    </>
  );
}
