import React from 'react';
import { Form, Checkbox } from '@alifd/next';
import HelpTooltip from '@/components/HelpTooltip/HelpTooltip';

/**
 * 订单类型
 *
 * 字段:
 *  - playInfo.orderType: string (逗号分隔字符串)，比如 "1", "2", "1,2"
 *    其中:
 *      "1" -> 现货订单
 *      "2" -> 预售订单
 */
export default function FormItemOrderType(props) {
  const { field } = props;
  const AF_Act_Form_Disabled = field.getValue('AF_Act_Form_Disabled');

  // 从表单中获取当前的字符串值，转换为数组
  const getCheckedValues = () => {
    const val = field.getValue('playInfo.orderType');
    if (!val) return [];
    return val.split(',').filter(Boolean);
  };

  // 当复选框变化时，将数组转换回字符串并存储到表单
  const handleChange = checkedValues => {
    const str = checkedValues.join(',');
    field.setValue('playInfo.orderType', str);
    console.log(field.getValue('playInfo.orderType'), 11111);
  };


  return (
    <Form.Item
      name="AF_playInfo_orderType"
      label="订单类型"
      required
      requiredMessage="请至少选择一种订单类型"
      disabled={AF_Act_Form_Disabled}
    >
      <Checkbox.Group
        value={getCheckedValues()}
        onChange={handleChange}
      >
        <Checkbox value="1">现货订单</Checkbox>
        {/* <Checkbox value="2"> */}
        {/*   预售订单 */}
        {/*   <HelpTooltip content={'预售订单【付尾款时间】在下单时间内便算作满足条件'} /> */}
        {/* </Checkbox> */}
      </Checkbox.Group>
    </Form.Item>
  );
}
