import React from 'react';
import Container from '@/components/Container';

import FormItemOrderAfterSign from './FormItemOrderAfterSign/FormItemOrderAfterSign';
import FormItemOrderTime from './FormItemOrderTime/FormItemOrderTime';
import FormItemOrderSku from './FormItemOrderSku/FormItemOrderSku';
import FormItemOrderType from './FormItemOrderType/FormItemOrderType';
import FormItemOrderStatus from './FormItemOrderStatus/FormItemOrderStatus';

export default function CustomActivityPlayInfo(props) {
  return (
    <Container
      title="玩法设置"
      subtitle="（用户订单满足以下条件后方可计入有效订单）"
    >
      <FormItemOrderAfterSign {...props} />
      <FormItemOrderTime {...props} />
      <FormItemOrderSku {...props} />
      <FormItemOrderType {...props} />
      <FormItemOrderStatus {...props} />
    </Container>
  );
}
