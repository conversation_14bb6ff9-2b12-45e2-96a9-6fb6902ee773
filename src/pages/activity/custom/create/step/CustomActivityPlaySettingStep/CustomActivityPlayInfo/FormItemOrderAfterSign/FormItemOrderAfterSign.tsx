import { Form, Checkbox } from '@alifd/next';

/**
 * 仅统计报名后订单
 * 绑定到 playInfo.orderAfterSign 字段
 */
export default function FormItemOrderAfterSign(props) {
  const { field } = props;

  const AF_Act_Form_Disabled = field.getValue('AF_Act_Form_Disabled');

  return (
    <Form.Item
      name="AF_playInfo_orderAfterSign"
      label=" "
      disabled={AF_Act_Form_Disabled}
    >
      <Checkbox
        checked={field.getValue('playInfo.orderAfterSign')}
        onChange={val => field.setValue('playInfo.orderAfterSign', val)}
      >
        仅统计报名后订单
      </Checkbox>
    </Form.Item>
  );
}
