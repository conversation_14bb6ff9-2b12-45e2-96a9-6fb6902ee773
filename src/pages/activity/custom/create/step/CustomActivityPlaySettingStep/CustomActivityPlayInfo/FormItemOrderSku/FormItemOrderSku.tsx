import { Form, Radio, Box } from '@alifd/next';

import ProductPicker from '@/pages/activity/custom/create/components/ProductPicker';
import HelpTooltip from '@/components/HelpTooltip/HelpTooltip';

/**
 * 下单商品
 *
 * 绑定字段:
 *  - playInfo.skuType: number, (1:全店商品, 2:排除部分商品, 3:指定商品)
 *  - playInfo.productList: array, (商品列表)
 */
export default function FormItemSkuType(props) {
  const { field } = props;
  const AF_Act_Form_Disabled = field.getValue('AF_Act_Form_Disabled');

  // 当前 skuType
  const skuType = field.getValue('playInfo.skuType');

  return (
    <Form.Item
      name="AF_playInfo_skuType"
      label="下单商品"
      required
      validator={(_) => {
        const type = field.getValue('playInfo.skuType');
        const productList = field.getValue('playInfo.productList') || [];
        if ((type === 2 || type === 3) && productList.length === 0) {
          return Promise.reject('请选择至少一个商品');
        }
        return Promise.resolve();
      }}
      autoValidate={false}
      disabled={AF_Act_Form_Disabled}
    >
      <Radio.Group
        value={skuType}
        onChange={value => {
          field.setValue('playInfo.skuType', value);
          // 当切换单选时清空 productList
          field.setValue('playInfo.productList', []);
        }}
      >
        <Radio value={1}>全店商品</Radio>
        <Radio value={2}>
          排除部分商品
          <HelpTooltip content={'您可以排除例如小样、购物金、补邮费链接或已经参与其他活动的商品，最多排除200件'} />
        </Radio>
        <Radio value={3}>指定商品</Radio>
      </Radio.Group>

      {/* 如果选了 “排除部分商品” (2) 或 “指定商品” (3)，出现商品选择器 */}
      {(skuType === 2 || skuType === 3) && (
        <Box style={{ marginTop: 8 }}>
          <ProductPicker
            key={skuType}
            min={1}
            max={skuType === 2 ? 200 : 500}
            selectedItems={field.getValue('playInfo.productList') || []}
            onSelectedProducts={selected => {
              // 当选择完成商品后更新表单
              field.setValue('playInfo.productList', selected);
              if (selected.length) {
                field.validate('AF_playInfo_skuType');
              }
            }}
            disabled={AF_Act_Form_Disabled}
          />
        </Box>
      )}
    </Form.Item>
  );
}
