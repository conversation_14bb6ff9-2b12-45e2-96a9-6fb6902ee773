import React from 'react';
import { Form, Radio, DatePicker2 } from '@alifd/next';
import dayjs from 'dayjs';

/**
 * 下单时间
 *
 * 字段：
 *  - playInfo.orderTimeType: number, 0 表示「活动时间内」、1 表示「指定时间」
 *  - playInfo.orderTimeStart: string (YYYY-MM-DD HH:mm:ss)
 *  - playInfo.orderTimeEnd: string (YYYY-MM-DD HH:mm:ss)
 */
export default function FormItemOrderTime(props) {
  const { field } = props;
  const AF_Act_Form_Disabled = field.getValue('AF_Act_Form_Disabled');

  // 获取当前的单选值，用来决定是否展示日期区间
  const orderTimeType = field.getValue('playInfo.orderTimeType');

  // 将表单中的开始/结束时间转换成 RangePicker 能识别的 [dayjs, dayjs]
  const getRangeValue = () => {
    const start = field.getValue('playInfo.orderTimeStart');
    const end = field.getValue('playInfo.orderTimeEnd');
    if (!start || !end) {
      return [];
    }
    return [dayjs(start), dayjs(end)];
  };

  // 当用户在 RangePicker 中修改时间时，同步到表单字段
  const handleRangeChange = val => {
    if (val && val.length === 2 && val[0] && val[1]) {
      field.setValue('playInfo.orderTimeStart', dayjs(val[0]).format('YYYY-MM-DD HH:mm:ss'));
      field.setValue('playInfo.orderTimeEnd', dayjs(val[1]).format('YYYY-MM-DD HH:mm:ss'));
      field.validate('AF_playInfo_orderTimeType'); // 触发校验，清除错误信息
    } else {
      // 如果用户清空或未选择完整，则清空
      field.setValue('playInfo.orderTimeStart', '');
      field.setValue('playInfo.orderTimeEnd', '');
    }
  };

  return (
    <Form.Item
      name="AF_playInfo_orderTimeType"
      label="下单时间"
      required
      validator={(_, __) => {
        const type = field.getValue('playInfo.orderTimeType');
        const start = field.getValue('playInfo.orderTimeStart');
        const end = field.getValue('playInfo.orderTimeEnd');
        if (type === 1 && (!start || !end)) {
          return Promise.reject('请选择完整的下单时间区间');
        }
        return Promise.resolve();
      }}
      autoValidate={false}
      disabled={AF_Act_Form_Disabled}>
      <Radio.Group
        value={field.getValue('playInfo.orderTimeType') ?? 0}
        onChange={val => {
          field.setValue('playInfo.orderTimeType', val);
        }}
        dataSource={[
          { value: 0, label: '活动时间内' },
          { value: 1, label: '指定时间' },
        ]}
      />
      {/* 如果选择“指定时间”，则展示 RangePicker */}
      {orderTimeType === 1 && (
        <DatePicker2.RangePicker
          style={{ marginLeft: 12 }}
          showTime
          format="YYYY-MM-DD HH:mm:ss"
          outputFormat="YYYY-MM-DD HH:mm:ss"
          timePanelProps={{ defaultValue: ['00:00:00', '23:59:59'] }}
          value={getRangeValue()}
          onChange={handleRangeChange}
        />
      )}
    </Form.Item>
  );
}
