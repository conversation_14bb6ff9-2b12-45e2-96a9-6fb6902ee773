import { Form, Radio, Box } from '@alifd/next';
import NumberInput from '@/components/NumberInput/NumberInput';
import HelpTooltip from '@/components/HelpTooltip/HelpTooltip';

/**
 * 订单状态
 *
 * 单选：
 *  1. 支付成功 => playInfo.orderStatus = 0
 *  2. 确认收货 N 天后 => playInfo.orderStatus = N (>=1)
 */
export default function FormItemOrderStatus(props) {
  const { field } = props;
  const AF_Act_Form_Disabled = field.getValue('AF_Act_Form_Disabled');

  // 从表单里获取当前值，如果还没设置，默认 0(支付成功)
  const currentVal = field.getValue('playInfo.orderStatus') ?? 0;

  // 根据 currentVal 判断 Radio 选中哪一项
  //  0 => "pay"
  //  >0 => "confirm"
  const radioValue = currentVal === 0 ? 'pay' : 'confirm';

  // 切换单选时，如果是 "pay" => 0；如果是 "confirm" => 若当前是 0，则默认 1（最小值），否则保持当前值。
  const handleRadioChange = val => {
    if (val === 'pay') {
      field.setValue('playInfo.orderStatus', 0);
    } else {
      // 切换到确认收货天数时，若当前=0 则设为1，否则保持当前
      const oldVal = field.getValue('playInfo.orderStatus');
      field.setValue('playInfo.orderStatus', oldVal > 0 ? oldVal : 1);
    }
  };

  // 当 NumberPicker 改变时，存入 playInfo.orderStatus
  const handleNumberChange = val => {
    // val 可能为 null, 你可以根据需求决定是否置为 1 或不处理
    if (val && val >= 1) {
      field.setValue('playInfo.orderStatus', val);
    } else {
      // 如果用户清空或输小于1，可根据需求重置为1或0
      field.setValue('playInfo.orderStatus', 1);
    }
  };

  return (
    <Form.Item
      name="AF_playInfo_orderStatus"
      label="订单状态"
      required
      validator={(_, __) => {
        const val = field.getValue('playInfo.orderStatus');
        if (val === 0) {
          return Promise.resolve();
        } else if (!val || val < 1) {
          return Promise.reject('请输入确认收货后的天数，且不能为0');
        } else {
          return Promise.resolve();
        }
      }}
      disabled={AF_Act_Form_Disabled}
    >
      <Radio.Group
        value={radioValue}
        onChange={handleRadioChange}
      >
        <Radio value="pay">
          支付成功
          <span style={{ color: 'var(--color-text-regular)' }}>（用户支付成功后即可累计阶梯进度）</span>
        </Radio>
        <br />
        <Radio value="confirm">
          <Box
            direction="row"
            align="center"
            spacing={10}
            style={{ display: 'inline-flex' }}
          >
            <span>确认收货</span>
            <NumberInput
              min={1}
              max={10}
              style={{ width: 80 }}
              value={currentVal > 0 ? currentVal : 1}
              onChange={handleNumberChange}
              disabled={AF_Act_Form_Disabled}
            />
            <span>天后</span>
            <span style={{ color: 'var(--color-text-regular)' }}>（用户订单确认收货+N天*24h后可累计阶梯进度）</span>
            <HelpTooltip content={'收货+N天不可选择【实物奖品(随单发货)】'} />
          </Box>
        </Radio>
      </Radio.Group>
    </Form.Item>
  );
}
