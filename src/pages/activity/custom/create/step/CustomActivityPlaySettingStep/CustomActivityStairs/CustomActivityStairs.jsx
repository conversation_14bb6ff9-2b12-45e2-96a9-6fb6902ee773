import React, { useState, useEffect, useRef } from 'react';
import { Box, Button, Form, Table, Input, Radio, Checkbox, Icon, Message, Dialog } from '@alifd/next';
import Container from '@/components/Container';
import NumberInput from '@/components/NumberInput/NumberInput';
import HelpTooltip from '@/components/HelpTooltip/HelpTooltip';
import { PrizeTypeEnum, getPrizeTypeLabel } from '@/utils';
import './CustomActivityStairs.scss';
import choosePrize from '@/components/choosePrize/choosePrize';
import editPrize from '@/components/editPrize/editPrize';
import { prizeGetSkuInfo } from '@/api/vf';
import { addDialogRef } from '@/utils/dialogMapper';
import {
  activityCustomizeActivityDelLottery,
  activityCustomizeActivityDelStairs,
  activityCustomizeActivityMinusLotteryNum,
} from '@/api/b';

/**
 * 奖品阶梯设置组件
 * 最终在表单中存储到: activityStairs
 */
export default function CustomActivityStairs(props) {
  const { field } = props;

  const AF_Act_Form_Disabled = field.getValue('AF_Act_Form_Disabled');
  const AF_Act_Not_Start = field.getValue('AF_Act_Not_Start');

  // 使用 ref 来存储最新的阶梯数据，确保验证函数能获取到实时数据
  const stairsRef = useRef([]);

  // 读取当前所有阶梯数据，默认为空数组
  const stairsFromField = field.getValue('activityStairs') || [];
  // 使用本地状态管理阶梯数据，确保即时更新
  const [stairs, setStairs] = useState(stairsFromField);

  // 当外部表单字段更新时同步本地状态
  useEffect(() => {
    const fieldStairs = field.getValue('activityStairs') || [];
    setStairs(fieldStairs);
    stairsRef.current = fieldStairs;
  }, [field]);

  // 更新本地状态和ref的辅助函数
  const updateStairs = (newStairs) => {
    setStairs(newStairs);
    stairsRef.current = newStairs;
    field.setValue('activityStairs', newStairs);
  };

  // 如果存在至少一个阶梯，则取第一个阶梯的 orderCountType；否则默认 1 (累计订单)
  const [orderCountType, setOrderCountType] = useState(stairs.length > 0 ? stairs[0].orderCountType : 1);

  // 根据 orderCountType 决定【阶梯类型】可选项
  const checkboxOptions =
    orderCountType === 1
      ? [
          { value: '满额', label: '满额' },
          { value: '满件', label: '满件' },
          { value: '满单', label: '满单' },
        ]
      : [
          { value: '满额', label: '满额' },
          { value: '满件', label: '满件' },
        ];

  /**
   * 初始化 stairType：
   * 如果回显数据里某个阶梯的 payment/nums/tradeNum 有值，就把对应类型勾选上，
   * 否则至少默认选中"满额"
   */
  const [stairType, setStairType] = useState(() => {
    const types = [];
    if (stairs.some((s) => s.payment)) types.push('满额');
    if (stairs.some((s) => s.nums)) types.push('满件');
    if (stairs.some((s) => s.tradeNum)) types.push('满单');
    return types.length ? types : ['满额'];
  });

  // 根据 stairType 计算 HelpTooltip 的内容
  const getHelpTooltipContent = (types) => {
    if (!types || types.length === 0) {
      return '';
    }
    if (types.length === 1) {
      if (types[0] === '满额') {
        return '(1)【玩法设置】中配置的【订单商品】金额计入有效金额\n(2)例:订单A中包含:商品a(有效商品)、商品b，则订单金额只计算商品a的金额';
      }
      if (types[0] === '满件') {
        return '(1)【玩法设置】中配置的【订单商品】计为符合条件的商品';
      }
      if (types[0] === '满单') {
        return '(1)符合【玩法设置】所配条件的订单计为有效订单';
      }
    } else if (types.length === 2) {
      return '(1)同时满足2个条件视为满足阶梯';
    } else if (types.length === 3) {
      return '(1)同时满足3个条件视为满足阶梯';
    }
    return '';
  };

  // 切换阶梯订单计算方式
  const handleGlobalOrderCountChange = (val) => {
    setOrderCountType(val);

    // 如果切换到"单笔订单"（value = 2），且 stairType 含"满单"，则自动去掉"满单"
    let newTypes = stairType;
    if (val === 2 && stairType.includes('满单')) {
      newTypes = stairType.filter((type) => type !== '满单');
      setStairType(newTypes);
    }

    // 一次性更新所有阶梯：orderCountType + 清理不再选中的字段
    const newStairs = stairs.map((stair) => {
      const updated = { ...stair, orderCountType: val };
      // 清理 payment/nums/tradeNum
      if (!newTypes.includes('满额')) updated.payment = '';
      if (!newTypes.includes('满件')) updated.nums = '';
      if (!newTypes.includes('满单')) updated.tradeNum = '';
      return updated;
    });

    updateStairs(newStairs);
  };

  // 当阶梯类型改变时，清空不在选项中的额外输入值
  const handleStairsTypeChange = (selectedOptions) => {
    setStairType(selectedOptions);

    // 一次性更新所有阶梯的对应字段
    const newStairs = stairs.map((stair, stairIndex) => {
      const updated = { ...stair };
      updated.payment = selectedOptions.includes('满额') ? updated.payment : '';
      updated.nums = selectedOptions.includes('满件') ? updated.nums : '';
      updated.tradeNum = selectedOptions.includes('满单') ? updated.tradeNum : '';
      field.reset(`AF_activityStairs_criteria_${stairIndex}`);
      return updated;
    });

    updateStairs(newStairs);
  };

  // 更新某个阶梯的名称
  const handleNameChange = (stairIndex, newName) => {
    const newStairs = [...stairs];
    newStairs[stairIndex] = {
      ...newStairs[stairIndex],
      name: newName,
    };
    updateStairs(newStairs);
  };

  // 更新阶梯额外字段，使用本地状态确保即时更新
  const handlePaymentChange = (stairIndex, val) => {
    const newStairs = [...stairs];
    newStairs[stairIndex] = { ...newStairs[stairIndex], payment: val };
    // 更新本地状态和ref
    updateStairs(newStairs);
    field.validate(`AF_activityStairs_criteria_${stairIndex}`);
  };

  const handleNumsChange = (stairIndex, val) => {
    const newStairs = [...stairs];
    newStairs[stairIndex] = { ...newStairs[stairIndex], nums: val };
    updateStairs(newStairs);
    field.validate(`AF_activityStairs_criteria_${stairIndex}`);
  };

  const handleTradeNumChange = (stairIndex, val) => {
    const newStairs = [...stairs];
    newStairs[stairIndex] = { ...newStairs[stairIndex], tradeNum: val };
    updateStairs(newStairs);
    field.validate(`AF_activityStairs_criteria_${stairIndex}`);
  };

  // 添加一个阶梯
  const handleAddStair = () => {
    const newStairs = [...stairs];
    const nextIndex = newStairs.length + 1;

    const prevStair = newStairs[newStairs.length - 1] || {};
    const nextPayment = stairType.includes('满额') ? Number(prevStair.payment || 0) + 1 : '';
    const nextNums = stairType.includes('满件') ? Number(prevStair.nums || 0) + 1 : '';
    const nextTradeNum = stairType.includes('满单') ? Number(prevStair.tradeNum || 0) + 1 : '';

    newStairs.push({
      name: `阶梯${nextIndex}`, // 默认阶梯名称
      orderCountType,
      payment: nextPayment,
      nums: nextNums,
      tradeNum: nextTradeNum,
      lottery: [],
    });

    updateStairs(newStairs);
    field.validate('AF_activityStairs');
  };

  const disabledTabs = [];
  //  当玩法设置中选择【订单状态=收货+N天】时，
  if (field.getValue('playInfo.orderStatus') > 0) {
    disabledTabs.push(PrizeTypeEnum.PRACTICALITY_BY_ORDER.value);
  }
  // 且自动发货时 不可选择【实物奖品（随单发货）】【优惠券】【积分】
  if (field.getValue('giftRule.autoSend')) {
    disabledTabs.push(PrizeTypeEnum.COUPON.value);
    disabledTabs.push(PrizeTypeEnum.MEMBER_POINT.value);
  }
  //  当基本信息的用户参与方式选择【免预约参与】时，不可选择【实物奖品（手工发货）】
  if (field.getValue('baseInfo.joinType') == 2) {
    disabledTabs.push(PrizeTypeEnum.PRACTICALITY.value);
  }

  /**
   * 点击"添加奖品"按钮后逻辑：
   * 1. 调用 choosePrize;
   * 2. 如果用户选中某个奖品并保存，result 即该奖品信息
   * 3. 将此奖品 push 到当前阶梯的 lottery 数组中
   * 4. 最多只允许 3 个奖品
   */
  const handleAddPrize = async (stairIndex) => {
    const newStairs = [...stairs];
    const currentStair = newStairs[stairIndex];
    // 如果还没初始化lottery数组，则初始化
    if (!Array.isArray(currentStair.lottery)) {
      currentStair.lottery = [];
    }
    // 判断是否已达3个上限
    if (currentStair.lottery.length >= 3) {
      Message.error('最多只能添加 3 个奖品');
      return;
    }
    const result = await choosePrize({
      disabledTabs,
    });
    if (result) {
      console.log('用户最终选择的奖品信息：', result);
      currentStair.lottery.push(result);
      updateStairs(newStairs);
      field.validate(`AF_activityStairs_lottery_${stairIndex}`);
    }
  };

  /**
   * 删除阶梯
   */
  const handleDeleteStair = (stairIndex) => {
    const stair = stairs[stairIndex];
    const confirmText = AF_Act_Not_Start ? '删除阶梯将释放阶梯内奖品冻结库存，是否继续？' : '是否删除当前阶梯';
    Dialog.confirm({
      title: '提示',
      content: confirmText,
      onOk: async () => {
        if (AF_Act_Not_Start && stair.id) {
          try {
            await activityCustomizeActivityDelStairs({
              stairsId: stair.id,
            });
          } catch (e) {
            Message.error(e.message);
          }
        }
        const newStairs = [...stairs];
        newStairs.splice(stairIndex, 1);
        updateStairs(newStairs);
      },
    });
  };

  /**
   * 编辑奖品
   */
  const handleEditPrize = async (stairIndex, prizeIndex, record) => {
    const stair = stairs[stairIndex];
    const prize = stair.lottery[prizeIndex];

    const proceedEdit = async () => {
      const updatedPrize = await editPrize({
        editPrizeInfo: {
          ...record,
          // 如果有prizeId，则是在修改活动的时候，点击的编辑
          prizeId: prize.id,
        },
        disabledTabs,
        field,
      });
      if (updatedPrize) {
        // 显式清空 id
        updatedPrize.id = '';

        const newStairs = [...stairs];
        const targetLottery = newStairs[stairIndex].lottery || [];
        targetLottery[prizeIndex] = {
          ...targetLottery[prizeIndex],
          ...updatedPrize,
        };
        field.setValue('activityStairs', newStairs);
      } else if (AF_Act_Not_Start && stair.id && prize.id) {
        const newStairs = [...stairs];
        newStairs[stairIndex].lottery.splice(prizeIndex, 1);
        updateStairs(newStairs);
        field.validate(`AF_activityStairs_lottery_${stairIndex}`);
      }
    };

    if (AF_Act_Not_Start && stair.id && prize.id) {
      const dialogRef = Dialog.confirm({
        title: '提示',
        content: '编辑实物奖品将释放已冻结库存需重新保存，是否继续？',
        onOk: async () => {
          dialogRef.hide();
          try {
            await activityCustomizeActivityMinusLotteryNum({
              lotteryId: prize.id,
              num: prize.prizeNum,
            });
          } catch (e) {
            Message.error(e.message);
          }
          await proceedEdit();
        },
      });
      addDialogRef(dialogRef);
    } else {
      await proceedEdit();
    }
  };

  /**
   * 删除已添加的奖品
   */
  const handleDeletePrize = (stairIndex, prizeIndex) => {
    const stair = stairs[stairIndex];
    const prize = stair.lottery[prizeIndex];
    const confirmText = AF_Act_Not_Start ? '删除后将释放该奖品冻结库存，是否继续？' : '是否删除当前奖品';
    Dialog.confirm({
      title: '提示',
      content: confirmText,
      onOk: async () => {
        if (AF_Act_Not_Start && stair.id && prize.id) {
          try {
            await activityCustomizeActivityDelLottery({
              stairsId: stair.id,
              lotteryId: prize.id,
            });
          } catch (e) {
            Message.error(e.message);
          }
        }

        const newStairs = [...stairs];
        newStairs[stairIndex].lottery.splice(prizeIndex, 1);
        updateStairs(newStairs);
        field.validate(`AF_activityStairs_lottery_${stairIndex}`);
      },
    });
  };

  // 构造各个条件的组件数组，并在中间插入"且"连接词
  const renderStairCriteria = (stair, stairIndex) => {
    const criteriaComponents = [];
    if (stairType.includes('满额')) {
      criteriaComponents.push(
        <Box key="满额" direction="row" align="center" spacing={5}>
          <span>订单金额大于等于</span>
          <NumberInput
            min={1}
            value={stair.payment}
            onChange={(val) => handlePaymentChange(stairIndex, val)}
            style={{ width: 80 }}
            disabled={AF_Act_Form_Disabled}
          />
          <span>元</span>
        </Box>,
      );
    }
    if (stairType.includes('满件')) {
      criteriaComponents.push(
        <Box key="满件" direction="row" align="center" spacing={5}>
          <span>订单中符合条件的商品累计满</span>
          <NumberInput
            min={1}
            value={stair.nums}
            onChange={(val) => handleNumsChange(stairIndex, val)}
            style={{ width: 80 }}
            disabled={AF_Act_Form_Disabled}
          />
          <span>件</span>
        </Box>,
      );
    }
    if (stairType.includes('满单')) {
      criteriaComponents.push(
        <Box key="满单" direction="row" align="center" spacing={5}>
          <span>订单笔数大于等于</span>
          <NumberInput
            min={1}
            value={stair.tradeNum}
            onChange={(val) => handleTradeNumChange(stairIndex, val)}
            style={{ width: 80 }}
            disabled={AF_Act_Form_Disabled}
          />
          <span>单</span>
        </Box>,
      );
    }
    // 插入连接词"且"
    const interleaved = [];
    criteriaComponents.forEach((item, idx) => {
      interleaved.push(item);
      if (idx < criteriaComponents.length - 1) {
        interleaved.push(
          <span key={`connector-${idx}`} className="connector">
            且
          </span>,
        );
      }
    });
    return interleaved;
  };

  // 修改验证器函数，使用ref中最新的stairs数据
  const validateStairCriteria = (_, __, stairIndex) => {
    // 确保使用最新的stairs数据
    const currentStairs = stairsRef.current;
    const stair = currentStairs[stairIndex];
    const stairNo = stairIndex + 1;
    const messages = [];
    if (stairType.includes('满额') && !stair.payment) {
      messages.push('请填写订单金额');
    }
    if (stairType.includes('满件') && !stair.nums) {
      messages.push('请填写商品件数');
    }
    if (stairType.includes('满单') && !stair.tradeNum) {
      messages.push('请填写订单笔数');
    }

    // 比较上一阶梯
    if (stairIndex > 0) {
      const prev = currentStairs[stairIndex - 1];
      if (
        stairType.includes('满额') &&
        stair.payment &&
        prev.payment &&
        Number(stair.payment) <= Number(prev.payment)
      ) {
        messages.push('订单金额必须大于上一阶梯');
      }
      if (stairType.includes('满件') && stair.nums && prev.nums && Number(stair.nums) <= Number(prev.nums)) {
        messages.push('商品件数必须大于上一阶梯');
      }
      if (
        stairType.includes('满单') &&
        stair.tradeNum &&
        prev.tradeNum &&
        Number(stair.tradeNum) <= Number(prev.tradeNum)
      ) {
        messages.push('订单笔数必须大于上一阶梯');
      }
    }

    if (messages.length) {
      return Promise.reject(`阶梯${stairNo}：${messages.join('、')}`);
    }
    return Promise.resolve();
  };

  return (
    <Container title="奖品阶梯设置">
      {/* 全局阶梯订单计算方式 */}
      <Form.Item label="阶梯订单计算方式" disabled={AF_Act_Form_Disabled}>
        <Radio.Group value={orderCountType} onChange={handleGlobalOrderCountChange}>
          <Radio value={1}>
            累计订单
            <HelpTooltip content="勾选后，下方【阶梯奖品】中多笔订单累计满足【阶梯任务】后可获得奖品" />
          </Radio>
          <Radio value={2}>
            单笔订单
            <HelpTooltip content="勾选后，下方【阶梯奖品】中任一单笔订单满足【阶梯任务】后可获得奖品" />
          </Radio>
        </Radio.Group>
      </Form.Item>

      {/* 阶梯类型 */}
      <Form.Item
        name="AF_activityStairs_stairType"
        label="阶梯类型"
        required
        requiredMessage="请选择至少一种阶梯类型"
        disabled={AF_Act_Form_Disabled}
      >
        <Checkbox.Group value={stairType} onChange={handleStairsTypeChange}>
          {checkboxOptions.map((option) => (
            <Checkbox key={option.value} value={option.value}>
              {option.label}
            </Checkbox>
          ))}
        </Checkbox.Group>
      </Form.Item>

      {/* 渲染所有阶梯 */}
      <Box className="stair-list">
        {stairs.map((stair, stairIndex) => (
          <Box key={stairIndex} spacing={16} padding={[20, 0]} margin={[0, 0, 20, 0]} className="stair-item">
            {/* 删除按钮 */}
            <Button
              type="primary"
              text
              iconSize="small"
              className="delete-stair-item"
              onClick={() => handleDeleteStair(stairIndex)}
              disabled={AF_Act_Form_Disabled}
            >
              <Icon type="ashbin" />
            </Button>

            <Form.Item
              name={`AF_activityStairs_name_${stairIndex}`}
              label={`阶梯名称 ${stairIndex + 1}`}
              required
              requiredMessage={`阶梯${stairIndex + 1} ：阶梯名称不能为空`}
              disabled={AF_Act_Form_Disabled}
            >
              <Input
                placeholder="请输入阶梯名称"
                value={stair.name}
                onChange={(val) => handleNameChange(stairIndex, val)}
                maxLength={5}
                showLimitHint
                trim
                composition
                style={{ width: 200 }}
                disabled={AF_Act_Form_Disabled}
              />
            </Form.Item>

            {/* 阶梯任务部分 */}
            <Form.Item
              name={`AF_activityStairs_criteria_${stairIndex}`}
              label={`阶梯任务 ${stairIndex + 1}`}
              required
              requiredMessage=""
              validator={(_, __) => validateStairCriteria(_, __, stairIndex)}
              autoValidate={false}
            >
              <Input htmlType="hidden" value={`activityStairs_criteria_${stairIndex}`} />
              <Box direction="row" align="center" spacing={10} style={{ height: 34 }}>
                {renderStairCriteria(stair, stairIndex)}
                <HelpTooltip content={getHelpTooltipContent(stairType)} />
              </Box>
            </Form.Item>

            {/* 阶梯奖品部分 */}
            <Form.Item
              name={`AF_activityStairs_lottery_${stairIndex}`}
              label={`阶梯奖品 ${stairIndex + 1}`}
              validator={(_, __) => {
                const stair = stairs[stairIndex];
                const stairNo = stairIndex + 1;
                const messages = [];

                if (!stair.lottery || stair.lottery.length === 0) {
                  messages.push('请至少添加一个奖品');
                } else if (
                  field.getValue('playInfo.orderStatus') > 0 &&
                  field.getValue('giftRule.autoSend') &&
                  field.getValue('baseInfo.joinType') === 2 &&
                  stair.lottery?.some(
                    (prize) =>
                      prize.lotteryType === PrizeTypeEnum.PRACTICALITY.value ||
                      prize.lotteryType === PrizeTypeEnum.COUPON.value ||
                      prize.lotteryType === PrizeTypeEnum.MEMBER_POINT.value,
                  )
                ) {
                  messages.push(
                    '选择【订单收货+N天】【自动领奖】且【免预约参与】时不可选择【实物奖品（手工发货，随单发货，优惠券，积分）】',
                  );
                } else if (
                  field.getValue('playInfo.orderStatus') > 0 &&
                  field.getValue('giftRule.autoSend') &&
                  stair.lottery?.some(
                    (prize) =>
                      prize.lotteryType === PrizeTypeEnum.PRACTICALITY_BY_ORDER.value ||
                      prize.lotteryType === PrizeTypeEnum.COUPON.value ||
                      prize.lotteryType === PrizeTypeEnum.MEMBER_POINT.value,
                  )
                ) {
                  messages.push('选择【订单收货+N天】【自动领奖】时不可选择【实物奖品（随单发货， 优惠券，积分）】');
                } else if (
                  field.getValue('playInfo.orderStatus') > 0 &&
                  stair.lottery?.some((prize) => prize.lotteryType === PrizeTypeEnum.PRACTICALITY_BY_ORDER.value)
                ) {
                  messages.push('选择【订单收货+N天】时不可选择【实物奖品（随单发货）】');
                } else if (
                  field.getValue('baseInfo.joinType') === 2 &&
                  stair.lottery?.some((prize) => prize.lotteryType === PrizeTypeEnum.PRACTICALITY.value)
                ) {
                  messages.push('选择【免预约参与】时不可选择【实物奖品（手工发货）】');
                } else if (
                  field.getValue('giftRule.autoSend') > 0 &&
                  stair.lottery?.some(
                    (prize) =>
                      prize.lotteryType === PrizeTypeEnum.COUPON.value ||
                      prize.lotteryType === PrizeTypeEnum.MEMBER_POINT.value,
                  )
                ) {
                  messages.push('选择【自动领奖】时不可选择【优惠券，积分】');
                }
                if (messages.length) {
                  return Promise.reject(`${stair.name}：${messages.join('、')}`);
                }
                return Promise.resolve();
              }}
              required
            >
              <Input htmlType="hidden" value={`activityStairs_lottery_${stairIndex}`} />
              <Box direction="column" padding={[0, 20, 0, 0]} style={{ width: '100%' }}>
                {/* 奖品操作行 */}
                <Box direction="row" align="center" margin={[0, 0, 10, 0]}>
                  <Button
                    type="primary"
                    onClick={() => handleAddPrize(stairIndex)}
                    disabled={(stair.lottery || []).length >= 3 || AF_Act_Form_Disabled}
                  >
                    {`添加奖品（${(stair.lottery || []).length}/3）`}
                  </Button>
                </Box>

                {/* 已添加的奖品列表 */}
                <Table dataSource={stair.lottery || []} hasBorder>
                  <Table.Column
                    title="奖品图片"
                    dataIndex="showImage"
                    cell={(value) => {
                      if (value) {
                        return <img src={value} alt="奖品" style={{ width: 60 }} />;
                      } else {
                        return '-';
                      }
                    }}
                  />
                  <Table.Column
                    title="奖品类型"
                    cell={(value, index, record) => getPrizeTypeLabel(record.lotteryType)}
                  />
                  <Table.Column title="奖品名称" dataIndex="lotteryName" />
                  <Table.Column
                    title="奖品价值"
                    dataIndex="price"
                    cell={(value, index, record) => {
                      if (record.lotteryType === PrizeTypeEnum.MEMBER_POINT.value) {
                        return `${value}积分`;
                      }
                      return `${value}元`;
                    }}
                  />
                  <Table.Column title="发放份数" dataIndex="prizeNum" />
                  <Table.Column title="限领规则" cell={() => '每人限领一次'} />
                  <Table.Column
                    title="操作"
                    cell={(value, index, record) => (
                      <Box direction="row" spacing={10}>
                        <Button
                          type="primary"
                          text
                          onClick={() => handleEditPrize(stairIndex, index, record)}
                          disabled={AF_Act_Form_Disabled}
                        >
                          编辑
                        </Button>
                        <Button
                          type="primary"
                          text
                          onClick={() => handleDeletePrize(stairIndex, index)}
                          disabled={AF_Act_Form_Disabled}
                        >
                          删除
                        </Button>
                      </Box>
                    )}
                  />
                </Table>
              </Box>
            </Form.Item>
          </Box>
        ))}
      </Box>

      {/* 添加阶梯按钮，最多5个 */}
      <Form.Item
        name="AF_activityStairs"
        label=" "
        validator={(_, __) => {
          const list = field.getValue('activityStairs');
          if (!list || list.length === 0) {
            return Promise.reject('请至少添加一个阶梯');
          }
          return Promise.resolve();
        }}
        autoValidate={false}
      >
        <Box direction="row">
          <Button
            disabled={stairs.length >= 5 || AF_Act_Form_Disabled}
            onClick={handleAddStair}
            type="secondary"
            style={{ width: 'calc(100% - 140px)', border: '1px dashed' }}
          >
            {`+ 添加阶梯（${stairs.length}/5）`}
          </Button>
        </Box>
      </Form.Item>

      {/* 用来校验同一个奖品在所有阶梯中的总数不超过其可用库存 */}
      <Form.Item
        name="AF_activityStairs_lottery"
        label=" "
        validator={async (_, __) => {
          // 当表单被禁用时，跳过校验
          if (AF_Act_Form_Disabled) return Promise.resolve();

          const list = field.getValue('activityStairs');
          const prizeMap = {};

          // 收集奖品，合并 PRACTICALITY 和 PRACTICALITY_BY_ORDER 类型
          list.forEach((stair) => {
            (stair.lottery || []).forEach((prize) => {
              const key = `${prize.lotteryValue}`; // 用 lotteryValue 合并共用库存
              if (!prizeMap[key]) {
                prizeMap[key] = {
                  total: 0,
                  frozen: 0, // 当前活动中此奖品已被冻结的库存
                  example: prize,
                  types: new Set(),
                };
              }
              prizeMap[key].total += Number(prize.prizeNum || 0);
              // 如果奖品有 id，则说明已绑定过，计入 frozen
              if (AF_Act_Not_Start && prize.id) {
                prizeMap[key].frozen += Number(prize.prizeNum || 0);
              }
              prizeMap[key].types.add(prize.lotteryType);
            });
          });
          console.log('收集所有奖品', prizeMap);

          const errors = [];
          // 按奖品类型去调不同接口获取库存
          for (const key in prizeMap) {
            const { total, frozen, example, types } = prizeMap[key];
            const prizeType = [...types][0]; // 取其中一个类型
            const value = example.lotteryValue;

            // 跳过积分奖品的库存校验
            if (prizeType === PrizeTypeEnum.MEMBER_POINT.value) {
              continue;
            }
            // 跳过优惠券的库存校验
            if (prizeType === PrizeTypeEnum.COUPON.value) {
              continue;
            }
            try {
              let stock = 0;
              let name = '';

              if (
                prizeType === PrizeTypeEnum.PRACTICALITY.value ||
                prizeType === PrizeTypeEnum.PRACTICALITY_BY_ORDER.value
              ) {
                const res = await prizeGetSkuInfo({ skuCode: value });
                stock = res.remainPrizeQuantity;
                name = res.benefitName;
              }
              const available = AF_Act_Not_Start ? stock + frozen : stock;
              if (total > available) {
                errors.push(`【${name}】在所有阶梯中的总数不能超过其可用库存 ${available}`);
              }
            } catch (e) {
              console.warn('库存校验失败', e);
            }
          }

          if (errors.length) {
            return Promise.reject(errors.join('\n'));
          }
          return Promise.resolve();
        }}
        autoValidate={false}
        style={{ height: 0, overflow: 'hidden' }}
      >
        <Input htmlType="hidden" value="activityStairs_lottery" />
      </Form.Item>
    </Container>
  );
}
