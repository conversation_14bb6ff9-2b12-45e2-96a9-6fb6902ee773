import { Form, Radio } from '@alifd/next';

/**
 * 领奖方式组件
 *
 * 字段:
 *  - giftRule.autoSend (boolean)
 *    true => 自动领奖
 *    false => 手动领奖
 */
export default function FormItemGiftAutoSend(props) {
  const { field } = props;
  const AF_Act_Form_Disabled = field.getValue('AF_Act_Form_Disabled');

  // 获取当前值，如果还没有则默认 false（手动领奖），按需调整
  const currentValue = field.getValue('giftRule.autoSend') ?? false;

  // 将布尔值映射为单选组的 'auto'/'manual' 选项
  // 根据 currentValue 判断选中哪个
  const radioValue = currentValue ? 'auto' : 'manual';

  // 当单选改变时，更新布尔值
  const handleChange = val => {
    field.setValue('giftRule.autoSend', val === 'auto');
  };

  return (
    <Form.Item
      label="领奖方式"
      required
      requiredMessage="请选择领奖方式"
      disabled={AF_Act_Form_Disabled}
    >
      <Radio.Group
        value={radioValue}
        onChange={handleChange}
      >
        <Radio value="auto">
          自动领奖
          <span style={{ color: 'var(--color-text-regular)' }}>
            （用户达成任务条件后自动发放奖品，无需再次进入活动页面）
          </span>
        </Radio>
        <br />
        <Radio value="manual">
          手动领奖
          <span style={{ color: 'var(--color-text-regular)' }}>
            （用户达成任务条件后，需再次进入活动后领取奖品）
          </span>
        </Radio>
      </Radio.Group>
    </Form.Item>
  );
}
