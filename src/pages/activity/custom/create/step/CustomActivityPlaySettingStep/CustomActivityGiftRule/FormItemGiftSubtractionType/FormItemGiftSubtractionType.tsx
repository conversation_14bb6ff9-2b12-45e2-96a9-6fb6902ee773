import { Form, Radio } from '@alifd/next';

/**
 * 奖品库存扣减方式组件
 *
 * 字段:
 *  - giftRule.subtractionType (number)
 *    1 => 发放时扣减
 *    2 => 预约时扣减
 */
export default function FormItemGiftSubtractionType(props) {
  const { field } = props;
  const AF_Act_Form_Disabled = field.getValue('AF_Act_Form_Disabled');

  return (
    <Form.Item
      name="AF_giftRule_subtractionType"
      label="奖品库存扣减方式"
      required
      requiredMessage="请选择奖品库存扣减方式"
      disabled={AF_Act_Form_Disabled}
    >
      <Radio.Group
        value={field.getValue('giftRule.subtractionType') ?? 1}
        onChange={val => field.setValue('giftRule.subtractionType', val)}
      >
        <Radio value={1}>
          发放时扣减
          <span style={{ color: 'var(--color-text-regular)' }}>
            （用户达成任务条件后发放/领取奖品时扣减库存，可能出现奖品不足的情况）
          </span>
        </Radio>
        {/* <br /> */}
        {/* <Radio value={2}> */}
        {/*   预约时扣减 */}
        {/*   <span style={{ color: 'var(--color-text-regular)' }}>（用户预约成功后即锁定扣减库存）</span> */}
        {/* </Radio> */}
      </Radio.Group>
    </Form.Item>
  );
}
