import Container from '@/components/Container';

import FormItemGiftAutoSend from './FormItemGiftAutoSend/FormItemGiftAutoSend';
import FormItemGiftSubtractionType from './FormItemGiftSubtractionType/FormItemGiftSubtractionType';
import FormItemGiftReSubtractionType from './FormItemGiftReSubtractionType/FormItemGiftReSubtractionType';

export default function CustomActivityGiftRule(props) {
  return (
    <Container title="领奖规则">
      <FormItemGiftAutoSend {...props} />
      <FormItemGiftSubtractionType {...props} />
      <FormItemGiftReSubtractionType {...props} />
    </Container>
  );
}
