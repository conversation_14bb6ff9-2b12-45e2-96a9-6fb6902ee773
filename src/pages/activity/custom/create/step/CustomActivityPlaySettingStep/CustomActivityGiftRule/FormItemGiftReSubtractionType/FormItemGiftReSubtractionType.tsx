import { Form, Radio } from '@alifd/next';

/**
 * 奖品库存回撤方式组件
 *
 * 字段:
 *  - giftRule.reSubtractionType (number)
 *    1 => 回撤至剩余库存
 *    （如果后续新增方式，可添加更多选项）
 */
export default function FormItemGiftReSubtractionType(props) {
  const { field } = props;
  const AF_Act_Form_Disabled = field.getValue('AF_Act_Form_Disabled');

  return (
    <Form.Item
      name="AF_giftRule_reSubtractionType"
      label="奖品库存回撤方式"
      required
      requiredMessage="请选择奖品库存回撤方式"
      disabled={AF_Act_Form_Disabled}
    >
      <Radio.Group
        value={field.getValue('giftRule.reSubtractionType') ?? 1}
        onChange={val => field.setValue('giftRule.reSubtractionType', val)}
      >
        <Radio value={1}>回撤至剩余库存</Radio>
      </Radio.Group>
    </Form.Item>
  );
}
