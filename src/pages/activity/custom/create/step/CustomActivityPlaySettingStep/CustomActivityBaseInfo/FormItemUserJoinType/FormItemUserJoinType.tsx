import { Form, Radio } from '@alifd/next';
import HelpTooltip from '@/components/HelpTooltip/HelpTooltip';

/**
 * 用户参与方式
 * 绑定到 baseInfo.joinType 字段
 */
export default function FormItemUserJoinType(props) {
  const { field } = props;

  const AF_Act_Form_Disabled = field.getValue('AF_Act_Form_Disabled');

  // '0无校验 1预约参与 2免预约',
  return (
    <Form.Item
      name="AF_baseInfo_joinType"
      label="用户参与方式"
      required
      disabled={AF_Act_Form_Disabled}
    >
      <Radio.Group
        value={field.getValue('baseInfo.joinType')}
        onChange={value => field.setValue('baseInfo.joinType', value)}
      >
        <Radio value={1}>
          预约参与
          <HelpTooltip content={'客户需点击【预约按钮】才视为报名参与活动'} />
        </Radio>
        <Radio value={2}>
          免预约参与
          <HelpTooltip content={'用户进入活动浏览即算作参与活动，无需点击立即预约按钮'} />
        </Radio>
      </Radio.Group>
    </Form.Item>
  );
}
