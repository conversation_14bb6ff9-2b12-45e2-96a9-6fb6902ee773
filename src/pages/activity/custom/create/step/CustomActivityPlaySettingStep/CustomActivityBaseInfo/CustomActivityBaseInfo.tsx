import Container from '@/components/Container';
import FormItemActivityTime
  from '@/pages/activity/custom/create/components/ActivityFormItem/FormItemActivityTime/FormItemActivityTime';
import FormItemActivityName
  from '@/pages/activity/custom/create/components/ActivityFormItem/FormItemActivityName/FormItemActivityName';
import FormItemUserJoinType from './FormItemUserJoinType/FormItemUserJoinType';

export default function CustomActivityBaseInfo(props) {
  return (
    <Container title="基本信息">
      <FormItemActivityName {...props} />
      <FormItemActivityTime {...props} />
      <FormItemUserJoinType {...props} />
    </Container>
  );
}
