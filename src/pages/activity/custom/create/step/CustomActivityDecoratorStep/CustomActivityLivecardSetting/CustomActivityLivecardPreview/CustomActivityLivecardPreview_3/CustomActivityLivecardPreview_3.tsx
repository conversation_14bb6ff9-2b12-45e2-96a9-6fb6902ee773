import React from 'react';
import { Box } from '@alifd/next';
import './CustomActivityLivecardPreview_3.scss';

export default function CustomActivityLivecardPreview_3({ field }) {
  const cardJson = field.getValue('cardJson') || {};

  const {
    bgImage_3, // livecard背景(大卡)
    thresholdBgColor, // 任务条件背景色
    thresholdTextColor, // 任务条件文字色
    totalProgressBgColor, // 达成总进度背景色
    totalProgressTextColor, // 达成总进度文字色
    mainBtnBgColor, // 按钮背景色
    mainBtnTextColor, // 按钮文字色
    countdownTextColor, // 倒计时文字色
  } = cardJson;

  return (
    <div
      className="custom-activity-livecard-preview-3"
      style={{ backgroundImage: `url(${bgImage_3})` }}
    >
      <div className="livecard-header">
        <div className="livecard-title">购物阶梯挑战</div>
      </div>
      <div
        className="countdown"
        style={{ color: `${countdownTextColor}` }}
      >
        <span className="label">距活动结束还剩</span>
        <span className="time-num">12</span>
        <span className="time-unit">天</span>
        <span className="time-num">12</span>
        <span className="time-unit">时</span>
        <span className="time-num">40</span>
        <span className="time-unit">分</span>
      </div>
      <div className="prize-list">
        {new Array(3).fill(undefined).map((_, index) => (
          <div
            className="prize-item"
            key={index}
          >
            <img
              className="prize-image"
              src={'https://img.alicdn.com/imgextra/i3/155168396/O1CN01uWOHY22BtQQ0EqcoP_!!155168396.png'}
            />
            <div className="prize-info">
              <div className="prize-title text-overflow-line">奖品名称奖品名称奖品名称奖品名称</div>
              <div className="prize-remain">剩余：88份</div>
            </div>
          </div>
        ))}
      </div>
      <div className="live-card-stair">
        <div className="stair-conditions">
          <div
            className="cond-item"
            style={{ backgroundColor: `${thresholdBgColor}`, color: `${thresholdTextColor}` }}
          >
            <span className="cond-value">1500元</span>
            <span className="cond-label">累计金额满</span>
          </div>
          <div
            className="cond-item"
            style={{ backgroundColor: `${thresholdBgColor}`, color: `${thresholdTextColor}` }}
          >
            <span className="cond-value">15单</span>
            <span className="cond-label">累计购物满</span>
          </div>
          <div
            className="cond-item"
            style={{ backgroundColor: `${thresholdBgColor}`, color: `${thresholdTextColor}` }}
          >
            <span className="cond-value">30件</span>
            <span className="cond-label">累计购买商品满</span>
          </div>
        </div>
        <div
          className="progress-summary"
          style={{ color: `${totalProgressTextColor}`, backgroundColor: `${totalProgressBgColor}` }}
        >
          <span className="label">已达成总进度：</span>
          <span className="value">{800}</span>
          <span>元</span>
          <span className="separator">＋</span>
          <span className="value">{5}</span>
          <span>单</span>
          <span className="separator">＋</span>
          <span className="value">{3}</span>
          <span>件商品</span>
        </div>
        <div
          className="order-button"
          style={{ color: `${mainBtnTextColor}`, backgroundColor: `${mainBtnBgColor}` }}
        >
          立即预约
        </div>
      </div>
    </div>
  );
}
