import React from 'react';
import { Box } from '@alifd/next';
import './CustomActivityLivecardPreview.scss';

import CustomActivityLivecardPreview_1 from './CustomActivityLivecardPreview_1/CustomActivityLivecardPreview_1';
import CustomActivityLivecardPreview_2 from './CustomActivityLivecardPreview_2/CustomActivityLivecardPreview_2';
import CustomActivityLivecardPreview_3 from './CustomActivityLivecardPreview_3/CustomActivityLivecardPreview_3';

export default function CustomActivityLivecardPreview({ field, scale = 1 }) {
  const cardSize = field.getValue('cardSize') || 1; // 1 小卡、2 中卡、3 大卡

  const baseHeightMap = {
    1: 346,
    2: 726,
    3: 968,
  };
  const baseWidth = 762; // 原始容器宽度
  const baseHeight = baseHeightMap[cardSize] || baseHeightMap[1];

  const cardPreviewMap = {
    1: CustomActivityLivecardPreview_1,
    2: CustomActivityLivecardPreview_2,
    3: CustomActivityLivecardPreview_3,
  };

  const CardPreviewComponent = cardPreviewMap[cardSize];

  return (
    <Box
      className="custom-activity-livecard-preview"
      style={{
        width: `${baseWidth * scale}px`,
        height: `${baseHeight * scale}px`,
        transform: `scale(${scale})`,
        transformOrigin: 'top left',
      }}
    >
      {CardPreviewComponent ? <CardPreviewComponent field={field} /> : null}
    </Box>
  );
}
