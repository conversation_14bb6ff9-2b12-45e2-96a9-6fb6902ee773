.custom-activity-livecard-preview-1 {
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 726px;
  height: 346px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .livecard-header {
    display: flex;
    height: 63px;
  }

  .livecard-title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 256px;
    height: 48px;
    color: #000;
    font-size: 30px;
  }

  .countdown {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    height: 100%;

    .label {
      font-size: 20px;
    }

    .time-num {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      margin: 0 5px;
      font-size: 20px;
      background-color: #fff;
      border-radius: 9px;
    }

    .time-unit {
      font-size: 20px;
    }
  }

  .livecard-content {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
  }

  .prize-item {
    position: relative;
    width: 209px;
    height: 241px;

    .prize-image {
      width: 100%;
      height: 100%;
    }

    .prize-info {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 60px;
      padding: 0 15px;
    }

    .prize-title {
      width: 100%;
      padding: 5px 0 0;
      font-size: 18px;
    }

    .prize-remain {
      font-size: 18px;
    }
  }

  .live-card-stair {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 462px;
    height: 100%;
    padding-top: 20px;
  }

  .stair-conditions {
    display: flex;
    justify-content: space-between;
    width: 100%;

    .cond-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-evenly;
      width: 147px;
      height: 73px;
      border-radius: 14px;
    }

    .cond-value {
      font-size: 22px;
    }

    .cond-label {
      font-size: 20px;
    }
  }

  .progress-summary {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 45px;
    margin: 18px 0 28px;
    font-size: 24px;
    border-radius: 23px;

    .value {
      color: #ef1010;
    }
  }

  .order-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 402px;
    height: 61px;
    font-size: 28px;
    border-radius: 11px;
  }
}
