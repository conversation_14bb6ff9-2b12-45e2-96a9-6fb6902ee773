.custom-activity-livecard-preview-2 {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 726px;
  height: 726px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .livecard-header {
    display: flex;
    width: 100%;
    height: 63px;
  }

  .livecard-title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 306px;
    height: 100%;
    color: #000;
    font-size: 30px;
  }

  .countdown {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 70px;

    .label {
      font-size: 27px;
    }

    .time-num {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 49px;
      margin: 0 7px;
      font-size: 27px;
      background-color: #fff;
      border-radius: 9px;
    }

    .time-unit {
      font-size: 27px;
    }
  }

  .prize-list {
    display: flex;
    justify-content: center;
    padding: 0 20px;
  }

  .prize-item {
    position: relative;
    width: 209px;
    height: 241px;
    margin: 0 10px;

    .prize-image {
      width: 100%;
      height: 100%;
    }

    .prize-info {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 60px;
      padding: 0 15px;
    }

    .prize-title {
      width: 100%;
      padding: 5px 0 0;
      font-size: 18px;
    }

    .prize-remain {
      font-size: 18px;
    }
  }

  .live-card-stair {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 23px;
  }

  .stair-conditions {
    display: flex;
    justify-content: center;
    width: 100%;

    .cond-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-evenly;
      width: 168px;
      height: 84px;
      margin: 0 12px;
      border-radius: 14px;
    }

    .cond-value {
      font-size: 25px;
    }

    .cond-label {
      font-size: 23px;
    }
  }

  .progress-summary {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 528px;
    height: 51px;
    margin: 18px 0 36px;
    font-size: 27px;
    border-radius: 30px;

    .value {
      color: #ef1010;
    }
  }

  .order-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 532px;
    height: 82px;
    font-size: 37px;
    border-radius: 11px;
  }
}
