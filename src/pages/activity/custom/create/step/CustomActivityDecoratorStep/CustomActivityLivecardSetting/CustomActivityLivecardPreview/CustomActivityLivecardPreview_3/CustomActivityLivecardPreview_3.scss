.custom-activity-livecard-preview-3 {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 726px;
  height: 968px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  .livecard-header {
    display: flex;
    width: 100%;
    height: 86px;
  }

  .livecard-title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 365px;
    height: 100%;
    color: #000;
    font-size: 30px;
  }

  .countdown {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 50px 0 12px;

    .label {
      font-size: 35px;
    }

    .time-num {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 63px;
      height: 61px;
      margin: 0 9px;
      font-size: 35px;
      background-color: #fff;
      border-radius: 9px;
    }

    .time-unit {
      font-size: 35px;
    }
  }

  .prize-list {
    display: flex;
    justify-content: center;
    padding: 0 25px;
  }

  .prize-item {
    position: relative;
    width: 219px;
    height: 296px;
    margin: 0 10px;

    .prize-image {
      width: 100%;
      height: 100%;
    }

    .prize-info {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 74px;
      padding: 0 15px;
    }

    .prize-title {
      width: 100%;
      padding: 10px 0 0;
      font-size: 21px;
    }

    .prize-remain {
      font-size: 21px;
    }
  }

  .live-card-stair {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 42px;
  }

  .stair-conditions {
    display: flex;
    justify-content: center;
    width: 100%;

    .cond-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-evenly;
      width: 207px;
      height: 102px;
      margin: 0 15px;
      border-radius: 14px;
    }

    .cond-value {
      font-size: 31px;
    }

    .cond-label {
      font-size: 28px;
    }
  }

  .progress-summary {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 637px;
    height: 61px;
    margin: 28px 0 70px;
    font-size: 33px;
    border-radius: 30px;

    .value {
      color: #ef1010;
    }
  }

  .order-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 636px;
    height: 97px;
    font-size: 48px;
    border-radius: 11px;
  }
}
