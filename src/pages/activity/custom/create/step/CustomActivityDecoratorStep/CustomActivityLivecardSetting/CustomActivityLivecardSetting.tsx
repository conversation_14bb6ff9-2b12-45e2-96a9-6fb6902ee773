import React from 'react';
import { Box, Select, Form, Input, Message } from '@alifd/next';

// import ImgUploadFromItem from '@step/DecorationFormItem/ImgUploadFromItem/ImgUploadFromItem';
// import ColorPickerFormItem from '@step/DecorationFormItem/ColorPickerFormItem/ColorPickerFormItem';

import ImgUploadFromItem from '@/components/ImgUpload';
import ColorPickerFormItem from '@/components/ColorPickerFormItem/ColorPickerFormItem';
import CustomActivityLivecardPreview from './CustomActivityLivecardPreview/CustomActivityLivecardPreview';
import './CustomActivityLivecardSetting.scss';

export default function CustomActivityLivecardSetting({ field }) {
  const AF_Act_Is_View = field.getValue('AF_Act_Is_View');

  // 更新小部件装修
  const updateCardJson = (key, value) => {
    const currentJson = field.getValue('cardJson') || {};
    currentJson[key] = value;
    field.setValue('cardJson', currentJson);
  };
  const cardSize = field.getValue('cardSize') || 1; // 1 小卡、2 中卡、3 大卡

  return (
    <Box
      direction="row"
      padding={[20, 0, 0]}
      spacing={16}
    >
      <Box spacing={16}>
        <Message
          type="warning"
          className="custom-activity-livecard-setting-message"
        >
          <h5>通用列表入口说明：</h5>
          <p>
            通用列表入口：指的是活动的入口集合页，在活动推广时可将多个活动入口卡片集合在一个页面上由用户自行选择想要进入的活动          </p>
          <h5>使用说明：</h5>
          <p>
            在此处配置活动入口样式后，可在【活动推广-通用列表页】中配置投放活动，用户进入后可根据千人千面条件查看对应活动入口          </p>
        </Message>
        {/* <Form.Item */}
        {/*   name="cardSize" */}
        {/*   label="livecard 尺寸" */}
        {/*   required */}
        {/*   requiredMessage="请选择 livecard 大小" */}
        {/*   disabled={AF_Act_Is_View} */}
        {/* > */}
        {/*   <Select */}
        {/*     value={field.getValue('cardSize') || 1} */}
        {/*     onChange={val => { */}
        {/*       field.setValue('cardSize', val); */}
        {/*     }} */}
        {/*     placeholder="请选择卡片大小" */}
        {/*   > */}
        {/*     <Select.Option value={1}>小卡 (726px × 346px)</Select.Option> */}
        {/*     <Select.Option value={2}>中卡 (726px × 726px)</Select.Option> */}
        {/*     <Select.Option value={3}>大卡 (726px × 968px)</Select.Option> */}
        {/*   </Select> */}
        {/* </Form.Item> */}
        {cardSize === 1 && (
          <ImageDecoration
            field={field}
            updateCardJson={updateCardJson}
            item={{
              key: 'bgImage_1',
              label: '卡片背景',
              width: 726,
              height: 346,
            }}
          />
        )}
        {cardSize === 2 && (
          <ImageDecoration
            field={field}
            updateCardJson={updateCardJson}
            item={{
              key: 'bgImage_2',
              label: 'livecard背景(中卡)',
              width: 726,
              height: 726,
            }}
          />
        )}
        {cardSize === 3 && (
          <ImageDecoration
            field={field}
            updateCardJson={updateCardJson}
            item={{
              key: 'bgImage_3',
              label: 'livecard背景(大卡)',
              width: 726,
              height: 968,
            }}
          />
        )}
        <ColorDecoration
          field={field}
          updateCardJson={updateCardJson}
          item={{
            key: 'thresholdBgColor',
            label: '任务条件背景色',
          }}
        />
        <ColorDecoration
          field={field}
          updateCardJson={updateCardJson}
          item={{
            key: 'thresholdTextColor',
            label: '任务条件文字色',
          }}
        />
        <ColorDecoration
          field={field}
          updateCardJson={updateCardJson}
          item={{
            key: 'totalProgressBgColor',
            label: '达成总进度背景色',
          }}
        />
        <ColorDecoration
          field={field}
          updateCardJson={updateCardJson}
          item={{
            key: 'totalProgressTextColor',
            label: '达成总进度文字色',
          }}
        />
        <ColorDecoration
          field={field}
          updateCardJson={updateCardJson}
          item={{
            key: 'mainBtnBgColor',
            label: '按钮背景色',
          }}
        />
        <ColorDecoration
          field={field}
          updateCardJson={updateCardJson}
          item={{
            key: 'mainBtnTextColor',
            label: '按钮文字色',
          }}
        />
        <ColorDecoration
          field={field}
          updateCardJson={updateCardJson}
          item={{
            key: 'countdownTextColor',
            label: '倒计时文字色',
          }}
        />
      </Box>
      <CustomActivityLivecardPreview
        field={field}
        scale={0.8}
      />
    </Box>
  );
}

function ImageDecoration({ field, item, updateCardJson }) {
  const cardJson = field.getValue('cardJson') || {};
  const AF_Act_Is_View = field.getValue('AF_Act_Is_View');

  return (
    <ImgUploadFromItem
      disabled={AF_Act_Is_View}
      label={item.label}
      required
      img={{
        value: cardJson[item.key],
        width: item.width,
        height: item.height,
      }}
      onSuccess={url => {
        updateCardJson(item.key, url);
      }}
      onReset={url => {
        updateCardJson(item.key, url);
      }}
    />
  );
}
function ColorDecoration({ field, item, updateCardJson }) {
  const cardJson = field.getValue('cardJson') || {};
  const AF_Act_Is_View = field.getValue('AF_Act_Is_View');

  return (
    <>
      <ColorPickerFormItem
        disabled={AF_Act_Is_View}
        label={item.label}
        required
        color={{
          value: cardJson[item.key],
        }}
        onSetColor={color => {
          updateCardJson(item.key, color);
        }}
        onReset={color => {
          updateCardJson(item.key, color);
        }}
      />
    </>
  );
}
