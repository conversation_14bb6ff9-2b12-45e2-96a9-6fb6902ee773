import React from 'react';
import { Box, Slider } from '@alifd/next';
import './CustomActivityMiniAppPreview.scss';

export default function CustomActivityMiniAppPreview({ field, showDialog }) {
  const atmosphereJson = field.getValue('baseInfo.atmosphereJson') || {};

  const {
    kvImageUrl, // kv图
    countdownBgImageUrl, // 倒计时背景图
    countdownTextColor, // 倒计时字色
    leftTopBtnBgColor, // 规则和领取记录按钮背景色
    leftTopBtnTextColor, // 规则和领取记录按钮字色
    stairNameTextColor, // 阶梯名字字色
    thresholdBoxBgColor, // 购物指标块背景色
    thresholdBoxTextColor, // 物指标块字色
    pageBgColor, // 页面背景色
    recommendSkuTitleTextColor, // 推荐商品字色
    stairBgImageUrl, // 奖品底框背景图
    prizeNameTextColor, // 奖品名称字色
    prizePriceTextColor, // 奖品价格字色
    prizeInventoryTextColor, // 奖品库存字色
    stairBtnBgImageUrl, // 预约/领奖按钮背景图
    stairBtnTextColor, // 预约/领奖按钮字色
    toShoppingBtnToUrl, // 去购物按钮引导跳转链接
    progressBarNode_notReachedImageUrl, // 进度条节点，未达成
    progressBarNode_reachedImageUrl, // 进度条节点，已达成
    progressBarNode_textColor, // 进度条字色
    progressBarReachedBgColor, // 进度条达成背景色
    totalProgressBgColor, // 总进度背景色-支持rgba
    totalProgressTextColor, // 总进度字色
    popupBgImageUrl, // 弹窗背景图
    popupMainBtnBgColor, // 弹窗主要按钮背景色
    popupMainBtnTextColor, // 弹窗主要按钮字色
    specialPopupBgImageUrl, // 特殊弹窗背景 领取记录和不符合业务门槛
  } = atmosphereJson;

  const prizeItems = [
    {
      imgUrl: 'https://img.alicdn.com/imgextra/i3/155168396/O1CN01Pzj0ow2BtQPwm9lMA_!!155168396.png',
      title: '奖品名称奖品名称奖品名称奖品名称',
      price: '1698.00',
      remain: 88,
    },
    {
      imgUrl: 'https://img.alicdn.com/imgextra/i3/155168396/O1CN01Pzj0ow2BtQPwm9lMA_!!155168396.png',
      title: '奖品名称奖品名称奖品名称奖品名称',
      price: '1899.00',
      remain: 12,
    },
    {
      imgUrl: 'https://img.alicdn.com/imgextra/i3/155168396/O1CN01Pzj0ow2BtQPwm9lMA_!!155168396.png',
      title: '奖品名称奖品名称奖品名称奖品名称',
      price: '999.00',
      remain: 30,
    },
  ];

  const skuItems = [
    {
      imgUrl: 'https://img.alicdn.com/imgextra/i3/155168396/O1CN01ZkYDLW2BtQPt9MH1f_!!155168396.png',
      title: '羽绒内胆三合一550蓬鹅绒防水透气冲锋衣保暖羽绒蓬鹅绒防水透气',
      price: '1698',
    },
    {
      imgUrl: 'https://img.alicdn.com/imgextra/i3/155168396/O1CN01ZkYDLW2BtQPt9MH1f_!!155168396.png',
      title: '羽绒内胆三合一550蓬鹅绒防水透气冲锋衣保暖羽绒蓬鹅绒防水透气',
      price: '1698',
    },
  ];

  // 示例：假设有 5 个阶梯；1、2已完成，3正在进行，4、5未开始
  const steps = [
    { label: '阶梯1', status: 'done', line_1: 0.6, line_2: 0.4 },
    { label: '阶梯2', status: 'done', line_1: 0.5, line_2: 0.5 },
    { label: '阶梯3', status: 'current', line_1: 0.5, line_2: 0.5 },
    { label: '阶梯4', status: 'todo', line_1: 0.5, line_2: 0.5 },
    { label: '阶梯5', status: 'todo', line_1: 0.4, line_2: 0.6 },
  ];

  return (
    <div
      className="custom-activity-mini-app-preview"
      style={{ backgroundImage: `url(${kvImageUrl})`, backgroundColor: `${pageBgColor}` }}
    >
      {/* 按钮 */}
      <Box
        className="top-left-btn"
        spacing={8.5}
      >
        <span style={{ backgroundColor: `${leftTopBtnBgColor}`, color: `${leftTopBtnTextColor}` }}>活动规则</span>
        <span style={{ backgroundColor: `${leftTopBtnBgColor}`, color: `${leftTopBtnTextColor}` }}>领奖记录</span>
      </Box>
      {/* 倒计时区域 */}
      <div
        className="countdown"
        style={{ backgroundImage: `url(${countdownBgImageUrl})` }}
      >
        <span
          className="label"
          style={{ color: `${countdownTextColor}` }}
        >
          距活动结束还剩
        </span>
        <span
          className="time-num"
          style={{ color: `${countdownTextColor}` }}
        >
          12
        </span>
        <span className="time-unit">天</span>
        <span
          className="time-num"
          style={{ color: `${countdownTextColor}` }}
        >
          12
        </span>
        <span className="time-unit">时</span>
        <span
          className="time-num"
          style={{ color: `${countdownTextColor}` }}
        >
          40
        </span>
        <span className="time-unit">分</span>
      </div>
      {/* 阶梯标题 + 阶梯信息 */}
      <div
        className="stair-section"
        style={{ backgroundImage: `url(${stairBgImageUrl})` }}
      >
        <h2
          className="stair-title"
          style={{ color: `${stairNameTextColor}` }}
        >
          - 阶梯03 -
        </h2>
        <div className="stair-conditions">
          <div
            className="cond-item"
            style={{ backgroundColor: `${thresholdBoxBgColor}`, color: `${thresholdBoxTextColor}` }}
          >
            <span className="cond-value">1500元</span>
            <span className="cond-label">累计金额满</span>
          </div>
          <div
            className="cond-item"
            style={{ backgroundColor: `${thresholdBoxBgColor}`, color: `${thresholdBoxTextColor}` }}
          >
            <span className="cond-value">15单</span>
            <span className="cond-label">累计购物满</span>
          </div>
          <div
            className="cond-item"
            style={{ backgroundColor: `${thresholdBoxBgColor}`, color: `${thresholdBoxTextColor}` }}
          >
            <span className="cond-value">30件</span>
            <span className="cond-label">累计购买商品满</span>
          </div>
        </div>
        <div className="prize-list">
          {prizeItems.map((item, index) => (
            <div
              className="prize-item"
              key={index}
            >
              <img
                className="prize-image"
                src={item.imgUrl}
              />
              <div className="prize-info">
                <div
                  className="prize-title text-overflow-line"
                  style={{ color: `${prizeNameTextColor}` }}
                >
                  {item.title}
                </div>
                <div className="prize-price-remain">
                  <div
                    className="prize-price"
                    style={{ color: `${prizePriceTextColor}` }}
                  >
                    <span>¥</span>
                    <span>{item.price}</span>
                  </div>
                  <div
                    className="prize-remain"
                    style={{ color: `${prizeInventoryTextColor}` }}
                  >
                    剩余：{item.remain}份
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        <div className="progress-container">
          {steps.map((item, index) => {
            let progressLineBgColor = item.status === 'done' ? progressBarReachedBgColor : '#666666';
            let progressIconBgColor = item.status === 'current' ? '#fff' : 'transparent';
            let progressIcon =
              item.status === 'done' ? progressBarNode_reachedImageUrl : progressBarNode_notReachedImageUrl;
            let progressLabelColor = item.status === 'done' ? progressBarNode_textColor : '#666666';

            return (
              <div
                key={index}
                className="progress-item"
              >
                <div
                  className="progress-line"
                  style={{ backgroundColor: progressLineBgColor }}
                />
                <div
                  className="progress-icon"
                  style={{ backgroundColor: progressIconBgColor }}
                >
                  <img src={progressIcon} />
                  <span
                    className="progress-label"
                    style={{ color: progressLabelColor }}
                  >
                    {item.label}
                  </span>
                </div>
                <div
                  className="progress-line"
                  style={{ backgroundColor: progressLineBgColor }}
                />
              </div>
            );
          })}
        </div>
        <div
          className="progress-summary"
          style={{ color: `${totalProgressTextColor}`, backgroundColor: `${totalProgressBgColor}` }}
        >
          <span className="label">已达成总进度：</span>
          <span className="value">{800}</span>
          <span>元</span>
          <span className="separator">＋</span>
          <span className="value">{5}</span>
          <span>单</span>
          <span className="separator">＋</span>
          <span className="value">{3}</span>
          <span>件商品</span>
        </div>
        {/* 立即预约 */}
        <div
          className="appointment-button"
          style={{ color: `${stairBtnTextColor}`, backgroundImage: `url(${stairBtnBgImageUrl})` }}
        >
          立即预约
        </div>
      </div>
      {/* 商品推荐 */}
      <div className="recommend-sku-container">
        <h2 style={{ color: `${recommendSkuTitleTextColor}` }}>- 商品推荐 -</h2>
        <div className="recommend-sku-list">
          {skuItems.map((item, index) => (
            <div
              className="recommend-sku-item"
              style={{ backgroundImage: `url(${item.imgUrl})` }}
              key={index}
            >
              <div className="recommend-sku-info">
                <div className="recommend-sku-name text-overflow-2-line">{item.title}</div>
                <div className="recommend-sku-price-button">
                  <div className="recommend-sku-price">
                    <span className="price-symbol">¥</span>
                    <span className="price-value">{item.price}</span>
                  </div>
                  <div className="recommend-sku-button">去购买</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      {/* 弹窗 */}
      {showDialog && (
        <div className="popup-container">
          <div
            className="popup"
            style={{ backgroundImage: `url(${popupBgImageUrl})` }}
          >
            <img
              className="popup-inner-image"
              src="https://img.alicdn.com/imgextra/i4/155168396/O1CN012cM8e52BtQPuwPwFj_!!155168396.png"
            />
            <div
              className="popup-button"
              style={{ backgroundColor: `${popupMainBtnBgColor}` }}
            >
              <div
                className="popup-button-text"
                style={{ color: `${popupMainBtnTextColor}` }}
              >
                去下单
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
