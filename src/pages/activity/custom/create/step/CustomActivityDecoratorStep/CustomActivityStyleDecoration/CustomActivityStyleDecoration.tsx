import React, { useState } from 'react';
import { Tab, Box, Form, Radio, Input } from '@alifd/next';

import ImgUploadFromItem from '@/components/ImgUpload';
import ColorPickerFormItem from '@/components/ColorPickerFormItem/ColorPickerFormItem';


import './CustomActivityStyleDecoration.scss';
import HelpTooltip from '@/components/HelpTooltip/HelpTooltip';

export default function CustomActivityStyleDecoration({ field, setStyleIsPopup }) {
  const [activeKey, setActiveKey] = useState('main');
  const AF_Act_Is_View = field.getValue('AF_Act_Is_View');

  // 更新小程序装修
  const updateAtmosphereJson = (key, value) => {
    const currentJson = field.getValue('baseInfo.atmosphereJson') || {};
    currentJson[key] = value;
    field.setValue('baseInfo.atmosphereJson', currentJson);
  };
  const atmosphereJson = field.getValue('baseInfo.atmosphereJson') || {};

  console.log(atmosphereJson, 'atmosphereJson');
  return (
    <Box
      padding={[20, 0, 0]}
      className="custom-activity-style-decoration"
    >
      <Tab
        activeKey={activeKey}
        onChange={key => {
          setActiveKey(key);
          setStyleIsPopup(key === 'popup');
        }}
        shape="capsule"
      >
        <Tab.Item
          key="main"
          title="主页面"
        >
          <Box
            direction="row"
            padding={[20, 0, 0, 0]}
            wrap
          >
            <Box margin={[0, 30, 0, 0]}>
              <ImageDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'kvImageUrl',
                  label: '活动KV图',
                  width: 750,
                  height: 574,
                }}
              />
              <ImageDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'countdownBgImageUrl',
                  label: '倒计时背景',
                  width: 680,
                  height: 80,
                }}
              />
              <ColorDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'countdownTextColor',
                  label: '倒计时字色',
                }}
              />
              <ColorDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'leftTopBtnTextColor',
                  label: '规则和领取记录按钮字色',
                }}
              />
              <ColorDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'leftTopBtnBgColor',
                  label: '规则和领取记录按钮背景色',
                }}
              />
              <ColorDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'stairNameTextColor',
                  label: '阶梯名字字色',
                }}
              />
              <ColorDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'thresholdBoxBgColor',
                  label: '购物指标块背景色',
                }}
              />
              <ColorDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'thresholdBoxTextColor',
                  label: '购物指标块文字色',
                }}
              />
              <ColorDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'pageBgColor',
                  label: '页面背景色',
                }}
              />
              <ColorDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'recommendSkuTitleTextColor',
                  label: '商品推荐字色',
                }}
              />
            </Box>
            <Box margin={[0, 30, 0, 0]}>
              <ImageDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'stairBgImageUrl',
                  label: '奖品底框背景图',
                  width: 750,
                  height: 1135,
                }}
              />
              <ColorDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'prizeNameTextColor',
                  label: '奖品名称字色',
                }}
              />
              <ColorDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'prizePriceTextColor',
                  label: '奖品价格字色',
                }}
              />
              <ColorDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'prizeInventoryTextColor',
                  label: '奖品库存字色',
                }}
              />
              <ImageDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'stairBtnBgImageUrl',
                  label: '预约/领奖按钮背景图',
                  width: 504,
                  height: 96,
                }}
              />
              <ColorDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'stairBtnTextColor',
                  label: '预约/领奖按钮字色',
                }}
              />
              <Form.Item
                defaultValue={1}
                name={'AF_baseInfo_toShoppingBtnType'}
                label={
                  <Box
                    direction="row"
                    align={'center'}
                    spacing={5}
                  >
                    <span>购物引导按钮跳转</span>
                  </Box>
                }
              >
                <Radio.Group
                  value={atmosphereJson['toShoppingBtnType']}
                  onChange={val => (
                    updateAtmosphereJson('toShoppingBtnType', val),
                    updateAtmosphereJson('toShoppingBtnValue', '')
                  )}
                  disabled={AF_Act_Is_View}
                >
                  <Radio value={1}>
                    店铺首页
                  </Radio>
                  {/* <Radio value={2}>
                    店铺自定义页面
                  </Radio>
                  <Radio value={3}>
                    商品详情页
                  </Radio> */}
                </Radio.Group>
                {/* <Input */}
                {/*   disabled={AF_Act_Is_View} */}
                {/*   trim */}
                {/*   composition */}
                {/*   value={atmosphereJson['toShoppingBtnToUrl']} */}
                {/*   onChange={val => updateAtmosphereJson('toShoppingBtnToUrl', val)} */}
                {/* /> */}
              </Form.Item>
              {
                atmosphereJson['toShoppingBtnType'] === 2 && <Form.Item name={'toShoppingBtnValue'} label={'自定义页面ID'} required requiredMessage={'请输入页面模块ID'}>
                  <Input value={atmosphereJson['toShoppingBtnValue']} onChange={val => updateAtmosphereJson('toShoppingBtnValue', val)} />
                </Form.Item>
              }
              {
                atmosphereJson['toShoppingBtnType'] === 3 && <Form.Item label={'商品ID'} name={'toShoppingBtnValue'} required requiredMessage={'请输入商品ID'}>
                  <Input value={atmosphereJson['toShoppingBtnValue']} onChange={val => updateAtmosphereJson('toShoppingBtnValue', val)} />
                  </Form.Item>
              }
            </Box>
            <Box>
              <ImageDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'progressBarNode_notReachedImageUrl',
                  label: '进度条节点，未达成',
                  width: 29,
                  height: 29,
                }}
              />
              <ImageDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'progressBarNode_reachedImageUrl',
                  label: '进度条节点，已达成',
                  width: 29,
                  height: 29,
                }}
              />
              <ColorDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'progressBarNode_textColor',
                  label: '进度条字色',
                }}
              />
              <ColorDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'progressBarReachedBgColor',
                  label: '进度条达成背景色',
                }}
              />
              <ColorDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'totalProgressBgColor',
                  label: '总进度背景色-支持rgba',
                }}
              />
              <ColorDecoration
                field={field}
                updateAtmosphereJson={updateAtmosphereJson}
                item={{
                  key: 'totalProgressTextColor',
                  label: '总进度字色',
                }}
              />
            </Box>
          </Box>
        </Tab.Item>
        <Tab.Item
          key="popup"
          title="弹窗"
        >
          <Box>
            <ImageDecoration
              field={field}
              updateAtmosphereJson={updateAtmosphereJson}
              item={{
                key: 'popupBgImageUrl',
                label: '弹窗背景图',
                width: 543,
                height: 669,
              }}
            />
            <ColorDecoration
              field={field}
              updateAtmosphereJson={updateAtmosphereJson}
              item={{
                key: 'popupMainBtnBgColor',
                label: '弹窗主要按钮背景色',
              }}
            />
            <ColorDecoration
              field={field}
              updateAtmosphereJson={updateAtmosphereJson}
              item={{
                key: 'popupMainBtnTextColor',
                label: '弹窗主要按钮字色',
              }}
            />
            <ImageDecoration
              field={field}
              updateAtmosphereJson={updateAtmosphereJson}
              item={{
                key: 'specialPopupBgImageUrl',
                label: '特殊弹窗背景 领取记录和不符合业务门槛',
                width: 750,
                height: 940,
              }}
            />
          </Box>
        </Tab.Item>
      </Tab>
    </Box>
  );
}

function ImageDecoration({ field, item, updateAtmosphereJson }) {
  const atmosphereJson = field.getValue('baseInfo.atmosphereJson') || {};
  const AF_Act_Is_View = field.getValue('AF_Act_Is_View');

  return (
    <ImgUploadFromItem
      disabled={AF_Act_Is_View}
      label={item.label}
      required
      img={{
        value: atmosphereJson[item.key],
        width: item.width,
        height: item.height,
      }}
      onSuccess={url => {
        updateAtmosphereJson(item.key, url);
      }}
      onReset={url => {
        updateAtmosphereJson(item.key, url);
      }}
    />
  );
}
function ColorDecoration({ field, item, updateAtmosphereJson }) {
  const atmosphereJson = field.getValue('baseInfo.atmosphereJson') || {};
  const AF_Act_Is_View = field.getValue('AF_Act_Is_View');

  return (
    <>
      <ColorPickerFormItem
        disabled={AF_Act_Is_View}
        label={item.label}
        required
        color={{
          value: atmosphereJson[item.key],
        }}
        onSetColor={color => {
          updateAtmosphereJson(item.key, color);
        }}
        onReset={color => {
          updateAtmosphereJson(item.key, color);
        }}
      />
    </>
  );
}
