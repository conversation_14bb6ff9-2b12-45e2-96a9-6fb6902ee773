.custom-activity-mini-app-preview {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100%;
  padding-top: 400px;
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100% auto;

  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    line-height: 1;
  }

  .top-left-btn {
    position: absolute;
    top: 116px;
    left: 0;

    span {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 146px;
      height: 43px;
      padding-right: 6px;
      font-size: 24px;
      border-radius: 0 22px 22px 0;
    }
  }

  .countdown {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 680px;
    height: 80px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;

    .label {
      font-size: 24px;
    }

    .time-num {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 65px;
      height: 65px;
      margin: 0 14px;
      font-size: 36px;
      background-color: #fff;
      border-radius: 9px;
    }

    .time-unit {
      font-size: 24px;
    }
  }

  .stair-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 750px;
    height: 1135px;
    margin-top: 33px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    overflow-x: hidden;

    .stair-title {
      margin: 0;
      padding: 43px 0 41px;
      font-size: 48px;
    }

    .stair-conditions {
      display: flex;
      justify-content: space-between;
      width: 100%;
      padding: 0 16px;

      .cond-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-evenly;
        width: 227px;
        height: 112px;
        border-radius: 14px;
      }

      .cond-value {
        font-size: 28px;
      }

      .cond-label {
        font-size: 24px;
      }
    }
  }

  .prize-list {
    display: flex;
    margin-top: 46px;
  }

  .prize-item {
    position: relative;
    width: 414px;
    height: 501px;

    &:first-child,
    &:last-child {
      transform: scale(0.86);
    }

    .prize-image {
      width: 100%;
      height: 100%;
    }

    .prize-info {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      height: 150px;
      padding: 0 30px;
    }

    .prize-title {
      padding: 25px 0 40px;
      font-size: 28px;
    }

    .prize-price-remain {
      display: flex;
      justify-content: space-between;
    }

    .prize-price {
      display: flex;
      align-items: baseline;
      font-weight: bold;

      span {
        &:first-child {
          font-size: 24px;
        }

        &:last-child {
          font-size: 30px;
        }
      }
    }

    .prize-remain {
      font-size: 24px;
    }
  }

  .progress-container {
    display: flex;
    justify-content: space-between;
    width: 723px;
    height: 36px;
    margin: 30px 0 25px;

    .progress-item {
      display: flex;
      align-items: center;
      width: 17%;
      height: 100%;

      .progress-line {
        flex: 1;
        height: 4px;
        border-radius: 2px;
      }

      .progress-icon {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 50%;

        img {
          width: 28px;
          height: 28px;
        }
      }

      .progress-label {
        position: absolute;
        bottom: -25px;
        left: 50%;
        width: max-content;
        font-size: 20px;
        white-space: nowrap;
        word-break: break-all;
        transform: translateX(-50%);
      }

      &:first-child {
        width: 23%;

        .progress-line {
          &:first-child {
            flex: 3;
          }

          &:last-child {
            flex: 2;
          }
        }
      }

      &:last-child {
        width: 23%;

        .progress-line {
          &:first-child {
            flex: 2;
          }

          &:last-child {
            flex: 3;
          }
        }
      }
    }
  }

  .progress-summary {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 503px;
    height: 47px;
    margin: 46px 0 60px;
    font-size: 26px;
    border-radius: 23px;

    .value {
      color: #ef1010;
    }
  }

  .appointment-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 504px;
    height: 96px;
    font-size: 36px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    border-radius: 11px;
  }

  .recommend-sku-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding: 0 0 50px;

    h2 {
      padding: 60px 0 50px;
      font-size: 48px;
    }

    .recommend-sku-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-evenly;
      width: 100%;
    }

    .recommend-sku-item {
      position: relative;
      width: 351px;
      height: 482px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: 100% 100%;
      border-radius: 23px;
    }

    .recommend-sku-info {
      position: absolute;
      bottom: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 100%;
      height: 150px;
      padding: 0 22px;
    }

    .recommend-sku-name {
      margin-bottom: 15px;
      color: #000;
      font-size: 26px;
      line-height: 1.5;
    }

    .recommend-sku-price-button {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .recommend-sku-price {
      display: flex;
      align-items: baseline;
      color: #ef201c;
    }

    .price-symbol {
      font-size: 24px;
    }

    .price-value {
      font-size: 30px;
    }

    .recommend-sku-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 152px;
      height: 46px;
      color: #fff;
      font-size: 26px;
      background-color: #ef1f1c;
      border-radius: 10px;
    }
  }

  .popup-container {
    position: absolute;
    background-color: rgba(0, 0, 0, 60%);
    inset: 0;

    .popup {
      position: absolute;
      top: 350px;
      left: 50%;
      width: 543px;
      height: 669px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: 100% 100%;
      border-radius: 24px;
      transform: translateX(-50%);
    }

    .popup-inner-image {
      position: absolute;
      z-index: 1;
      width: 100%;
      inset: 0;
    }

    .popup-button {
      position: absolute;
      bottom: 90px;
      left: 50%;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 352px;
      height: 68px;
      border-radius: 11px;
      transform: translateX(-50%);
    }

    .popup-button-text {
      font-size: 30px;
    }
  }
}
