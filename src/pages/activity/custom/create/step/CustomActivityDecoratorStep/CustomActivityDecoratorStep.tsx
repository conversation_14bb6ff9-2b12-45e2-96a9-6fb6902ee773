import { Box, Button, Tab, Message, Loading } from '@alifd/next';
import React, { useState } from 'react';
import { history } from 'ice';

import Container from '@/components/Container/';
import FixedBottomBox from '@/components/FixedBottomBox/FixedBottomBox';
import PhonePreview from '@/components/PhonePreview/PhonePreview';

import ActivityDecoratorContainer
  from '@/pages/activity/custom/create/components/ActivityDecoratorContainer/ActivityDecoratorContainer';

import CustomActivityMiniAppPreview from './CustomActivityStyleDecoration/CustomActivityMiniAppPreview/CustomActivityMiniAppPreview';

import CustomActivityStyleDecoration from './CustomActivityStyleDecoration/CustomActivityStyleDecoration';
import ActivityRecommendSkuSetting
  from '@/pages/activity/custom/create/components/ActivityRecommendSkuSetting/ActivityRecommendSkuSetting';
import CustomActivityLivecardSetting from './CustomActivityLivecardSetting/CustomActivityLivecardSetting';
import ActivityShareSetting from '@/pages/activity/custom/create/components/ActivityShareSetting/ActivityShareSetting';
import { showErrorMessageDialog } from '@/utils';
import { activityCustomizeActivitySaveOrUpdate } from '@/api/b';

export default function CustomActivityDecoratorStep(props) {
  const { goNextStep, goPrevStep, field } = props;
  const AF_Act_Is_View = field.getValue('AF_Act_Is_View');

  // 表示“样式装修”里是不是弹窗页
  const [styleIsPopup, setStyleIsPopup] = useState(false);

  const [submitting, setSubmitting] = useState(false); // 提交锁

  const tabsData = [
    {
      key: 'pageStyle',
      title: '页面样式装修',
      component: (
        <CustomActivityStyleDecoration
          field={field}
          setStyleIsPopup={setStyleIsPopup}
        />
      ),
    },
    {
      key: 'recommend',
      title: '推荐商品设置',
      component: <ActivityRecommendSkuSetting field={field} />,
    },
    {
      key: 'livecard',
      title: '通用列表入口设置',
      component: <CustomActivityLivecardSetting field={field} />,
    },
    {
      key: 'share',
      title: '分享设置',
      component: <ActivityShareSetting field={field} />,
    },
  ];
  // 当前激活的 tab key
  const [activeKey, setActiveKey] = useState(tabsData[0].key);

  async function clickSubmit() {
    if (submitting) return;
    setSubmitting(true);

    const { errors } = await field.validatePromise();
    if (errors) {
      showErrorMessageDialog(errors);
      setSubmitting(false);
      return;
    }

    // 1. 先拿到原始值
    const raw = field.getValues();

    // 2. 构造一个干净的 payload，剔除所有 AF_ 开头的键
    const payload: any = Object.entries(raw).reduce((acc, [key, val]) => {
      if (!key.startsWith('AF_')) {
        acc[key] = val;
      }
      return acc;
    }, {});

    // 3. cardJson 序列化
    if (payload.cardJson != null && typeof payload.cardJson !== 'string') {
      payload.cardJson = JSON.stringify(payload.cardJson);
    }

    // 4. atmosphereJson 在 baseInfo 里也要序列化
    if (
      payload.baseInfo &&
      payload.baseInfo.atmosphereJson != null &&
      typeof payload.baseInfo.atmosphereJson !== 'string'
    ) {
      payload.baseInfo = {
        ...payload.baseInfo,
        atmosphereJson: JSON.stringify(payload.baseInfo.atmosphereJson),
      };
    }

    console.log('最终要提交的 payload:', payload);

    // 5. 调 API
    try {
      const res = await activityCustomizeActivitySaveOrUpdate(payload);
      Message.success('保存成功');
      field.setValue('baseInfo.activityId', res);
      goNextStep();
    } catch (err) {
      // Message.error('网络异常，保存失败');
      Message.error(err.message);
      console.error(err);
    } finally {
      setSubmitting(false);
    }
  }

  function clickReturn() {
    goPrevStep();
  }

  function returnActivityList() {
    history?.push('/activity/list');
  }

  const leftContent = (
    <Loading visible={submitting} style={{ width: '100%', height: '100%' }}>
      <Container>
        <Tab
          activeKey={activeKey}
          lazyLoad={false}
          onChange={setActiveKey}
        >
          {tabsData.map(item => (
            <Tab.Item
              key={item.key}
              title={item.title}
            >
              {item.component}
            </Tab.Item>
          ))}
        </Tab>
      </Container>
      <FixedBottomBox>
        <Button
          type="normal"
          size="large"
          onClick={clickReturn}
        >
          上一步
        </Button>
        {AF_Act_Is_View ? (
          <Button
            type="primary"
            size="large"
            onClick={returnActivityList}
          >
            返回活动列表
          </Button>
        ) : (
          <Button
            type="primary"
            size="large"
            onClick={clickSubmit}
          >
            下一步：完成创建并投放
          </Button>
        )}
      </FixedBottomBox>
    </Loading>
  );
  const rightContent = (
    <Box>
      <PhonePreview
        width={750}
        scale={0.4}
      >
        <CustomActivityMiniAppPreview
          field={field}
          showDialog={styleIsPopup}
        />
      </PhonePreview>
    </Box>
  );

  return (
    <ActivityDecoratorContainer
      left={leftContent}
      right={rightContent}
    />
  );
}
