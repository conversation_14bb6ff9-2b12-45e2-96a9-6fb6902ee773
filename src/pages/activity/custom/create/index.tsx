import { useEffect, useState } from 'react';
import { Box, Field, Step, Form, Message, Loading } from '@alifd/next';
import dayjs from 'dayjs';
import useStepNavigation from '@/hooks/useStepNavigation';
import { StatusMapEnum, ActivityTypeMapEnum } from '@/utils';
import { useLocation } from 'ice';
import { activityGetCrowdConfigList, activityCustomizeActivityGetActivityInfo } from '@/api/b';


import CustomActivityPlaySettingStep from '@/pages/activity/custom/create/step/CustomActivityPlaySettingStep/CustomActivityPlaySettingStep';
import ActivityRuleStep from '@/pages/activity/custom/create/components/ActivityRuleStep/ActivityRuleStep';
import CustomActivityDecoratorStep
  from '@/pages/activity/custom/create/step/CustomActivityDecoratorStep/CustomActivityDecoratorStep';
import ActivityPromotionStep from '@/pages/activity/custom/create/components/ActivityPromotionStep/ActivityPromotionStep';

// 自定义活动-表单初始数据
export let customActivityInitData = {
  // 基本信息
  baseInfo: {
    activityType: 1,
    activityName: `自定义活动${dayjs().format('YYYY-MM-DD')}`,
    startTime: '',
    endTime: '',
    joinType: 1,
    rule: '',
    atmosphereJson: {
      toShoppingBtnType: 1,
    },
    shareTitle: '自定义活动',
    shareContent: '自定义活动',
    shareImage: '',
  },
  // 活动门槛
  crowdType: 1,
  crowdName: '全部用户',
  crowdInfo: [],
  cdpCrowdId: '', // 人群id
  // 玩法设置
  playInfo: {
    orderAfterSign: true, // 仅统计报名后订单
    orderTimeType: 0, // 下单时间  0:活动时间 1:指定时间
    orderTimeStart: '', //  下单开始时间
    orderTimeEnd: '', // 下单结束时间
    skuType: 1, // sku类型 1:全店商品 2:排除部分商品 3:指定商品
    productList: [],
    orderType: '1', // 订单类型 1:现货订单 2:预售订单
    orderStatus: 0, // 0:支付成功  1:确认收货N天后
  },
  // 领奖规则
  giftRule: {
    autoSend: false, // 领奖方式 true:自动领奖 false:手动领奖
    subtractionType: 1, // 奖品库存扣减方式  1:发放时扣减 2:预约时扣减
    reSubtractionType: 1, // 奖品库存回撤方式 1:回撤至剩余库存
  },
  // 奖品阶梯设置
  activityStairs: [],
  // 推荐商品
  activityProduct: [],
  // livecard装修
  cardJson: {},
  // livecard尺寸
  cardSize: 1,
};

// 每一步对应的标题和组件
const steps = [
  { title: '基础玩法设置', component: CustomActivityPlaySettingStep },
  { title: '规则设置', component: ActivityRuleStep },
  { title: '氛围定制', component: CustomActivityDecoratorStep },
  { title: '完成创建并投放', component: ActivityPromotionStep },
];

export default function CustomActivityCreatePage() {
  const location = useLocation();
  const { activityId, activityStatus, operationType } = location.state || {};
  // 一些标记活动状态的字段
  const ActivityStatusFieldValues: any = {
    AF_Act_Not_Start: activityStatus == StatusMapEnum.NOT_START.value,
    AF_Act_In_Progress: activityStatus == StatusMapEnum.IN_PROGRESS.value,
    AF_Act_End: activityStatus == StatusMapEnum.END.value,
    AF_Act_Is_Create: !operationType,
    AF_Act_Is_Edit: operationType == 'edit',
    AF_Act_Is_View: operationType == 'view',
    AF_Act_Is_Copy: operationType == 'copy',
  };

  // 是否禁用表单（创建：不禁用 or 未开始：不禁用 or 复制：不禁用）
  // 如果不满足 创建、未开始、复制 三种情况 → 就禁用
  ActivityStatusFieldValues.AF_Act_Form_Disabled = !(
    ActivityStatusFieldValues.AF_Act_Is_Create ||
    ActivityStatusFieldValues.AF_Act_Not_Start ||
    ActivityStatusFieldValues.AF_Act_Is_Copy
  );

  // 管理当前第几步、前进后退
  const { activeStep, goNextStep, goPrevStep } = useStepNavigation(0, steps.length);
  const [loading, setLoading] = useState(true);

  // 创建一个统一的 field 对象，用于多步骤表单数据管理
  const customActivityField = Field.useField({ parseName: true });

  // —— 初始化：新增 vs 编辑 互斥 —— //
  useEffect(() => {
    console.log('📝 活动详情 - 参数');
    if (!operationType) {
      initNewData().then();
    } else if (ActivityStatusFieldValues.AF_Act_Is_Edit || ActivityStatusFieldValues.AF_Act_Is_View) {
      loadActivityDetail().then();
    } else if (ActivityStatusFieldValues.AF_Act_Is_Copy) {
      ActivityStatusFieldValues.AF_Act_Is_Create = true; // copy 模式，设置为创建模式
      loadActivityDetail({
        forCopy: true, // copy 模式，传 true
      }).then();
    }
  }, []);

  /**
   * 初始化“新增”表单：包括拉取人群配置、合并默认数据、设置 field
   */
  async function initNewData(copyData: any = {}) {
    setLoading(true);
    try {
      const {
        atmosphereJson,
        cardJson,
        templateId,
      } = location.state || {};
      let crowdInfo: any = [];
      const res = await activityGetCrowdConfigList({
        activityType: ActivityTypeMapEnum.CUSTOMIZE_ACTIVITY.value,
      });
      crowdInfo = res;
      const initData = {
        ...customActivityInitData,
        crowdInfo,
        templateId: copyData.templateId || templateId,
        baseInfo: {
          ...customActivityInitData.baseInfo,
          atmosphereJson: copyData.atmosphereJson || atmosphereJson || {},
        },
        cardJson: copyData.cardJson || cardJson || {},
        ...ActivityStatusFieldValues,
      };
      customActivityField.setValues(initData);
    } catch (e) {
      Message.error(e.message);
    } finally {
      setLoading(false);
    }
  }

  /**
   * 编辑模式：拉取后端详情并将所有字段回显到表单
   */
  async function loadActivityDetail({ forCopy = false } = {}) {
    setLoading(true);
    try {
      const data: any = await activityCustomizeActivityGetActivityInfo({ activityId });

      // 装修信息格式化
      const atmosphereJson = data.baseInfo.atmosphereJson ? JSON.parse(data.baseInfo.atmosphereJson) : {};
      const cardJson = data.cardJson ? JSON.parse(data.cardJson) : {};

      const formValues = {
        ...data,
        baseInfo: {
          ...data.baseInfo,
          atmosphereJson,
        },
        cardJson,
        ...ActivityStatusFieldValues,
      };

      if (forCopy) {
        delete formValues.baseInfo.activityId;
        delete formValues.baseInfo.id;
        delete formValues.actUrl;
        delete formValues.giftRule.activityId;
        delete formValues.giftRule.id;
        delete formValues.playInfo.activityId;
        delete formValues.playInfo.id;
        delete formValues.qrCodeUrl;
        formValues.activityProduct.forEach(item => {
          delete item.id;
        });
        formValues.playInfo.productList.forEach(item => {
          delete item.id;
        });

        formValues.activityStairs = [];
        formValues.baseInfo.rule = '';
      }

      customActivityField.setValues(formValues);
    } catch (e) {
      Message.error(e.message);
    } finally {
      setLoading(false);
    }
  }

  const renderStepContent = stepIndex => {
    const stepItem = steps[stepIndex];
    if (!stepItem || !stepItem.component) return null;
    const StepComponent = stepItem.component;
    return (
      <StepComponent
        goNextStep={goNextStep}
        goPrevStep={goPrevStep}
        field={customActivityField}
      />
    );
  };

  const handleFormChange = () => {
    const fieldValues = customActivityField.getValues();
    console.log('自定义活动 - 表单数据 - fieldValues', fieldValues);
  };
  return (

    loading ? <Loading visible={loading} style={{ width: '100%', height: 800 }} /> : (
      <div>
        <Box
          direction="row"
          justify="center"
          padding={[0, 0, 16]}
        >
          <Step
            className="step"
            current={activeStep}
            labelPlacement="hoz"
            shape="circle"
            readOnly
          >
            {steps.map((item, idx) => (
              <Step.Item
                key={idx}
                title={item.title}
              />
            ))}
          </Step>
        </Box>

        {/* 整体表单容器 */}
        <Form
          field={customActivityField}
          onChange={handleFormChange}
          className="activity-setting-form"
        >
          {renderStepContent(activeStep)}
        </Form>
      </div>
    )
  );
}
