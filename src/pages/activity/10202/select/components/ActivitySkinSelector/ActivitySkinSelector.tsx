import { useState, useEffect } from 'react';
import TemplateItem from './TemplateItem/TemplateItem';
import { Loading, Message } from '@alifd/next';
import { templateGetActivityTemplate } from '@/api/common';

export default function ActivitySkinSelector(props) {
  const { activityType } = props;
  const [loading, setLoading] = useState(false);

  const [templateList, setTemplateList] = useState<any>([]);

  useEffect(() => {
    fetchTemplateList().then();
  }, []);

  async function fetchTemplateList() {
    setLoading(true);
    try {
      const res = await templateGetActivityTemplate({
        actType: activityType,
      });
      setTemplateList(res);
    } catch (e) {
      Message.error(e.message);
    } finally {
      setLoading(false);
    }
  }

  return (
    <Loading visible={loading} style={{ width: '100%', height: '100%' }} className="skin-selector">
      {templateList?.map((template, index) => (
        <TemplateItem
          key={index}
          template={template}
        />
      ))}
    </Loading>
  );
}
