import PhonePreview from '@/components/PhonePreview/PhonePreview';
import { Button } from '@alifd/next';
import SkinList from '../SkinList/SkinList';
import { history } from 'ice';

import '../ActivitySkinSelector.scss';

export default function TemplateItem({ template }) {
  const onSelectSkin = async () => {
    history?.push('/activity/10202/create/', { operationType: 'add', configValue: template.configValue });
  };

  return (
    <div className="template-item">
      <PhonePreview width={180}>
        <img
          className="selected-image"
          alt={''}
          src={template.cover}
        />
      </PhonePreview>
      <div className="template-content">
        <span className="template-content-title">可选皮肤：</span>
        <SkinList
          template={template}
        />
        <div className="template-tags">
          <span className="status-red">{'全店促销'}</span>
          <span className="status-normal">{'日常促复购'}</span>
        </div>
        <div className="select-button">
          <Button
            type="primary"
            onClick={() => onSelectSkin()}
          >
            立即创建
          </Button>
        </div>
      </div>
    </div>
  );
}
