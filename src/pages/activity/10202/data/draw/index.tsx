import Container from '@/components/Container';
import usePagination from '@/hooks/usePagination';
import { downloadExcel, maskSensitiveInfo } from '@/utils';
import { Box, Button, DatePicker2, Input, Message, Pagination, Select, Table } from '@alifd/next';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { dataGetDrawChanceRecord, dataGetDrawChanceRecordExport } from '@/api/v10002';
import { Auth } from '@/components/Auth';

const pageNum = 1;
const pageSize = 10;

export default function DrawData({ activityId }) {
  const defaultSearchParams = {
    activityId,
    openId: '',
    timeRange: [],
    nick: '',
    taskType: -2, // 任务类型 -2全部 其他参照任务组件
    startTime: '',
    endTime: '',
  };
  // 搜索相关
  const [searchParams, setSearchParams] = useState({
    ...defaultSearchParams,
    activityId,
  });

  // 表格相关
const [tableData, setTableData] = useState<any>([]);
const [loading, setLoading] = useState(false);

// 通用的参数更新函数
const updateSearchParam = (key: string, value: any) => {
  setSearchParams(prev => ({ ...prev, [key]: value }));
};


const pagination = usePagination(pageNum, pageSize, async (current, size, params) => {
  await fetchData({
    pageNum: current,
    pageSize: size,
    ...params,
  });
});

const fetchData = async (params?: any) => {
  setLoading(true);
  try {
    const [start, end] = searchParams.timeRange || [];

    // 构建查询参数
    const queryParams = {
      ...searchParams,
      startTime: start ? dayjs(start).format('YYYY-MM-DD 00:00:00') : '',
      endTime: end ? dayjs(end).format('YYYY-MM-DD 23:59:59') : '',
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    };

    const { records, total }: any = await dataGetDrawChanceRecord({
      ...queryParams,
      ...params,
    });

    setTableData(records || []);
    pagination.setTotal(total || 0);
  } catch (e) {
    Message.error(e.message);
  } finally {
    setLoading(false);
  }
};

const handleExport = async () => {
  setLoading(true);
  try {
    const [start, end] = searchParams.timeRange || [];
    const data: any = await dataGetDrawChanceRecordExport({
      ...searchParams,
      startTime: start ? dayjs(start).format('YYYY-MM-DD 00:00:00') : '',
      endTime: end ? dayjs(end).format('YYYY-MM-DD 23:59:59') : '',
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    });
    downloadExcel(data, `抽奖机会获取明细${dayjs().format('YYYY-MM-DD HH:mm')}`);
  } catch (e) {
    Message.error(e.message);
  } finally {
    setLoading(false);
  }
};

const handleSearch = () => {
  pagination.changePage(pageNum);
};

const handleReset = () => {
  setSearchParams(defaultSearchParams);
  pagination.reset(defaultSearchParams);
};

useEffect(() => {
  fetchData().then();
}, []);

  return (
    <Container style={{ marginTop: 16 }}>
      <Box direction="row" spacing={16} margin={[0, 0, 16, 0]} wrap align="center">
        <Input label={'用户昵称'} value={searchParams.nick} onChange={(value) => updateSearchParam('nick', value)} />
        <Input label={'openId'} value={searchParams.openId} onChange={(value) => updateSearchParam('openId', value)} />
        <DatePicker2.RangePicker
          type={'range'}
          label={'发放时间'}
          value={searchParams.timeRange}
          onChange={(value) => updateSearchParam('timeRange', value)}
        />
        <Select label={'任务类型'} value={searchParams.taskType} onChange={(value) => updateSearchParam('taskType', value)}>
          <Select.Option value={-2}>全部</Select.Option>
          <Select.Option value={-1}>首次访问活动赠送</Select.Option>
          <Select.Option value={0}>每天赠送</Select.Option>
          <Select.Option value={1}>商品下单</Select.Option>
          <Select.Option value={2}>积分兑换抽奖次数</Select.Option>
          <Select.Option value={3}>浏览指定页面</Select.Option>
          <Select.Option value={4}>浏览指定商品</Select.Option>
          <Select.Option value={5}>关注店铺</Select.Option>
          <Select.Option value={6}>分享活动</Select.Option>
          <Select.Option value={7}>活动签到</Select.Option>
          <Select.Option value={8}>首购送机会</Select.Option>
          <Select.Option value={9}>加入会员</Select.Option>
        </Select>
        <Button type="primary" onClick={handleSearch}>查询</Button>
        <Button onClick={handleReset}>重置</Button>
        <Auth authKey={'activity_list_data_export'}>
          <Button
            type="secondary"
            onClick={handleExport}
          >
            导出
          </Button>
        </Auth>
      </Box>
      <Table
        dataSource={tableData}
        loading={loading}
      >
        <Table.Column
          title="用户昵称"
          dataIndex="nickName"
          cell={(value) => {
          return <div >{maskSensitiveInfo(value, 1, 1)}</div>;
        }}
        />
        <Table.Column
          title="openId"
          dataIndex="openId"
          cell={(value) => {
          return <div >{maskSensitiveInfo(value, 1, 1, 6)}</div>;
        }}
        />
        <Table.Column title="发放时间" dataIndex="createTime" cell={(value) => (value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-')} />
        <Table.Column title="发放抽奖次数" dataIndex="chanceNum" />
        <Table.Column title="完成任务类型" dataIndex="taskTypeStr" />
        <Table.Column
          title="任务明细"
          dataIndex="details"
          cell={(value) => {
          return <div>{value}</div>;
        }}
        />
        <Table.Column
          width={200}
          title="备注"
          dataIndex="notes"
          cell={(value, index, record) => {
          return <div >{record.taskType === 1 ? `关联订单号：${value}` : value}</div>;
        }}
        />
      </Table>
      <Box direction="row" justify="flex-end" margin={[16, 0, 0, 0]}>
        <Pagination
          {...pagination.paginationProps}
          shape={'arrow-only'}
          totalRender={(total) => `共 ${total} 条`}
          pageSizeSelector="dropdown"
          pageSizePosition="end"
        />
      </Box>
    </Container>
  );
}
