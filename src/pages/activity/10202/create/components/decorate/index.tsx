import { Box, Button, Message } from '@alifd/next';
import { useEffect, useReducer, useState } from 'react';
import Container from '@/components/Container';
import { useActivity } from '../../reducer';
import { showErrorMessageDialog } from '@/utils';
import _ from 'lodash';
import { createActivity, updateActivity } from '@/api/v10202';
import { history } from '@ice/runtime';

export default function Decorate({ goPrevStep }) {
  const { state, dispatch } = useActivity();
  const { operationType } = state.extra;
  const isEdit = operationType === 'edit';
  const [loading, setLoading] = useState(false);

  // 这里 10202 没有复杂装修面板，直接在此提交
  const handleBack = () => {
    goPrevStep();
  };

  const handleSubmit = async () => {
    setLoading(true);
    try {
      const apiFn = [createActivity, updateActivity];
      const { activityUrl } = await apiFn[+isEdit]({
        activityData: state as any,
        decoData: state.decorate || '{}',
      });
      dispatch({ type: 'UPDATE_EXTRA', payload: { activityUrl } });
      history?.push('/activity/list');
    } catch (error: any) {
      Message.error(error?.message || '提交失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Box direction="row">
        <Box flex={1} style={{ position: 'relative' }}>
          <Container style={{ marginBottom: 0 }}>
            <div style={{ color: 'var(--color-text-secondary)' }}>本活动无需装修，点击下一步完成创建。</div>
          </Container>
        </Box>
      </Box>
      <Box
        direction="row"
        justify="center"
        spacing={16}
        style={{ position: 'fixed', bottom: 12, left: '50%', transform: 'translateX(calc(-50% + 110px))' }}
      >
        <Button onClick={handleBack}>上一步</Button>
        <Button loading={loading} type="primary" onClick={handleSubmit}>
          返回活动列表
        </Button>
      </Box>
    </div>
  );
}

