import Container from '@/components/Container';
import { QRCodeSVG } from 'qrcode.react';
import { Box, Button } from '@alifd/next';
import { history } from '@ice/runtime';
import { useActivity } from '../../reducer';

export default function Complete() {
  const { state } = useActivity();
  const { activityUrl } = state.extra;

  return (
    <div>
      <Container>
        <div style={{ display: 'flex', alignItems: 'center', gap: 24 }}>
          <QRCodeSVG value={activityUrl || ''} />
          <div>
            <p style={{ fontWeight: 600, marginBottom: 8 }}>活动保存成功</p>
            <p style={{ color: 'var(--color-text-danger)' }}>重要提示：活动需选择推广渠道并投放，才能对外可见</p>
            <p style={{ color: 'var(--color-text-secondary)' }}>请使用抖音APP扫一扫预览</p>
          </div>
        </div>
      </Container>

      <Box direction="row" justify="center" style={{ position: 'fixed', bottom: 22, left: '50%', transform: 'translateX(calc(-50% + 110px))' }}>
        <Button type="primary" style={{ width: 150 }} onClick={() => history?.push('/activity/list')}>
          暂不投放
        </Button>
      </Box>
    </div>
  );
}

