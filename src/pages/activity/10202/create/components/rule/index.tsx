import Container from '@/components/Container';
import { Form, Button, Box, Input, Field } from '@alifd/next';
import { useActivity } from '../../reducer';
import dayjs from 'dayjs';
import { ModuleType } from '../../type';

export default function Rule({ goNextStep, goPrevStep }) {
  const { state, dispatch } = useActivity();
  const field = Field.useField();

  const { base, threshold, order, demoSkuList, repurchasePrizeList } = state as any;
  const { operationType } = state.extra;
  const needDisable = operationType === 'view';

  const updateRule = (data: string) => {
    dispatch({ type: 'UPDATE_RULE', payload: data });
  };

  const handleSubmit = () => {
    field.validatePromise().then(({ errors }) => {
      if (errors) return;
      goNextStep();
    });
  };

  const handleBack = () => {
    goPrevStep();
  };

  const getThresholdText = () => {
    // 这里只展示“店铺会员”，如后续扩展人群包等，再补充
    return '店铺会员';
  };

  const getSkuPrizeText = () => {
    const skuLines: string[] = [];
    (demoSkuList || []).forEach((sku, idx) => {
      const prizes = (sku.prizeList || []).map((p, i) => `（${i + 1}）${p.prizeName}：数量：${p.sendTotalCount || 1}份`);
      skuLines.push(`第${idx + 1}段（${sku.goodsLine || ''}/${sku.skuName || ''}）：\n${prizes.join('\n')}`);
    });
    const repurchaseText = (repurchasePrizeList || []).map((p, i) => `复购奖励（${i + 1}）：${p.prizeName}，数量：${p.sendTotalCount || 1}份`).join('\n');
    return `${skuLines.join('\n')}\n${repurchaseText}`;
  };

  const generateRule = () => {
    const autoText = `1、活动时间：\n${dayjs(base.startTime).format('YYYY-MM-DD HH:mm:ss')}至${dayjs(base.endTime).format('YYYY-MM-DD HH:mm:ss')}；\n2、活动对象：\n${getThresholdText()}\n3、奖励说明：\n${getSkuPrizeText()}\n4、如发现刷单作弊等行为，将取消奖励资格；`;
    updateRule(autoText);
    field.setError('rule', '');
  };

  return (
    <Form className="activity-rule-form" field={field}>
      <Container title={'活动规则'}>
        <Box direction="row" justify="flex-end" align="center" spacing={10} margin={[0, 0, 10, 0]}>
          <span style={{ color: 'var(--color-text-secondary)' }}>
            提交活动前请核对活动规则，可根据实际情况，对内容进行适当编辑
          </span>
          <Button type="secondary" onClick={generateRule} disabled={needDisable}>
            生成规则
          </Button>
        </Box>

        <Form.Item name="rule" label="" required requiredMessage={'请生成活动规则'}>
          <Input.TextArea
            maxLength={2000}
            disabled={needDisable}
            rows={24}
            cutString
            showLimitHint
            composition
            value={state.rule}
            onChange={(val) => updateRule(val)}
            placeholder="请输入活动规则"
          />
        </Form.Item>
      </Container>

      <Box direction="row" justify="center" spacing={16} margin={[-30, 0, 0, 0]}>
        <Button onClick={handleBack}>上一步</Button>
        <Button type="primary" style={{ width: 150 }} onClick={handleSubmit}>
          下一步：氛围定制
        </Button>
      </Box>
    </Form>
  );
}


