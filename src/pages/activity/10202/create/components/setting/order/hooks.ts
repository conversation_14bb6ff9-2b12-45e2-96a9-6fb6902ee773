import { useActivity } from '@/pages/activity/10202/create/reducer';
import { validateOrder } from './validator';
import { ModuleType, OrderState } from '@/pages/activity/10202/create/type';
import { Field } from '@alifd/next';
export function useOrder() {
  const { state, dispatch } = useActivity();
  const field = Field.useField({ values: state.order });

  const updateOrder = (data: Partial<OrderState>) => {
    dispatch({
      type: 'UPDATE_ORDER',
      payload: data,
    });

    // 清除错误
    dispatch({
      type: 'CLEAR_ERRORS',
      module: ModuleType.ORDER,
    });
  };

  const validateOrderData = () => {
    const errors = validateOrder(state);
    dispatch({
      type: 'VALIDATE_MODULE',
      module: ModuleType.ORDER,
      payload: errors,
    });
    return errors.length === 0;
  };

  return {
    order: state.order,
    updateOrder,
    validateOrder: validateOrderData,
    field,
    errors: state.errors[ModuleType.ORDER],
  };
}