import Container from '@/components/Container';
import { Button, DatePicker2, Form, Input, NumberPicker, Message } from '@alifd/next';
import { useActivity } from '../../../reducer';
import { ModuleType } from '@/pages/activity/10202/create/type';
import dayjs from 'dayjs';

const FormItem = Form.Item;

export default function RepurchasePrizeList() {
  const { state, dispatch } = useActivity();
  const prize = (state.repurchasePrizeList || [])[0] || { prizeName: '', sendTotalCount: 1, startDate: '', endDate: '' };

  const updatePrize = (data: any) => {
    const list = [{ ...(prize as any), ...data }];
    dispatch({ type: 'UPDATE_REPURCHASE_PRIZE_LIST', payload: list });
    dispatch({ type: 'CLEAR_ERRORS', module: ModuleType.REPURCHASE_PRIZE_LIST });
  };

  return (
    <Container title="复购奖品设置">
      <Form labelAlign="left" style={{ width: 620 }}>
        <FormItem label="奖品名称" required>
          <Input value={prize.prizeName} onChange={(v) => updatePrize({ prizeName: v })} />
        </FormItem>
        <FormItem label="发放份数" required>
          <NumberPicker min={1} value={Number(prize.sendTotalCount) || 1} onChange={(v) => updatePrize({ sendTotalCount: Number(v) })} />
        </FormItem>
        <FormItem label="生效起止">
          <DatePicker2.RangePicker
            value={prize.startDate && prize.endDate ? [dayjs(prize.startDate).toDate(), dayjs(prize.endDate).toDate()] : []}
            showTime
            onChange={(range: any) => {
              if (range && range.length === 2) {
                updatePrize({ startDate: dayjs(range[0]).format('YYYY-MM-DD HH:mm:ss'), endDate: dayjs(range[1]).format('YYYY-MM-DD HH:mm:ss') });
              }
            }}
          />
        </FormItem>
      </Form>
    </Container>
  );
}

