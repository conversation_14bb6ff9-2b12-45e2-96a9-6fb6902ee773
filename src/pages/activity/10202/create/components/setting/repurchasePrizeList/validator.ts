import { AppState } from '@/pages/activity/10202/create/type';
import dayjs from 'dayjs';

export const validateRepurchasePrizeList = (state: AppState): string[] => {
  const errors: string[] = [];
  const { repurchasePrizeList, base } = state as any;
  if (!repurchasePrizeList || repurchasePrizeList.length === 0) {
    errors.push('请设置复购奖品');
    return errors;
  }
  const prize = repurchasePrizeList[0];
  if (!prize.prizeName) {
    errors.push('请填写复购奖品名称');
  }
  if (!prize.sendTotalCount || prize.sendTotalCount <= 0) {
    errors.push('复购奖品发放份数需大于0');
  }
  if (prize.startDate && prize.endDate) {
    const ps = dayjs(prize.startDate);
    const pe = dayjs(prize.endDate);
    const as = dayjs(base.startTime);
    const ae = dayjs(base.endTime);
    if (ps.isAfter(as)) {
      errors.push('复购奖品开始时间必须小于等于活动开始时间');
    }
    if (pe.isBefore(ae)) {
      errors.push('复购奖品结束时间必须大于等于活动结束时间');
    }
  }
  return errors;
};

