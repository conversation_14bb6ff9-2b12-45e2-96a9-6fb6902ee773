import Container from '@/components/Container';
import ActivityName from './components/ActivityName';
import ActivityTime from './components/ActivityTime';
import { useActivity } from '@/pages/activity/10202/create/reducer';

/**
 * 基础信息模块
 * 包含活动名称、活动时间等基本配置
 */
export default function Base() {
  const { state } = useActivity();

  const { operationType, activityStatus } = state.extra;
  return (
    <Container title={'基础信息'}>
      <ActivityName operationType={operationType} />
      <ActivityTime operationType={operationType} activityStatus={activityStatus} />
    </Container>
  );
}
