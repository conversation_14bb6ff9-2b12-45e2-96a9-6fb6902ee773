import { useActivity } from '@/pages/activity/10202/create/reducer';
import { validateBaseInfo } from './validator';
import { BaseInfoState, ModuleType } from '@/pages/activity/10202/create/type';


/**
 * 基础信息模块的自定义Hook
 * 提供对基础信息状态的访问和更新方法
 */
export function useBaseInfo() {
  const { state, dispatch } = useActivity();

  // 更新基础信息并自动重新验证
  const updateBaseInfo = (data?: Partial<BaseInfoState>) => {
    if (!data) return;
    // 先更新数据
    dispatch({
      type: 'UPDATE_BASE',
      payload: data,
    });

    // 获取更新后的数据
    const updatedStateInfo = {
      ...state,
      base: {
        ...state.base,
        ...data,
      },
    };

    // 重新验证并更新错误状态
    const errors = validateBaseInfo(updatedStateInfo);
    dispatch({
      type: 'VALIDATE_MODULE',
      module: ModuleType.BASE,
      payload: errors,
    });
  };

  return {
    baseInfo: state.base,
    errors: state.errors[ModuleType.BASE] || [],
    updateBaseInfo,
    dispatch,
  };
}
