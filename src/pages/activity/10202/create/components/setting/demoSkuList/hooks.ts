import { useActivity } from '@/pages/activity/10202/create/reducer';
import { validateDemoSkuList } from './validator';
import { DemoSkuListState, DemoOriginalFileState, ModuleType } from '@/pages/activity/10202/create/type';


/**
 * 中听设置模块的自定义Hook
 * 提供对中听设置sku和奖品信息状态的访问和更新方法
 */
export function useDemoSkuList() {
    const { state, dispatch } = useActivity();

    // 更新基础信息并自动重新验证
    const updateDemoSkuList = (data?: DemoSkuListState[]) => {
        if (!data) return;
        // 先更新数据
        dispatch({
            type: 'UPDATE_DEMO_SKU_LIST',
            payload: data,
        });

        // 获取更新后的数据
        const updatedStateInfo = {
            ...state,
            demoSkuList: data,
        };

        // 重新验证并更新错误状态
        const errors = validateDemoSkuList(updatedStateInfo);
        dispatch({
            type: 'VALIDATE_MODULE',
            module: ModuleType.DEMO_SKU_LIST,
            payload: errors,
        });
    };

    return {
        demoSkuList: state.demoSkuList,
        errors: state.errors[ModuleType.DEMO_SKU_LIST] || [],
        updateDemoSkuList,
        dispatch,
    };
}

export function useDemoOriginalFile() {
    const { state, dispatch } = useActivity();

    const updateDemoOriginalFile = (data?: Partial<DemoOriginalFileState>) => {
        if (!data) return;
        // 先更新数据
        dispatch({
            type: 'UPDATE_DEMO_ORIGINAL_FILE',
            payload: data,
        });

        // 获取更新后的数据
        const updatedStateInfo = {
            ...state,
            demoOriginalFile: {
                ...state.demoOriginalFile,
                ...data,
            },
        };

        // 重新验证并更新错误状态
        const errors = validateDemoSkuList(updatedStateInfo);
        dispatch({
            type: 'VALIDATE_MODULE',
            module: ModuleType.DEMO_SKU_LIST,
            payload: errors,
        });
    };

    return {
        demoOriginalFile: state.demoOriginalFile,
        errors: state.errors[ModuleType.DEMO_SKU_LIST] || [],
        updateDemoOriginalFile,
        dispatch,
    };
}
