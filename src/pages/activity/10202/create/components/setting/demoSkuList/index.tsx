import Container from '@/components/Container';
import { Button, DatePicker2, Dialog, Form, Input, NumberPicker, Table, Message } from '@alifd/next';
import { useActivity } from '../../../reducer';
import { DemoSkuListState, ModuleType } from '../../../../create/type';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { getPrizeTypeLabel, PrizeTypeEnum } from '@/utils';
import ExcelImport from '../components/ExcelImport';
import { ACTIVITY_STATUS } from '@/utils/constant';
import { useDemoSkuList, useDemoOriginalFile } from '../demoSkuList/hooks';

const FormItem = Form.Item;

export default function DemoSkuList() {
  const { state, dispatch } = useActivity();
  const { demoSkuList, updateDemoSkuList } = useDemoSkuList();
  const { demoOriginalFile, updateDemoOriginalFile } = useDemoOriginalFile();

  const { operationType, activityStatus } = state.extra;
  const isCreate = operationType === 'add';
  const isCopy = operationType === 'copy';
  const isEdit = operationType === 'edit';
  const isView = operationType === 'view';
  const notStart = activityStatus !== ACTIVITY_STATUS.NOT_STARTED;
  const needDisable = (isEdit || isView) && notStart;

  const [visible, setVisible] = useState(false);
  const [editSkuIndex, setEditSkuIndex] = useState<number>(-1);
  const [editPrizeIndex, setEditPrizeIndex] = useState<number>(-1);
  const [editPrize, setEditPrize] = useState<any>({ prizeName: '', sendTotalCount: 1, startDate: '', endDate: '' });
  const [expandedTableData, setExpandedTableData] = useState<any>([]);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  // 当前编辑的奖品索引
  const [prizeTarget, setPrizeTarget] = useState(0);

  const updateList = (list: DemoSkuListState[]) => {
    dispatch({ type: 'UPDATE_DEMO_SKU_LIST', payload: list });
    dispatch({ type: 'CLEAR_ERRORS', module: ModuleType.DEMO_SKU_LIST });
  };

  // 下载小听模板
  const downloadDemoTemplate = async () => {
    try {
      // todo
      setTemporarySectionList([
        {
          sectionNum: 2,
          skuInfo: [
            {
              dayLimitCount: 100,
              goodsLine: '品线名1',
              prizeMainName: '9.9元奖品主名称1',
              sectionNum: 2,
              skuId: 12345678,
              skuMainPicture: '//img10.360buyimg.com/imgzone/jfs/t1/300396/28/22855/14477/6878b7a0F8898a760/d29b45a6329d149c.png',
              skuName: '商品名1',
            },
            {
              dayLimitCount: 100,
              goodsLine: '品线名7',
              prizeMainName: '0.1元奖品主名称2',
              sectionNum: 2,
              skuId: 12345677,
              skuMainPicture: '//img10.360buyimg.com/imgzone/jfs/t1/300396/28/22855/14477/6878b7a0F8898a760/d29b45a6329d149c.png',
              skuName: '商品名2',
            },
          ],
        },
        {
          sectionNum: 3,
          skuInfo: [
            {
              dayLimitCount: 100,
              goodsLine: '品线名2',
              prizeMainName: '5元奖品主名称3',
              sectionNum: 3,
              skuId: 12345679,
              skuMainPicture: '//img10.360buyimg.com/imgzone/jfs/t1/300396/28/22855/14477/6878b7a0F8898a760/d29b45a6329d149c.png',
              skuName: '商品名3',
            },
          ],
        },
      ]);
      // 移除这两行
      // console.log('setExpandedTableData');
      // setExpandedTableData(generateExpandedTableData());

      Message.success('模板下载功能待实现');
    } catch (error) {
      Message.error(error.message);
    }
  };

  // 设置demoSkuList
  const setTemporarySectionList = (data: any) => {
    const newDemoSkuList: any = [];
    data.forEach((sectionItem: any) => {
      sectionItem.skuInfo.forEach((skuItem: any) => {
        newDemoSkuList.push({
          goodsLine: skuItem.goodsLine,
          sectionSort: skuItem.sectionNum,
          skuMainPicture: skuItem.skuMainPicture,
          prizeMainName: skuItem.prizeMainName,
          skuName: skuItem.skuName,
          skuId: skuItem.skuId,
          dayLimitCount: skuItem.dayLimitCount,
          daylimit: skuItem.dayLimitCount,
          prizeList: [],
        });
      });
    });
    updateDemoSkuList(newDemoSkuList); // 使用 updateDemoSkuList 而不是 updateList
  };

  // 添加奖品到指定SKU
  const addPrizeToSku = (skuIndex: number) => {
    const newDemoSkuList = [...demoSkuList];
    if (!newDemoSkuList[skuIndex].prizeList) {
      newDemoSkuList[skuIndex].prizeList = [];
    }

    // 检查是否已达到最大奖品数量（3个）
    if (newDemoSkuList[skuIndex].prizeList.length >= 3) {
      return;
    }

    setTarget(skuIndex);
    setPrizeTarget(newDemoSkuList[skuIndex].prizeList.length);
    setEditValue(null);
    setVisible(true);
  };

  // 删除SKU行
  const deleteSku = (skuIndex: number) => {
    console.log('删除S', skuIndex, demoSkuList, typeof demoSkuList, state.demoSkuList);
    const newDemoSkuList = demoSkuList;
    console.log('删除SKU行', skuIndex, newDemoSkuList);
    newDemoSkuList.splice(skuIndex, 1);
    console.log('删除SKU行2', skuIndex, newDemoSkuList);
    updateDemoSkuList(newDemoSkuList);
    console.log('3', skuIndex, demoSkuList);
  };

  const openEditPrize = (skuIndex: number, prizeIndex: number) => {
    setEditSkuIndex(skuIndex);
    setEditPrizeIndex(prizeIndex);
    const prize = (demoSkuList[skuIndex].prizeList || [])[prizeIndex];
    setEditPrize({
      // prizeName: prize?.prizeName || '',
      // sendTotalCount: prize?.sendTotalCount || 1,
      // startDate: prize?.startDate || prize?.startTime || '',
      // endDate: prize?.endDate || prize?.endTime || '',
    });
    setVisible(true);
  };

  const savePrize = () => {
    const list = [...demoSkuList];
    console.log('保存奖品');
    setVisible(false);
  };

  const deletePrize = (skuIndex: number, prizeIndex: number) => {
    const list = [...demoSkuList];
    list[skuIndex].prizeList.splice(prizeIndex, 1);
    updateList(list);
  };

  // 生成展开的表格数据，每个SKU的每个奖品占一行
  const generateExpandedTableData = () => {
    // updateDemoSkuList([]);
    const expandedData: any = [];

    // 将对象转换为数组
    const skuArray = Object.values(demoSkuList || {});

    if (!Array.isArray(skuArray) || skuArray.length === 0) {
      return expandedData;
    }

    skuArray.forEach((sku, skuIndex) => {
      const prizeList = sku.prizeList || [];

      if (prizeList.length === 0) {
        // 如果没有奖品，显示一行空的奖品数据
        expandedData.push({
          ...sku,
          skuIndex,
          prizeIndex: -1,
          prize: null,
          isFirstPrizeRow: true,
          prizeRowCount: 1,
          sortId: skuIndex,
        });
      } else {
        // 为每个奖品创建一行
        prizeList.forEach((prize, prizeIndex) => {
          expandedData.push({
            ...sku,
            skuIndex,
            prizeIndex,
            prize,
            isFirstPrizeRow: prizeIndex === 0,
            prizeRowCount: prizeList.length,
            sortId: skuIndex,
          });
        });
      }
    });

    return expandedData;
  };

  // 监听 demoSkuList 变化，自动更新表格数据
  useEffect(() => {
    console.log('生成table', demoSkuList);
    setExpandedTableData(generateExpandedTableData());
  }, [demoSkuList]);

  return (
    <Container title="小听商品及奖品设置">
      <Form>
        <FormItem label="小听sku数据源上传">
          {(isCreate || isCopy || notStart) && (
          <>
            <Message type="notice" style={{ marginBottom: 10 }}>
              导入须知： <br />
              1.导入前请下载模板，将你要上传的数据放入模板中，使用模板进行导入。
              <br />
              2.单次导入最大5M，导入中请不要关闭此页面。
              <br />
              <Button text type="primary" onClick={downloadDemoTemplate} disabled={needDisable}>
                下载模板
              </Button>
            </Message>
            {/* todo: 改接口 */}
            <ExcelImport
              buttonText="上传小听sku数据"
              action={`${process.env.ICE_BASE_URL || ''}/10202/importSeriesExcel`}
              onSuccess={(res) => {
                const { response } = res;
                // code 是 200 就保留文件名称，其他情况不展示文件名
                if (response && response.code === 200) {
                  if (response.data) {
                    setTemporarySectionList(response.data);
                    Message.success('导入成功');
                    return true; // 返回 true 保留文件名
                  } else {
                    Message.error('导入数据为空，请检查文件内容');
                    return false; // 返回 false 不展示文件名
                  }
                } else {
                  // code 不是 200 (比如 500)，不展示文件名
                  const errorMessage = response?.message || '导入失败，请检查数据格式';
                  Message.error(errorMessage);
                  return false; // 返回 false 不展示文件名
                }
              }}
              onError={(error) => {
                console.error('上传失败：', error);
                const errorMessage = error?.response?.message || error?.message;
                Message.error(errorMessage);
              }}
            />
          </>
          )}
        </FormItem>
        <FormItem label="小听奖品设置">
          <div style={{ overflowX: 'auto' }}>
            {/* { */}
            {/*  expandedTableData.length > 0 && ( */}
            <Table
              dataSource={expandedTableData}
              style={{ marginTop: '15px' }}
              cellProps={(rowIndex, colIndex, dataIndex, record) => {
                  // 需要合并的列：段数、品线、sku名称、skuId、sku图、奖品名、每日限额、SKU操作
                  const mergeColumns = [
                    'sectionSort',
                    'goodsLine',
                    'skuName',
                    'skuId',
                    'skuImg',
                    'prizeMainName',
                    'daylimit',
                    'skuOperation',
                  ];

                  if (mergeColumns.includes(dataIndex)) {
                    // 如果是该SKU的第一行奖品，显示合并的单元格
                    if (record.isFirstPrizeRow) {
                      return {
                        rowSpan: record.prizeRowCount,
                      };
                    } else {
                      // 其他行隐藏
                      return {
                        rowSpan: 0,
                      };
                    }
                  }

                  // 奖品相关列不合并
                  return {};
                }}
            >
              <Table.Column
                title="段数"
                dataIndex="sectionNum"
                cell={(value, index) => {
                    return (
                      <div>{value || '--'}</div>
                    );
                  }}
              />
              <Table.Column
                title="品线"
                dataIndex="goodsLine"
                cell={(value, index) => {
                    return (
                      <div>{value || '--'}</div>
                    );
                  }}
              />
              <Table.Column
                title="SKU名称"
                dataIndex="skuName"
                cell={(value, index) => {
                    return (
                      <div>{value || '--'}</div>
                    );
                  }}
              />
              <Table.Column
                title="SKU ID"
                dataIndex="skuId"
                cell={(value, index) => {
                    return (
                      <div>{value || '--'}</div>
                    );
                  }}
              />
              <Table.Column
                title="默认兜底图"
                dataIndex="skuMainPicture"
                cell={(value, index) => {
                    return (
                      <img src={value} alt="" />
                    );
                  }}
              />
              <Table.Column
                title="每日限额"
                dataIndex="dayLimitCount"
                cell={(value, index) => {
                    return (
                      <div>{value || '--'}</div>
                    );
                  }}
              />
              <Table.Column
                title="奖品"
                dataIndex="lotteryName"
              />
              <Table.Column
                title="奖品类型"
                dataIndex="lotteryType"
                cell={(value) => getPrizeTypeLabel(value)}
              />
              <Table.Column
                title="单位数量"
                cell={(value, index, record) => {
                    if (!record.lotteryType) return '-';
                    if (record.lotteryType === PrizeTypeEnum.MEMBER_POINT.value) {
                      return record.lotteryValue;
                    } else {
                      return 1;
                    }
                  }}
              />
              <Table.Column
                title="发放份数"
                dataIndex="prizeNum"
              />
              <Table.Column
                title="奖品行操作"
                cell={(_, index) => (
                  <div>
                    {(demoSkuList[index].prizeList || []).map((p: any, i: number) => (
                      <div key={i} style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 6 }}>
                        <Button text type="primary" onClick={() => openEditPrize(index, i)}>编辑</Button>
                        <Button text onClick={() => deletePrize(index, i)}>删除</Button>
                      </div>
                    ))}
                  </div>
                  )}
              />
              <Table.Column
                title="SKU行操作"
                cell={(_, index, row) => {
                  const currentSku = demoSkuList[row.skuIndex];
                  const prizeCount = currentSku.prizeList ? currentSku.prizeList.length : 0;
                  return (
                    <div style={{ display: 'flex', flexDirection: 'column' }}>
                      <Button
                        type="secondary"
                        size="small"
                        onClick={() => addPrizeToSku(row.skuIndex)}
                        disabled={prizeCount >= 3 || needDisable}
                      >
                        添加奖品
                      </Button>
                      <Button
                        type="normal"
                        size="small"
                        style={{ margin: '10px auto 0' }}
                        onClick={() => deleteSku(row.skuIndex)}
                        disabled={demoSkuList.length === 1 || needDisable}
                      >
                        删除SKU
                      </Button>
                    </div>
                  );
                }}
              />
            </Table>
            {/* ) */}
            {/* } */}
          </div>
        </FormItem>
      </Form>

      <Dialog visible={visible} footer={false} title={editPrizeIndex >= 0 ? '编辑奖品' : '新增奖品'} onClose={() => setVisible(false)}>
        <Form labelAlign="left" style={{ width: 480 }}>
          <FormItem label="奖品名称" required>
            <Input value={editPrize.prizeName} onChange={(v) => setEditPrize({ ...editPrize, prizeName: v })} />
          </FormItem>
          <FormItem label="发放份数" required>
            <NumberPicker min={1} value={Number(editPrize.sendTotalCount) || 1} onChange={(v) => setEditPrize({ ...editPrize, sendTotalCount: Number(v) })} />
          </FormItem>
          <FormItem label="生效起止">
            <DatePicker2.RangePicker
              value={editPrize.startDate && editPrize.endDate ? [dayjs(editPrize.startDate).toDate(), dayjs(editPrize.endDate).toDate()] : []}
              showTime
              onChange={(range: any) => {
                if (range && range.length === 2) {
                  setEditPrize({ ...editPrize, startDate: dayjs(range[0]).format('YYYY-MM-DD HH:mm:ss'), endDate: dayjs(range[1]).format('YYYY-MM-DD HH:mm:ss') });
                }
              }}
            />
          </FormItem>
          <div style={{ textAlign: 'right' }}>
            <Button onClick={() => setVisible(false)} style={{ marginRight: 8 }}>取消</Button>
            <Button type="primary" onClick={savePrize}>保存</Button>
          </div>
        </Form>
      </Dialog>
    </Container>
  );
}

