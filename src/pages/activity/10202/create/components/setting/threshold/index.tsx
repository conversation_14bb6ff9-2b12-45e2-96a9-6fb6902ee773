import Container from '@/components/Container';
import { useThreshold } from './hooks';
import { Form, Radio } from '@alifd/next';

export default function Threshold() {
  const { threshold } = useThreshold();

  return (
    <Container title="活动门槛">
      <Form.Item label="参与人群" required>
        <Radio.Group value={threshold.isMember}>
          <Radio value={1}>店铺会员</Radio>
        </Radio.Group>
      </Form.Item>
    </Container>
  );
}