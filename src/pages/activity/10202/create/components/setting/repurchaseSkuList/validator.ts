import { AppState } from '@/pages/activity/10202/create/type';

export const validateRepurchaseSkuList = (state: AppState): string[] => {
  const errors: string[] = [];
  const { repurchaseSkuList } = state as any;
  if (!repurchaseSkuList || repurchaseSkuList.length === 0) {
    errors.push('请上传复购SKU数据');
    return errors;
  }
  repurchaseSkuList.forEach((sku: any, i: number) => {
    if (!sku.skuId) errors.push(`复购SKU第${i + 1}行缺少skuId`);
    if (!sku.skuName) errors.push(`复购SKU第${i + 1}行缺少商品名称`);
    if (sku.sectionNum === undefined || sku.sectionNum === null) errors.push(`复购SKU第${i + 1}行缺少段数`);
    if (!sku.goodsLine) errors.push(`复购SKU第${i + 1}行缺少品线`);
  });
  return errors;
};

