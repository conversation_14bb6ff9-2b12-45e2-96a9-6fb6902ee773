import Container from '@/components/Container';
import { Button, Form, Input, NumberPicker, Table } from '@alifd/next';
import { useActivity } from '../../../reducer';
import { ModuleType, RepurchaseSkuListState } from '@/pages/activity/10202/create/type';

export default function RepurchaseSkuList() {
  const { state, dispatch } = useActivity();
  const repurchaseSkuList: RepurchaseSkuListState[] = state.repurchaseSkuList || [];

  const updateList = (list: RepurchaseSkuListState[]) => {
    dispatch({ type: 'UPDATE_REPURCHASE_SKU_LIST', payload: list });
    dispatch({ type: 'CLEAR_ERRORS', module: ModuleType.REPURCHASE_SKU_LIST });
  };

  const addSku = () => {
    const next = [...repurchaseSkuList, { skuId: '', skuName: '', sectionNum: 1, goodsLine: '', skuMainPicture: '' } as any];
    updateList(next);
  };

  const removeSku = (index: number) => {
    const next = [...repurchaseSkuList];
    next.splice(index, 1);
    updateList(next);
  };

  const updateSkuField = (index: number, field: keyof RepurchaseSkuListState, value: any) => {
    const next = [...repurchaseSkuList];
    (next[index] as any)[field] = value;
    updateList(next);
  };

  return (
    <Container title="复购SKU设置">
      <div style={{ marginBottom: 12 }}>
        <Button type="primary" onClick={addSku}>新增复购SKU</Button>
      </div>
      <div style={{ overflowX: 'auto' }}>
        <Table dataSource={repurchaseSkuList} primaryKey="skuId">
          <Table.Column title="段数" dataIndex="sectionNum" cell={(v, idx) => (
            <NumberPicker min={1} value={Number(v) || 1} onChange={(val) => updateSkuField(idx, 'sectionNum', Number(val))} />
          )} />
          <Table.Column title="品线" dataIndex="goodsLine" cell={(v, idx) => (
            <Input value={v} onChange={(val) => updateSkuField(idx, 'goodsLine', val)} />
          )} />
          <Table.Column title="SKU名称" dataIndex="skuName" cell={(v, idx) => (
            <Input value={v} onChange={(val) => updateSkuField(idx, 'skuName', val)} />
          )} />
          <Table.Column title="SKU ID" dataIndex="skuId" cell={(v, idx) => (
            <Input value={v} onChange={(val) => updateSkuField(idx, 'skuId', val)} />
          )} />
          <Table.Column title="默认兜底图" dataIndex="skuMainPicture" cell={(v, idx) => (
            <Input value={v} onChange={(val) => updateSkuField(idx, 'skuMainPicture', val)} />
          )} />
          <Table.Column title="操作" cell={(_, index) => (
            <Button text onClick={() => removeSku(index)}>删除SKU</Button>
          )} />
        </Table>
      </div>
    </Container>
  );
}

