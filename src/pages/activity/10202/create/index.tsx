import useStepNavigation from '@/hooks/useStepNavigation';
import { useEffect, useState } from 'react';
import { Box, Loading, Step } from '@alifd/next';
import Setting from './components/setting';
import Complete from './components/complete';
import Decorate from './components/decorate';
import Rule from './components/rule';
import { ActivityProvider, useActivity } from './reducer';
import styles from './index.module.scss';
import { useLocation } from 'ice';
import { BaseInfoState } from './type';
import dayjs from 'dayjs';
import { activityGetDecorationData } from '@/api/common';

const steps = [
  { title: '基础玩法设置', component: Setting },
  { title: '规则设置', component: Rule },
  { title: '氛围定制', component: Decorate },
  { title: '完成创建并投放', component: Complete },
];

function ActivityCreate() {
  const { activeStep, goNextStep, goPrevStep } = useStepNavigation(0, steps.length);
  const [loading, setLoading] = useState<boolean>(false);

  const { dispatch } = useActivity();

  const location = useLocation();
  const { configValue, operationType, activityId } = location.state;

  const isCreate = operationType === 'add';
  const isEdit = operationType === 'edit';
  const isCopy = operationType === 'copy';
  const isView = operationType === 'view';

  const getActivityStatus = (base: BaseInfoState) => {
    const startTime = dayjs(base?.startTime);
    const endTime = dayjs(base?.endTime);
    const now = dayjs();
    // 未开始
    if (now.isBefore(startTime)) {
      return 1;
    } else if (now.isAfter(endTime)) {
      // 已结束
      return 3;
    } else {
      // 进行中
      return 2;
    }
  };

  const getActivityDetail = async () => {
    setLoading(true);
    try {
      const { activityData, decorationData } = await activityGetDecorationData({ activityId });

      const activityDataObj = JSON.parse(activityData!);
      activityDataObj.base.activityId = activityId;
      delete activityDataObj.extra;
      if (isCopy) {
        delete activityDataObj.repurchasePrizeList;
        activityDataObj.rule = '';
        activityDataObj.demoSkuList = [];
        activityDataObj.repurchaseSkuList = [];
      }
      // 记录活动原始结束时间
      !isCopy && dispatch({
        type: 'UPDATE_EXTRA',
        payload: {
          originalEndTime: activityDataObj.base?.endTime,
          activityStatus: getActivityStatus(activityDataObj.base),
        },
      });
      // 初始化活动数据
      dispatch({
        type: 'INIT_MODULE',
        payload: {
          activityData: activityDataObj,
          decorationData,
        },
      });
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = (stepIndex: number) => {
    const stepItem = steps[stepIndex];
    const StepComponent: any = stepItem.component;
    return (
      <StepComponent
        goNextStep={goNextStep}
        goPrevStep={goPrevStep}
      />
    );
  };

  console.table({
    isCreate,
    isEdit,
    isCopy,
    isView,
  });

  useEffect(() => {
    // 记录操作类型
    dispatch({ type: 'UPDATE_EXTRA', payload: { operationType } });
    // 创建时初始化装修数据
    if (isCreate && configValue) {
      dispatch({ type: 'UPDATE_DECORATE', payload: configValue });
    }
    // 编辑时初始化活动数据
    if (isEdit || isCopy || isView) {
      getActivityDetail();
    }
  }, []);

  return (
    <>
      {loading ? <Loading visible={loading} style={{ width: '100%', height: 800 }} /> : (
        <div className={styles.createPage}>
          <Box
            direction="row"
            justify="center"
            padding={[0, 0, 16]}
            className={styles.stepTitle}
          >
            <Step
              className="step"
              current={activeStep}
              labelPlacement="hoz"
              shape="circle"
              readOnly
            >
              {steps.map((item, idx) => (
                <Step.Item
                  key={idx}
                  title={item.title}
                />
              ))}
            </Step>
          </Box>
          <div className={styles.stepContent}>
            {renderStepContent(activeStep)}
          </div>
        </div>
      )}
    </>
  );
}

export default function CustomActivityCreatePage() {
  return (
    <ActivityProvider>
      <ActivityCreate />
    </ActivityProvider>
  );
}