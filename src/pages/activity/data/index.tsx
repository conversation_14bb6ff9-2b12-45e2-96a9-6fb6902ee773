import React, { useState } from 'react';
import { Box, Tab } from '@alifd/next';

import AwardRecord from './components/AwardRecord/AwardRecord';
import PhysicalPrizeRecord from './components/PhysicalPrizeRecord/PhysicalPrizeRecord';
import OrderRecord from './components/OrderRecord/OrderRecord';
import ActivityData from './components/ActivityData/ActivityData';
import StairsData from './components/StairsData/StairsData';
import { useLocation } from '@ice/runtime/router';
import Container from '@/components/Container';

const tabList = [
  { title: '领奖记录', key: 'award', component: AwardRecord },
  { title: '实物中奖信息', key: 'physical', component: PhysicalPrizeRecord },
  { title: '订单记录', key: 'order', component: OrderRecord },
  { title: '活动数据', key: 'activity', component: ActivityData },
  { title: '阶梯数据', key: 'stairs', component: StairsData },
];

export default function ActivityDataPage() {
  const location = useLocation();
  console.log('location', location);
  const { record } = location.state || {};
  console.log('location=record', record);
  const [activeTab, setActiveTab] = useState(tabList[0].key);

  return (
    <Tab
      shape="pure"
      activeKey={activeTab}
      onChange={setActiveTab}
    >
      {tabList.map(tab => {
          const Component = tab.component;
          return (
            <Tab.Item
              key={tab.key}
              title={tab.title}
            >
              <Box padding={[20, 0]}>
                <Container>
                  { activeTab === tab.key && <Component record={record} /> }
                </Container>
              </Box>
            </Tab.Item>
          );
        })}
    </Tab>
  );
}
