import React, { useState, useEffect } from 'react';
import { Box, Button, Progress, Input, Table, Pagination, Message, Select } from '@alifd/next';
import { echartsGetAchieveDetail } from '@/api/b';
import { maskSensitiveInfo } from '@/utils';
import usePagination from '@/hooks/usePagination';

export default function AchieveDetail({ activityId }) {
  const [userNick, setUserNick] = useState<any>('');
  const [ouid, setOuid] = useState<any>('');
  const [dataList, setDataList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<any>(-1); // 达成进度状态

  // 使用分页Hook
  const pagination = usePagination(1, 5, (current, size) => {
    if (activityId) {
      fetchData({
        pageNum: current,
        pageSize: size,
      });
    }
  });

  useEffect(() => {
    if (activityId) {
      fetchData();
    }
  }, [activityId]);

  const fetchData = async (params?: any) => {
    setLoading(true);
    try {
      const res: any = await echartsGetAchieveDetail({
        activityId,
        userNick: userNick.trim(),
        ouid: ouid.trim(),
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        status,
        ...params,
      });
      setDataList(res.list || []);
      pagination.setTotal(res?.total || 0);
    } catch (e) {
      console.error('查询异常:', e);
      Message.error('查询异常');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    pagination.changePage(1);
  };

  const handleReset = () => {
    setUserNick('');
    setOuid('');
    setStatus(-1);
    pagination.reset();

    // 手动调用查询确保使用重置后的查询条件
    if (activityId) {
      fetchData({
        pageNum: 1,
        pageSize: 5,
        userNick: '',
        ouid: '',
        status: -1,
      });
    }
  };

  return (
    <Box spacing={16}>
      {/* 查询区域 */}
      <Box
        direction="row"
        spacing={10}
      >
        <Input
          label="用户账号"
          value={userNick}
          onChange={setUserNick}
          style={{ width: 200 }}
        />
        <Input
          label="openId"
          value={ouid}
          onChange={setOuid}
          style={{ width: 200 }}
        />
        <Select label="达成进度" value={status} onChange={setStatus} style={{ width: 200 }}>
          <Select.Option value={-1}>全部</Select.Option>
          <Select.Option value={0}>未达成</Select.Option>
          <Select.Option value={1}>有达成</Select.Option>
        </Select>
        <Button
          type="primary"
          onClick={handleSearch}
        >
          查询
        </Button>
        <Button
          type="normal"
          onClick={handleReset}
        >
          重置
        </Button>
      </Box>

      {/* 表格 */}
      <Table
        dataSource={dataList}
        hasBorder
        loading={loading}
      >
        <Table.Column
          title="用户账号"
          dataIndex="userNick"
          cell={(value) => {
            return maskSensitiveInfo(value) || '-';
          }}
        />
        <Table.Column
          title="openId"
          dataIndex="ouid"
          cell={(value) => {
            return maskSensitiveInfo(value) || '-';
          }}
        />
        <Table.ColumnGroup title="已下单">
          <Table.Column
            title="订单数"
            dataIndex="payTrade.tradeNum"
          />
          <Table.Column
            title="商品数（件）"
            dataIndex="payTrade.reduceNum"
          />
          <Table.Column
            title="订单金额（元）"
            dataIndex="payTrade.reducePrice"
          />
        </Table.ColumnGroup>
        <Table.ColumnGroup title="已收货">
          <Table.Column
            title="订单数"
            dataIndex="getGoodsTrade.tradeNum"
          />
          <Table.Column
            title="商品数（件）"
            dataIndex="getGoodsTrade.reduceNum"
          />
          <Table.Column
            title="订单金额（元）"
            dataIndex="getGoodsTrade.reducePrice"
          />
        </Table.ColumnGroup>
        <Table.Column
          title="活动达成进度"
          cell={(value, index, record) => <Progress percent={parseFloat(record.completion)} />}
        />
      </Table>
      <Box direction="row" justify="flex-end">
        {/* 分页 */}
        <Pagination
          {...pagination.paginationProps}
          shape={'arrow-only'}
          pageSizeSelector="dropdown"
          pageSizePosition="end"
          totalRender={total => `共 ${total} 条`}
        />
      </Box>
    </Box>
  );
}
