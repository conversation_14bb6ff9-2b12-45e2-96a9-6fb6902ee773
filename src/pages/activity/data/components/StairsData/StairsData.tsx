import React, { useState, useEffect } from 'react';
import { Box, Button, Card, Icon, Tab, Message, Loading } from '@alifd/next';
import { echartsGetAchieveInfo } from '@/api/b';
import './StairsData.less';

import StairsDataOverview from './StairsDataOverview';
import StairsPrizeList from './StairsPrizeList';
import AchieveDetail from './AchieveDetail';

export default function StairsData({ record }) {
  const { activityId, endTime } = record;
  const [achieveInfo, setAchieveInfo] = useState<any>(null);
  const [activeKey, setActiveKey] = useState('stairsPrize');

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (activityId) fetchData();
  }, [activityId]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const res = await echartsGetAchieveInfo({ activityId } as any);
      setAchieveInfo(res || {});
    } catch (err) {
      console.error(err);
      Message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  if (!achieveInfo) return null;

  return (
    <Card
      title="阶梯数据概览"
      subTitle={
        <Box
          style={{ display: 'inline-flex' }}
          direction="row"
          align="center"
          spacing={10}
        >
          <span style={{ color: 'var(--color-text-regular)' }}>统计截止：{achieveInfo.statisticsTime || '--'}</span>
          <Button
            type="primary"
            iconSize={'small'}
            text
            onClick={fetchData}
          >
            <Icon type="refresh" />
          </Button>
        </Box>
      }
      contentHeight="auto"
      showTitleBullet={false}
      showHeadDivider={false}
      hasBorder={false}
    >

      <Loading visible={loading} style={{ width: '100%', height: '100%' }} >
        {/* 阶梯达成概览 */}
        <StairsDataOverview achieveInfo={achieveInfo} />

        <Tab
          activeKey={activeKey}
          onChange={setActiveKey}
          contentStyle={{ marginTop: 20 }}
        >
          <Tab.Item
            key="stairsPrize"
            title="阶梯奖品明细"
          >
            <StairsPrizeList
              achieveInfo={achieveInfo}
              fetchData={fetchData}
              endTime={endTime}
            />
          </Tab.Item>

          <Tab.Item
            key="activityAchieve"
            title="活动达成明细"
          >
            <AchieveDetail activityId={activityId} />
          </Tab.Item>
        </Tab>
      </Loading>
    </Card>
  );
}
