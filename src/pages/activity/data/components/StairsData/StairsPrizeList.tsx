import { Button, Table } from '@alifd/next';
import { getPrizeTypeLabel } from '@/utils';
import appendPrize from './appendPrize/appendPrize';
import dayjs from 'dayjs';

export default function StairsPrizeList({ achieveInfo, fetchData, endTime }) {
  const prizeTableData = achieveInfo.stairsList.flatMap(stair => {
    return stair.lotteryList.map((lottery, index) => ({
      key: `${stair.name}-${index}`,
      stairName: stair.name,
      achieveNum: stair.achieveNum,
      id: lottery.id,
      lotteryName: lottery.lotteryName,
      prizeType: getPrizeTypeLabel(lottery.lotteryType),
      prizeNum: lottery.prizeNum,
      sendPrizeNum: lottery.sendPrizeNum,
      resNum: lottery.resNum,
      lotteryValue: lottery.lotteryValue,
      showStairInfo: index === 0, // 只有第一条显示阶梯信息
      lotteryType: lottery.lotteryType,
    }));
  });

  return (
    <Table
      dataSource={prizeTableData}
      hasBorder
      cellProps={(rowIndex, colIndex, dataIndex, record) => {
        if (dataIndex === 'stairName' || dataIndex === 'achieveNum') {
          if (record.showStairInfo) {
            const sameStairCount = prizeTableData.filter(item => item.stairName === record.stairName).length;
            return { rowSpan: sameStairCount };
          } else {
            return { rowSpan: 0 };
          }
        }
        return {};
      }}
    >
      <Table.Column
        title="阶梯名称"
        dataIndex="stairName"
      />
      <Table.Column
        title="阶梯达成人数"
        dataIndex="achieveNum"
      />
      <Table.Column
        title="阶梯奖品"
        dataIndex="lotteryName"
      />
      <Table.Column
        title="奖品类型"
        dataIndex="prizeType"
      />
      <Table.Column
        title="发放总量"
        dataIndex="prizeNum"
      />
      <Table.Column
        title="已发放数"
        dataIndex="sendPrizeNum"
      />
      <Table.Column
        title="剩余库存"
        dataIndex="resNum"
      />
      <Table.Column
        title="操作"
        cell={(value, index, record) => (
          <Button
            type="primary"
            text
            disabled={dayjs().isAfter(dayjs(endTime))}
            onClick={async () => {
              const result = await appendPrize({ record });
              if (result) {
                fetchData(); // 追加成功，刷新表格
              }
            }}
          >
            补充奖品
          </Button>
        )}
      />
    </Table>
  );
}
