import React from 'react';
import { Box } from '@alifd/next';

export default function StairsDataOverview({ achieveInfo }) {
  return (
    <Box
      direction="row"
      margin={[0, 0, 10, 0]}
      spacing={16}
    >
      {achieveInfo.stairsList.map((item, index) => {
        const criteriaComponents: any = [];
        if (item.tradeNum) {
          criteriaComponents.push(
            <>
              订单笔数大于等于
              <span className="stairs-data-info-num">{item.tradeNum}</span>单
            </>,
          );
        }
        if (item.nums) {
          criteriaComponents.push(
            <>
              订单中符合条件的商品累计满
              <span className="stairs-data-info-num">{item.nums}</span>件
            </>,
          );
        }
        if (item.payment) {
          criteriaComponents.push(
            <>
              订单金额大于等于
              <span className="stairs-data-info-num">{item.payment}</span>元
            </>,
          );
        }
        // 插入连接词"且"
        const interleaved: any = [];
        criteriaComponents.forEach((item, idx) => {
          interleaved.push(item);
          if (idx < criteriaComponents.length - 1) {
            interleaved.push(
              <span
                key={`connector-${idx}`}
                className="connector-text"
              >
                且
              </span>,
            );
          }
        });
        return (
          <div
            key={index}
            className="stairs-data-item"
          >
            <div className="stairs-data-title">
              {item.name}：达成人数：<span>{item.achieveNum}</span>人
            </div>
            <div className="stairs-data-info">
              <span className="stairs-data-info-label">阶梯任务：</span>
              <div className="stairs-data-info-content">{interleaved}</div>
            </div>
          </div>
        );
      })}
    </Box>
  );
}
