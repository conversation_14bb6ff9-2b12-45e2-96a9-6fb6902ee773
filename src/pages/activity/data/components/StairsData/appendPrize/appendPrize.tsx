import { Dialog } from '@alifd/next';
import React from 'react';
import AppendPrizeContent from './AppendPrizeContent';
import { addDialogRef } from '@/utils/dialogMapper';

/* 追加奖品 */
export default function appendPrize({ record }) {
  return new Promise(resolve => {
    const dialogRef = Dialog.show({
      v2: true,
      title: '补充奖品',
      width: 400,
      centered: true,
      closeMode: ['close'],
      content: (
        <AppendPrizeContent
          record={record}
          onResolve={result => {
            resolve(result);
            dialogRef.hide();
          }}
        />
      ),
      footer: false,
    });
    addDialogRef(dialogRef);
  });
}
