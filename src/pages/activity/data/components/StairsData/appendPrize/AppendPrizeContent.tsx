import { useEffect, useState } from 'react';
import { Form, Field, Input, Button, Box, Message } from '@alifd/next';
import { echartsAddLotteryNum } from '@/api/b';
import NumberInput from '@/components/NumberInput/NumberInput';
import { prizeGetSkuInfo } from '@/api/vf';
import { PrizeTypeEnum } from '@/utils';

export default function AppendPrizeContent({ record, onResolve }) {
  const appendPrizeField = Field.useField({
    values: {
      addNum: '',
    },
  });
  const [remain, setRemain] = useState('');
  const [loading, setLoading] = useState(false);

  const noStock = () => {
    return record.lotteryType === PrizeTypeEnum.COUPON.value || record.lotteryType === PrizeTypeEnum.MEMBER_POINT.value;
  };
  const handleOk = async () => {
    appendPrizeField.validatePromise().then(async ({ errors }) => {
      if (errors) return;
      const addNum = appendPrizeField.getValue('addNum');
      setLoading(true);
      try {
        await echartsAddLotteryNum({
          id: record.id,
          lotteryValue: record.lotteryValue,
          addNum,
        } as any);
        Message.success('追加成功');
        onResolve(true);
      } catch (e) {
        Message.error(e.errorMessage || e.message);
      } finally {
        setLoading(false);
      }
    });
  };

  const handleCancel = () => {
    onResolve(null);
  };

  const getPrizeInfo = async () => {
    const res = await prizeGetSkuInfo({
      skuCode: record.lotteryValue,
    });
    setRemain(res.remainPrizeQuantity!);
  };

  useEffect(() => {
    if (!noStock()) {
      getPrizeInfo().then();
    }
  }, []);

  return (
    <Form
      field={appendPrizeField}
      className="edit-prize-form"
    >
      <Form.Item label="实物奖品名称">
        <Input
          disabled
          value={record.lotteryName}
        />
      </Form.Item>
      {
        !noStock() && (
          <Form.Item label="当前可用库存">
            <Input
              disabled
              value={remain}
            />
          </Form.Item>
        )
      }

      <Form.Item
        name="addNum"
        label="追加库存数"
        required
        requiredMessage="请输入追加库存数"
        validator={(_, value) => {
          if (noStock()) {
            console.log(1);
            return Promise.resolve();
          } else {
            if (value === '' || value === undefined || value === null) {
              return Promise.reject('请输入追加库存数');
            }
            const num = Number(value);
            if (isNaN(num)) {
              return Promise.reject('请输入数字');
            }
            if (num <= 0) {
              return Promise.reject('追加数量必须大于0');
            }
            if (num > Number(remain)) {
              return Promise.reject(`追加数量不能大于当前可用库存${record.remain}`);
            }
            return Promise.resolve();
          }
        }}
      >
        <NumberInput
          min={1}
          max={noStock() ? 999999 : Number(remain)}
          style={{ width: 200 }}
        />
      </Form.Item>
      <Box
        direction="row"
        spacing={10}
        justify="center"
        margin={[20, 0, 0, 0]}
      >
        <Button
          onClick={handleCancel}
          disabled={loading}
        >
          取消
        </Button>
        <Button
          type="primary"
          loading={loading}
          onClick={handleOk}
        >
          确认追加
        </Button>
      </Box>
    </Form>
  );
}
