import React, { useState, useEffect } from 'react';
import { Box, Button, Input, DatePicker2, Table, Pagination, Message } from '@alifd/next';
import { activityDataGetPhysicalPrizeInfoPage, activityDataExportPhysicalPrizeInfoExport } from '@/api/b';
import { downloadExcel, maskSensitiveInfo } from '@/utils';
import dayjs from 'dayjs';
import { Auth } from '@/components/Auth';
import usePagination from '@/hooks/usePagination';

export default function PhysicalPrizeRecord({ record }) {
  const { activityId } = record;

  // 查询条件
  const [userNick, setUserNick] = useState<any>('');
  const [ouid, setOuid] = useState<any>('');
  const [timeRange, setTimeRange] = useState<any>([]);

  // 表格相关
  const [tableData, setTableData] = useState([]);
  const [loading, setLoading] = useState(false);

  // 使用分页Hook
  const pagination = usePagination(1, 10, (current, size) => {
    if (activityId) {
      fetchData({
        pageNum: current,
        pageSize: size,
      });
    }
  });

  useEffect(() => {
    if (activityId) fetchData();
  }, [activityId]);

  const fetchData = async (params?: any) => {
    const [start, end] = timeRange;
    setLoading(true);
    try {
      const res: any = await activityDataGetPhysicalPrizeInfoPage({
        activityId,
        userNick: userNick.trim(),
        ouid: ouid.trim(),
        createTimeStart: start ? dayjs(start).format('YYYY-MM-DD 00:00:00') : '',
        createTimeEnd: end ? dayjs(end).format('YYYY-MM-DD 23:59:59') : '',
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        ...params,
      });
      setTableData(res?.records || []);
      pagination.setTotal(res?.total || 0);
    } catch (err) {
      console.error(err);
      Message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async () => {
    setLoading(true);
    const [start, end] = timeRange;
    try {
      const data: any = await activityDataExportPhysicalPrizeInfoExport({
        activityId,
        userNick: userNick.trim(),
        ouid: ouid.trim(),
        createTimeStart: start ? dayjs(start).format('YYYY-MM-DD 00:00:00') : '',
        createTimeEnd: end ? dayjs(end).format('YYYY-MM-DD 23:59:59') : '',
      } as any);
      downloadExcel(data, `实物中奖信息${dayjs().format('YYYY-MM-DD HH:mm')}`);
    } catch (err) {
      console.error(err);
      Message.error('导出失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    pagination.changePage(1);
  };

  const handleReset = () => {
    setUserNick('');
    setOuid('');
    setTimeRange([]);
    pagination.reset();

    // 手动调用查询确保使用重置后的查询条件
    if (activityId) {
      fetchData({
        pageNum: 1,
        pageSize: 10,
        userNick: '',
        ouid: '',
        createTimeStart: '',
        createTimeEnd: '',
      });
    }
  };

  return (
    <Box spacing={16}>
      {/* 筛选表单 */}
      <Box
        direction="row"
        spacing={10}
        align="center"
      >
        <Input
          label="用户昵称"
          value={userNick}
          onChange={setUserNick}
        />
        <Input
          label="openId"
          value={ouid}
          onChange={setOuid}
        />
        <DatePicker2.RangePicker
          label="获奖时间"
          value={timeRange}
          onChange={setTimeRange}
        />
        <Button
          type="primary"
          onClick={handleSearch}
        >
          查询
        </Button>
        <Button
          type="normal"
          onClick={handleReset}
        >
          重置
        </Button>
        <Auth authKey={'activity_list_data_export'}>
          <Button
            type="secondary"
            onClick={handleExport}
          >
            导出
          </Button>
        </Auth>
      </Box>

      {/* 表格 */}
      <Table
        dataSource={tableData}
        hasBorder
        loading={loading}
      >
        <Table.Column
          title="奖品名称"
          dataIndex="lotteryName"
        />
        <Table.Column
          title="用户昵称"
          dataIndex="userNick"
          cell={(value) => {
            return maskSensitiveInfo(value) || '-';
          }}
        />
        <Table.Column
          title="openId"
          dataIndex="ouid"
          cell={(value) => {
            return maskSensitiveInfo(value) || '-';
          }}
        />
        <Table.Column
          title="获奖时间"
          dataIndex="createTime"
          cell={(value) => {
            return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
          }}
        />
        <Table.Column
          title="收件人"
          dataIndex="receiver"
        />
        <Table.Column
          title="收件人手机号"
          dataIndex="phone"
          cell={(value) => {
            return maskSensitiveInfo(value) || '-';
          }}
        />
        <Table.Column
          title="省"
          dataIndex="province"
        />
        <Table.Column
          title="市"
          dataIndex="city"
        />
        <Table.Column
          title="区"
          dataIndex="area"
        />
        <Table.Column
          title="详细地址"
          dataIndex="address"
        />
      </Table>

      {/* 分页 */}
      <Box direction="row" justify="flex-end">
        <Pagination
          {...pagination.paginationProps}
          shape={'arrow-only'}
          totalRender={total => `共 ${total} 条`}
          pageSizeSelector="dropdown"
          pageSizePosition="end"
        /></Box>
    </Box>
  );
}
