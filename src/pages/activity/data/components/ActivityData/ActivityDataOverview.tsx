import React, { useState, useEffect, useRef } from 'react';
import { FunnelChart } from 'bizcharts';
import { Box, Button, Card, Icon, ResponsiveGrid, Message, Loading } from '@alifd/next';
import DataCard from '@/components/DataCard/DataCard';
import { echartsGetActivityData } from '@/api/b';

export default function ActivityDataOverview({ record }) {
  const { activityId } = record;
  const [activityData, setActivityData] = useState<any>(null);
  const chartRef: any = useRef(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (activityId) {
      fetchData().then();
    }
  }, [activityId]);

  useEffect(() => {
    return () => {
      if (chartRef.current) {
        chartRef.current = null;
      }
    };
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      const res = await echartsGetActivityData({ activityId } as any);
      console.log(res, ' res');
      setActivityData(res);
    } catch (err) {
      console.error(err);
      Message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  if (!activityData) return null;

  return (
    <Loading visible={loading} style={{ width: '100%', height: '100%' }}>
      <Box
        direction="row"
        align="center"
        spacing={16}
        style={{ overflow: 'auto' }}
      >
        <Card
          title="活动参与数据"
          subTitle={
            <Box
              style={{ display: 'inline-flex' }}
              direction="row"
              align="center"
              spacing={10}
            >
              <span style={{ color: 'var(--color-text-regular)' }}>统计截止：{activityData?.statisticsTime || '--'}</span>
              <Button
                type="primary"
                iconSize={'small'}
                text
                onClick={() => fetchData()}
              >
                <Icon type="refresh" />
              </Button>
            </Box>
          }
          contentHeight={'auto'}
          showTitleBullet={false}
          showHeadDivider={false}
          style={{ flexShrink: 0 }}
        >
          <Box
            direction="row"
            align="center"
            spacing={16}
          >
            <Box spacing={10}>
              <div>
                <DataCard
                  title="PV"
                  tooltip="时间窗内，访问用户的次数"
                  value={activityData?.pv}
                />
              </div>
              <div>
                <DataCard
                  title="UV"
                  tooltip="时间窗内，访问用户的数量（活动全程去重）"
                  value={activityData?.uv}
                />
              </div>
              <div>
                <DataCard
                  title="活动入会人数"
                  tooltip="时间窗内，通过活动入会的人数（活动全程去重）"
                  value={activityData?.joinMemberUv}
                />
              </div>
              <div>
                <DataCard
                  title="参与人数"
                  tooltip="时间窗内，活动报名人数（活动全程去重）"
                  value={activityData?.joinUv}
                />
              </div>
            </Box>
            <FunnelChart
              onGetG2Instance={g2 => {
                chartRef.current = g2.chart;
              }}
              width={400}
              data={[
                { action: 'PV', num: activityData?.pv || 0 },
                { action: 'UV', num: activityData?.uv || 0 },
                { action: '活动入会人数', num: activityData?.joinMemberUv || 0 },
                { action: '参与人数', num: activityData?.joinUv || 0 },
              ]}
              xField="action"
              yField="num"
              conversionTag={false}
              label={{
                position: 'right',
                offsetX: 10,
                style: { fill: '#333', fontSize: 12 },
              }}
              tooltip={{
                showMarkers: false,
              }}
            />
          </Box>
        </Card>

        <DataCard
          title="下单转化率"
          tooltip="（活动下单人数 / 活动参与人数）*100%"
          value={activityData?.divOrder}
          warning
          connectingLine
        />

        <Card
          title="购物订单数据"
          contentHeight={'auto'}
          showTitleBullet={false}
          showHeadDivider={false}
          style={{ flexShrink: 0 }}
        >
          <ResponsiveGrid gap={10}>
            <ResponsiveGrid.Cell
              colSpan={8}
              rowSpan={3}
            >
              <Box
                className="order-num-cell"
                justify="space-between"
              >
                <DataCard
                  title="活动下单人数"
                  tooltip={'（1）【玩法设置】中所设【下单时间】内有下单的买家总数\n（2）若勾选【仅统计报名后订单】，则仅统计报名后有下单的买家总数'}
                  value={activityData?.orderNum}
                />
                <Box
                  direction="row"
                  justify="space-between"
                >
                  <DataCard
                    title="下单新客人数"
                    tooltip={'【玩法设置】中所设【下单时间】之前无店铺购买记录，【下单时间】内有购买商品的买家数'}
                    value={activityData?.newUserOrderNum}
                  />
                  <DataCard
                    title="下单老客人数"
                    tooltip={'【玩法设置】中所设【下单时间】之前有店铺购买记录，【下单时间】内也有购买商品的买家数'}
                    value={activityData?.oldUserOrderNum}
                  />
                </Box>
              </Box>
            </ResponsiveGrid.Cell>
            <ResponsiveGrid.Cell
              colSpan={4}
              rowSpan={1}
            >
              <DataCard
                title="活动下单数"
                tooltip={'（1）【玩法设置】中所设下单时间内的有效订单总数（排除退款）\n（2）若勾选【仅统计报名后订单】，则仅统计报名的有效订单数'}
                value={activityData?.activityOrderNum}
              />
            </ResponsiveGrid.Cell>
            <ResponsiveGrid.Cell
              colSpan={4}
              rowSpan={1}
            >
              <DataCard
                title="活动下单金额"
                tooltip={'有效订单总金额(排除退款)'}
                value={activityData?.activityOrderPayment}
              />
            </ResponsiveGrid.Cell>
            <ResponsiveGrid.Cell
              colSpan={4}
              rowSpan={1}
            >
              <DataCard
                title="活动下单件数"
                tooltip={'有效订单包含的有效商品件数'}
                value={activityData?.activityOrderNums}
              />
            </ResponsiveGrid.Cell>
            <ResponsiveGrid.Cell
              colSpan={4}
              rowSpan={1}
            >
              <DataCard
                title="活动FREQ"
                tooltip={'活动下单数/活动下单人数'}
                value={activityData?.freq}
              />
            </ResponsiveGrid.Cell>
            <ResponsiveGrid.Cell
              colSpan={4}
              rowSpan={1}
            >
              <DataCard
                title="活动Spending"
                tooltip={'活动下单金额/活动下单人数'}
                value={activityData?.spending}
              />
            </ResponsiveGrid.Cell>
            <ResponsiveGrid.Cell
              colSpan={4}
              rowSpan={1}
            >
              <DataCard
                title="活动AUS"
                tooltip={'活动下单金额/活动下单数'}
                value={activityData?.aus}
              />
            </ResponsiveGrid.Cell>
          </ResponsiveGrid>
        </Card>
      </Box>
    </Loading>

  );
}
