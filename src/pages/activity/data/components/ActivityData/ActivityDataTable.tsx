import React, { useState, useEffect } from 'react';
import { Box, But<PERSON>, Card, DatePicker2, Table, Pagination, Message } from '@alifd/next';
import { echartsGetActivityDataTable, exportExportExcelExport } from '@/api/b';
import { downloadExcel } from '@/utils';
import dayjs from 'dayjs';
import { Auth } from '@/components/Auth';
import usePagination from '@/hooks/usePagination';

export default function ActivityDataTable({ record }) {
  const { activityId, startTime, endTime } = record;

  const [startDate, setStartDate] = useState<any>('');
  const [endDate, setEndDate] = useState<any>('');
  const [statisticsTime, setStatisticsTime] = useState<any>('');

  const [columns, setColumns] = useState<any>([]);
  const [tableData, setTableData] = useState<any>([]);
  const [loading, setLoading] = useState(false);

  // 使用分页Hook
  const pagination = usePagination(1, 10, (current, size) => {
    if (activityId) {
      fetchData({
        pageNum: current,
        pageSize: size,
      });
    }
  });

  useEffect(() => {
    if (!activityId) return;
    setStartDate(startTime ? dayjs(startTime).format('YYYY-MM-DD') : '');
    setEndDate(endTime ? dayjs(endTime).format('YYYY-MM-DD') : '');
  }, [activityId, startTime, endTime]);

  useEffect(() => {
    if (activityId) fetchData();
  }, [activityId]);

  const fetchData = async (params?: any) => {
    setLoading(true);
    try {
      const res = await echartsGetActivityDataTable({
        activityId,
        startDate,
        endDate,
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        ...params,
      });
      const data: any = res || {};
      setStatisticsTime(data?.statisticsTime || '');
      try {
        const parsedColumns = JSON.parse((data?.title || '[]').replace(/,]$/, ']'));
        setColumns(parsedColumns);
      } catch (e) {
        console.error('解析表头失败:', e);
        Message.error('解析表头失败');
      }
      setTableData(data?.list || []);
      pagination.setTotal(data?.total || 0);
    } catch (e) {
      console.error('查询异常:', e);
      Message.error('查询异常');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    pagination.changePage(1);
  };

  const handleReset = () => {
    setStartDate(startTime ? dayjs(startTime).format('YYYY-MM-DD') : '');
    setEndDate(endTime ? dayjs(endTime).format('YYYY-MM-DD') : '');
    pagination.reset();

    // 手动调用查询确保使用重置后的查询条件
    if (activityId) {
      fetchData({
        pageNum: 1,
        pageSize: 10,
        startDate: startTime ? dayjs(startTime).format('YYYY-MM-DD') : '',
        endDate: endTime ? dayjs(endTime).format('YYYY-MM-DD') : '',
      });
    }
  };

  const handleExport = async () => {
    const exportType = 'activityDataInfo'; // 活动数据明细
    try {
      const data: any = await exportExportExcelExport({
        exportType,
        exportParam: JSON.stringify({
          activityId,
          startDate,
          endDate,
          pageNum: 1,
          pageSize: 0, // 导出全部
        }),
      });
      downloadExcel(data, `活动数据${dayjs().format('YYYY-MM-DD HH:mm')}`);
    } catch (err) {
      console.error('导出异常：', err);
      Message.error('导出异常');
    }
  };
  return (
    <Card
      title="数据明细"
      subTitle={`统计截止：${statisticsTime || '--'}`}
      contentHeight={'auto'}
      showTitleBullet={false}
      showHeadDivider={false}
      hasBorder={false}
    >
      <Box spacing={16}>
        {/* 查询区 */}
        <Box
          direction="row"
          spacing={10}
          align="center"
        >
          <DatePicker2.RangePicker
            value={[startDate, endDate]}
            onChange={([start, end]) => {
              setStartDate(start);
              setEndDate(end);
            }}
            outputFormat="YYYY-MM-DD"
            hasClear={false}
            disabledDate={date => {
              const start = dayjs(startTime);
              const end = dayjs(endTime);
              return date.isBefore(start, 'day') || date.isAfter(end, 'day');
            }}
          />
          <Button
            type="primary"
            onClick={handleSearch}
          >
            查询
          </Button>
          <Button
            type="normal"
            onClick={handleReset}
          >
            重置
          </Button>
          <Auth authKey={'activity_list_data_export'}>
            <Button
              type="secondary"
              onClick={handleExport}
            >
              导出
            </Button>
          </Auth>
        </Box>

        {/* 表格 */}
        <Table
          dataSource={tableData}
          loading={loading}
          hasBorder
        >
          {columns.map(col => (
            <Table.Column
              key={col.dataIndex}
              title={col.name}
              dataIndex={col.dataIndex}
              cell={val => val ?? '0'}
            />
          ))}
        </Table>

        <Box direction="row" justify="flex-end">
          {/* 分页 */}
          <Pagination
            {...pagination.paginationProps}
            shape={'arrow-only'}
            totalRender={total => `共 ${total} 条`}
            pageSizeSelector="dropdown"
            pageSizePosition="end"
          />
        </Box>
      </Box>
    </Card>
  );
}
