import React, { useState, useEffect } from 'react';
import { Box, Input, DatePicker2, Select, Button, Table, Pagination, Message } from '@alifd/next';
import { activityDataGetOrderRecordPage, activityDataExportOrderRecordExport } from '@/api/b';
import { downloadExcel, maskSensitiveInfo } from '@/utils';
import dayjs from 'dayjs';
import viewOrderItems from './viewOrderItems/viewOrderItems';
import { Auth } from '@/components/Auth';
import usePagination from '@/hooks/usePagination';

const ORDER_STATUS_MAP = {
  1: '待支付',
  105: '已支付',
  2: '备货中',
  3: '已发货',
  4: '已取消',
  5: '已完成',
  999: '售后退款',
};

export default function OrderRecord({ record }) {
  const { activityId } = record;

  // 查询条件
  const [userNick, setUserNick] = useState<any>('');
  const [ouid, setOuid] = useState<any>('');
  const [timeRange, setTimeRange] = useState<any>([]);
  const [orderStatus, setOrderStatus] = useState<any>('');
  const [tid, setTid] = useState<any>('');

  // 表格相关
  const [tableData, setTableData] = useState<any>([]);
  const [loading, setLoading] = useState(false);

  // 使用分页Hook
  const pagination = usePagination(1, 10, (current, size) => {
    if (activityId) {
      fetchData({
        pageNum: current,
        pageSize: size,
      });
    }
  });

  useEffect(() => {
    if (activityId) fetchData();
  }, [activityId]);

  const fetchData = async (params?: any) => {
    const [start, end] = timeRange;
    setLoading(true);
    try {
      const res: any = await activityDataGetOrderRecordPage({
        activityId,
        userNick: userNick.trim(),
        ouid: ouid.trim(),
        tid: tid.trim(),
        createTimeStart: start ? dayjs(start).format('YYYY-MM-DD 00:00:00') : '',
        createTimeEnd: end ? dayjs(end).format('YYYY-MM-DD 23:59:59') : '',
        orderStatus,
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        ...params,
      });
      setTableData(res?.records || []);
      pagination.setTotal(res?.total || 0);
    } catch (err) {
      console.error(err);
      Message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async () => {
    // 接口及权限
    // await checkButtonPermission('activityDataExport');
    setLoading(true);
    const [start, end] = timeRange;
    try {
      const data: any = await activityDataExportOrderRecordExport({
        activityId,
        userNick: userNick.trim(),
        tid: tid.trim(),
        ouid: ouid.trim(),
        createTimeStart: start ? dayjs(start).format('YYYY-MM-DD 00:00:00') : '',
        createTimeEnd: end ? dayjs(end).format('YYYY-MM-DD 23:59:59') : '',
        orderStatus,
      } as any);
      downloadExcel(data, `订单记录${dayjs().format('YYYY-MM-DD HH:mm')}`);
    } catch (err) {
      console.error(err);
      Message.error('导出失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    pagination.changePage(1);
  };

  const handleReset = () => {
    setUserNick('');
    setOuid('');
    setTimeRange([]);
    setOrderStatus('');
    setTid('');
    pagination.reset();

    // 手动调用查询确保使用重置后的查询条件
    if (activityId) {
      fetchData({
        pageNum: 1,
        pageSize: 10,
        userNick: '',
        ouid: '',
        tid: '',
        createTimeStart: '',
        createTimeEnd: '',
        orderStatus: '',
      });
    }
  };

  return (
    <Box spacing={16}>
      {/* 筛选区域 */}
      <Box
        direction="row"
        spacing={10}
        align="center"
      >
        <Input
          label="用户昵称"
          value={userNick}
          onChange={setUserNick}
        />
        <Input
          label="openId"
          value={ouid}
          onChange={setOuid}
        />
        <Input
          label="订单编号"
          value={tid}
          onChange={setTid}
        />
        <DatePicker2.RangePicker
          label="下单时间"
          value={timeRange}
          onChange={setTimeRange}
        />
        <Select
          label="订单状态"
          value={orderStatus}
          onChange={setOrderStatus}
          style={{ width: 200 }}
        >
          <Select.Option value="">全部</Select.Option>
          {Object.entries(ORDER_STATUS_MAP).map(([key, label]) => (
            <Select.Option
              key={key}
              value={key}
            >
              {label}
            </Select.Option>
          ))}
        </Select>
        <Button
          type="primary"
          onClick={handleSearch}
        >
          查询
        </Button>
        <Button
          type="normal"
          onClick={handleReset}
        >
          重置
        </Button>
        <Auth authKey={'activity_list_data_export'}>
          <Button
            type="secondary"
            onClick={handleExport}
          >
            导出
          </Button>
        </Auth>
      </Box>

      {/* 表格 */}
      <Table
        dataSource={tableData}
        loading={loading}
        hasBorder
      >
        <Table.Column
          title="用户昵称"
          dataIndex="userNick"
          cell={(value) => {
            return maskSensitiveInfo(value) || '-';
          }}
        />
        <Table.Column
          title="openId"
          dataIndex="ouid"
          cell={(value) => {
            return maskSensitiveInfo(value) || '-';
          }}
        />
        <Table.Column
          title="订单编号"
          dataIndex="tid"
        />
        <Table.Column
          title="下单时间"
          dataIndex="createTime"
        />
        <Table.Column
          title="应付金额（元）"
          dataIndex="price"
        />
        <Table.Column
          title="实付金额（元）"
          dataIndex="payment"
        />
        <Table.Column
          title="订单状态"
          dataIndex="orderStatus"
          cell={val => ORDER_STATUS_MAP[val] || '--'}
        />
        <Table.Column
          title="操作"
          cell={(_, __, record) => (
            <Button
              type="primary"
              text
              onClick={() => viewOrderItems(record)}
            >
              查看商品明细
            </Button>
          )}
        />
      </Table>

      <Box direction="row" justify="flex-end">
        {/* 分页 */}
        <Pagination
          {...pagination.paginationProps}
          shape={'arrow-only'}
          totalRender={total => `共 ${total} 条`}
          pageSizeSelector="dropdown"
          pageSizePosition="end"
        />
      </Box>
    </Box>
  );
}
