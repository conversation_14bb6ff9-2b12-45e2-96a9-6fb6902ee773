import React, { useEffect, useState } from 'react';
import { Box, Table, Pagination, Message } from '@alifd/next';
import { activityDataGetOrderItemPage } from '@/api/b';

export default function OrderItemsList({ record, onConfirm }) {
  const { tid } = record;

  const [dataList, setDataList] = useState<any>([]);
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState<any>(0);

  useEffect(() => {
    fetchList();
  }, [pageNum, pageSize]);

  const fetchList = async () => {
    try {
      const res: any = await activityDataGetOrderItemPage({
        tid,
        pageNum,
        pageSize,
      });
      const { records = [], total = 0 } = res!;
      setDataList(records);
      setTotal(total);
    } catch (err) {
      console.error(err);
      Message.error('获取商品明细失败');
    }
  };

  return (
    <Box spacing={16}>
      <Table
        dataSource={dataList}
        hasBorder
      >
        <Table.Column
          title="商品名称"
          dataIndex="title"
        />
        <Table.Column
          title="商品ID"
          dataIndex="skuId"
        />
        <Table.Column
          title="购买件数"
          dataIndex="num"
        />
        <Table.Column
          title="商品实付总价"
          dataIndex="payment"
        />
      </Table>
      <Pagination
        current={pageNum}
        pageSize={pageSize}
        total={total}
        onChange={setPageNum}
        onPageSizeChange={size => {
          setPageSize(size);
          setPageNum(1);
        }}
        totalRender={total => `共 ${total} 条`}
        pageSizeSelector="dropdown"
        pageSizePosition="end"
      />
    </Box>
  );
}
