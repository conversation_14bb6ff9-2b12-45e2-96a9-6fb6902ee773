import { Dialog, Button } from '@alifd/next';
import React from 'react';
import OrderItemsList from './OrderItemsList';
import { addDialogRef } from '@/utils/dialogMapper';

export default function viewOrderItems(record) {
  const dialogRef = Dialog.show({
    v2: true,
    title: '订单商品明细',
    width: 'auto',
    centered: true,
    closeMode: ['close'],
    content: (
      <OrderItemsList
        record={record}
        onConfirm={() => {
          dialogRef.hide();
        }}
      />
    ),
    footer: (
      <>
        <Button
          type="primary"
          onClick={() => dialogRef.hide()}
        >
          取消
        </Button>
      </>
    ),
  });
  addDialogRef(dialogRef);
}
