import React, { useState, useEffect } from 'react';
import { Box, Button, DatePicker2, Input, Select, Table, Pagination, Message } from '@alifd/next';
import { activityDataGetLotteryRecordPage, activityDataExportLotteryRecordExport } from '@/api/b';
import { downloadExcel, maskSensitiveInfo } from '@/utils';
import dayjs from 'dayjs';
import { Auth } from '@/components/Auth';
import usePagination from '@/hooks/usePagination';

export default function AwardRecord({ record }) {
  const { activityId } = record;

  // 查询条件
  const [userNick, setUserNick] = useState<any>('');
  const [ouid, setOuid] = useState<any>('');
  const [timeRange, setTimeRange] = useState<any>([]);
  const [receiveResult, setReceiveResult] = useState<any>('');

  // 表格相关
  const [tableData, setTableData] = useState<any>([]);
  const [loading, setLoading] = useState(false);

  // 使用分页Hook
  const pagination = usePagination(1, 10, (current, size) => {
    if (activityId) {
      fetchData({
        pageNum: current,
        pageSize: size,
      });
    }
  });

  useEffect(() => {
    if (activityId) fetchData();
  }, [activityId]);

  const fetchData = async (params?: any) => {
    const [start, end] = timeRange;
    setLoading(true);
    try {
      const res: any = await activityDataGetLotteryRecordPage({
        activityId,
        userNick: userNick.trim(),
        ouid: ouid.trim(),
        receiveTimeStart: start ? dayjs(start).format('YYYY-MM-DD 00:00:00') : '',
        receiveTimeEnd: end ? dayjs(end).format('YYYY-MM-DD 23:59:59') : '',
        receiveResult,
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        ...params,
      });
      setTableData(res?.records || []);
      pagination.setTotal(res?.total || 0);
    } catch (err) {
      console.error(err);
      Message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async () => {
    // await checkButtonPermission('activityDataExport');
    const [start, end] = timeRange;
    setLoading(true);
    try {
      const data: any = await activityDataExportLotteryRecordExport({
        activityId,
        userNick: userNick.trim(),
        ouid: ouid.trim(),
        receiveTimeStart: start ? dayjs(start).format('YYYY-MM-DD 00:00:00') : '',
        receiveTimeEnd: end ? dayjs(end).format('YYYY-MM-DD 23:59:59') : '',
        receiveResult,
      } as any);
      downloadExcel(data, `领奖记录${dayjs().format('YYYY-MM-DD HH:mm')}`);
    } catch (err) {
      console.error(err);
      Message.error('导出失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    pagination.changePage(1);
  };

  const handleReset = () => {
    setUserNick('');
    setOuid('');
    setTimeRange([]);
    setReceiveResult('');
    pagination.reset();

    // 手动调用查询确保使用重置后的查询条件
    if (activityId) {
      fetchData({
        pageNum: 1,
        pageSize: 10,
        userNick: '',
        ouid: '',
        receiveTimeStart: '',
        receiveTimeEnd: '',
        receiveResult: '',
      });
    }
  };

  return (
    <Box spacing={16}>
      {/* 筛选表单 */}
      <Box
        direction="row"
        spacing={10}
        align="center"
      >
        <Input
          label="用户昵称"
          value={userNick}
          onChange={setUserNick}
        />
        <Input
          label="openId"
          value={ouid}
          onChange={setOuid}
        />
        <DatePicker2.RangePicker
          label="领取时间"
          value={timeRange}
          onChange={setTimeRange}
        />
        <Select
          label="领取状态"
          value={receiveResult}
          onChange={setReceiveResult}
        >
          <Select.Option value="">全部</Select.Option>
          <Select.Option value="1">领取成功</Select.Option>
          <Select.Option value="2">领取失败</Select.Option>
        </Select>
        <Button
          type="primary"
          onClick={handleSearch}
        >
          查询
        </Button>
        <Button
          type="normal"
          onClick={handleReset}
        >
          重置
        </Button>
        <Auth authKey={'activity_list_data_export'}>
          <Button
            type="secondary"
            onClick={handleExport}
          >
            导出
          </Button>
        </Auth>

      </Box>

      {/* 表格 */}
      <Table
        dataSource={tableData}
        hasBorder
        loading={loading}
      >
        <Table.Column
          title="用户昵称"
          dataIndex="userNick"
          cell={(value) => {
            return maskSensitiveInfo(value) || '-';
          }}
        />
        <Table.Column
          title="openId"
          dataIndex="ouid"
          cell={(value) => {
            return maskSensitiveInfo(value) || '-';
          }}
        />
        <Table.Column
          title="领取时间"
          dataIndex="createTime"
        />
        <Table.Column
          title="奖品类型"
          dataIndex="lotteryType"
        />
        <Table.Column
          title="奖品名称"
          dataIndex="lotteryName"
        />
        <Table.Column
          title="领取状态"
          dataIndex="receiveStatus"
          cell={value => {
            let className = '';
            if (value === '领取成功') {
              className = 'status-normal';
            } else if (value === '领取失败') {
              className = 'status-disabled';
            }
            return <span className={className}>{value}</span>;
          }}
        />
        <Table.Column
          title="备注"
          dataIndex="mark"
        />
      </Table>

      <Box direction="row" justify="flex-end">
        <Pagination
          {...pagination.paginationProps}
          shape={'arrow-only'}
          totalRender={total => `共 ${total} 条`}
          pageSizeSelector="dropdown"
          pageSizePosition="end"
        />
      </Box>
    </Box>
  );
}
