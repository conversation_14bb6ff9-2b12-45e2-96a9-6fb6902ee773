import Container from '@/components/Container';
import { Form, Button, Box, Input, Field } from '@alifd/next';
import { useRule } from './hook';
import { useActivity } from '../../reducer';

/**
 * 规则设置页面
 * 包含门槛设置、订单规则、奖品设置等
 */
export default function Rule({ goNextStep, goPrevStep }) {
  const { rule, updateRule } = useRule();
  // 获取全局reducer状态
  const { state } = useActivity();

  // 操作类型
  const { operationType } = state.extra;
  // 是否禁用
  const needDisable = operationType === 'view';
  // form表单
  const field = Field.useField();

  // 验证及下一步
  const handleSubmit = () => {
    // 这里可以添加验证逻辑
    field.validatePromise().then(({ errors }) => {
      if (errors) return;
      goNextStep();
    });
  };
  // 上一步
  const handleBack = () => {
    goPrevStep();
  };

  return (
    <Form className="activity-rule-form" field={field}>
      <Container title={'活动规则'}>
        <Box direction="row" justify="flex-end" align="center" spacing={10} margin={[0, 0, 10, 0]}>
          <span style={{ color: 'var(--color-text-secondary)' }}>
            提交活动前请核对活动规则
          </span>
        </Box>

        <Form.Item name="rule" label="" required requiredMessage={'请输入活动规则'}>
          <Input.TextArea
            maxLength={2000}
            disabled={needDisable}
            rows={24}
            cutString
            showLimitHint
            composition
            value={rule}
            onChange={val => updateRule(val)}
            placeholder="请输入活动规则"
          />
        </Form.Item>
      </Container>

      <Box direction="row" justify="center" spacing={16} margin={[-30, 0, 0, 0]}>
        <Button onClick={handleBack}>上一步</Button>
        <Button type="primary" style={{ width: 150 }} onClick={handleSubmit}>
          下一步：氛围定制
        </Button>
      </Box>
    </Form>
  );
}
