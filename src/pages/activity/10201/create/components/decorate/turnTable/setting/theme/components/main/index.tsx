import ImgUpload from '@/components/ImgUpload';
import { Box } from '@alifd/next';
import { ThemeState } from '../../../../type';
import ColorPickerFormItem from '@/components/ColorPickerFormItem/ColorPickerFormItem';

export default function Main(props) {
  const { tState, tDispatch } = props;
  const {
      kv,
      actBgColor,
      ruleBtn,
      ruleBtnColor,
      userInfoBg,
      userInfoColor,
      thresholdBg,
      stepBtnBg,
      stepBtnSelectBg,
      prizeListBg,
      prizeBg,
      exchangeBtn,
      skuListTitle,
      checkedSkuLine,
      unCheckedSkuLine,
      skuBg,
      bottomToTop,
  } = tState;

  const handleUpdateField = (field: keyof ThemeState, value: any) => {
    tDispatch({ type: 'UPDATE_MODULE', payload: { [field]: value } });
  };
    return (
      <Box direction="row" spacing={32}>
        <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>
          <ImgUpload
            label="活动主图"
            required
            img={{
            value: kv,
            width: 750,
          }}
            onSuccess={(url: string) => handleUpdateField('kv', url)}
            onReset={() => handleUpdateField('kv', -1)}
          />
          <ColorPickerFormItem
            label="页面背景颜色"
            color={{ value: actBgColor }}
            onSetColor={(color: string) => handleUpdateField('actBgColor', color)}
            onReset={() => handleUpdateField('actBgColor', -1)}
          />
          <ImgUpload
            label="右上角按钮背景图"
            required
            img={{
              value: ruleBtn,
              width: 105,
              height: 45,
            }}
            onSuccess={(url: string) => handleUpdateField('ruleBtn', url)}
            onReset={() => handleUpdateField('ruleBtn', -1)}
          />
          <ColorPickerFormItem
            label="按钮文字颜色"
            color={{ value: ruleBtnColor }}
            onSetColor={(color: string) => handleUpdateField('ruleBtnColor', color)}
            onReset={() => handleUpdateField('ruleBtnColor', -1)}
          />
        </Box>
        <span />
        <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>
          <ImgUpload
            label="用户信息背景"
            required
            img={{
                  value: userInfoBg,
                  width: 690,
                  height: 180,
              }}
            onSuccess={(url: string) => handleUpdateField('userInfoBg', url)}
            onReset={() => handleUpdateField('userInfoBg', -1)}
          />
          <ColorPickerFormItem
            label="用户信息字体颜色"
            color={{ value: userInfoColor }}
            onSetColor={(color: string) => handleUpdateField('userInfoColor', color)}
            onReset={() => handleUpdateField('userInfoColor', -1)}
          />
          <ImgUpload
            label="选择兑换门槛背景图"
            required
            img={{
                  value: thresholdBg,
                  width: 187,
                  height: 85,
              }}
            onSuccess={(url: string) => handleUpdateField('thresholdBg', url)}
            onReset={() => handleUpdateField('thresholdBg', -1)}
          />
          <ImgUpload
            label="门槛未选中按钮背景图"
            required
            img={{
            value: stepBtnBg,
            width: 244,
            height: 75,
          }}
            onSuccess={(url: string) => handleUpdateField('stepBtnBg', url)}
            onReset={() => handleUpdateField('stepBtnBg', -1)}
          />
          <ImgUpload
            label="门槛选中按钮背景图"
            required
            img={{
                  value: stepBtnSelectBg,
                  width: 245,
                  height: 75,
              }}
            onSuccess={(url: string) => handleUpdateField('stepBtnSelectBg', url)}
            onReset={() => handleUpdateField('stepBtnSelectBg', -1)}
          />
        </Box>
        <span />
        <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>
          <ImgUpload
            label="兑礼区域背景图"
            required
            img={{
            value: prizeListBg,
            width: 731,
            height: 514,
          }}
            onSuccess={(url: string) => handleUpdateField('prizeListBg', url)}
            onReset={() => handleUpdateField('prizeListBg', -1)}
          />
          <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>
            <ImgUpload
              label="单个奖品区背景图"
              required
              img={{
                      value: prizeBg,
                      width: 619,
                      height: 324,
                  }}
              onSuccess={(url: string) => handleUpdateField('prizeBg', url)}
              onReset={() => handleUpdateField('prizeBg', -1)}
            />
          </Box>
          <ImgUpload
            label="兑换按钮背景"
            required
            img={{
            value: exchangeBtn,
            width: 252,
            height: 74,
          }}
            onSuccess={(url: string) => handleUpdateField('exchangeBtn', url)}
            onReset={() => handleUpdateField('exchangeBtn', -1)}
          />
        </Box>
        <span />
        <Box direction="column" spacing={16} margin={[16, 0, 0, 0]}>
          <ImgUpload
            label="商品列表标题"
            required
            img={{
                    value: skuListTitle,
                    width: 731,
                    height: 234,
                }}
            onSuccess={(url: string) => handleUpdateField('skuListTitle', url)}
            onReset={() => handleUpdateField('skuListTitle', -1)}
          />
          <ImgUpload
            label="选中商品系列按钮背景图"
            required
            img={{
                  value: checkedSkuLine,
                  width: 198,
                  height: 75,
              }}
            onSuccess={(url: string) => handleUpdateField('checkedSkuLine', url)}
            onReset={() => handleUpdateField('checkedSkuLine', -1)}
          />
          <ImgUpload
            label="未选中商品系列按钮背景图"
            required
            img={{
                  value: unCheckedSkuLine,
                  width: 198,
                  height: 75,
              }}
            onSuccess={(url: string) => handleUpdateField('unCheckedSkuLine', url)}
            onReset={() => handleUpdateField('unCheckedSkuLine', -1)}
          />
          <ImgUpload
            label="商品背景图"
            required
            img={{
                  value: skuBg,
                  width: 342,
                  height: 425,
              }}
            onSuccess={(url: string) => handleUpdateField('skuBg', url)}
            onReset={() => handleUpdateField('skuBg', -1)}
          />
        </Box>
      </Box>
    );
}