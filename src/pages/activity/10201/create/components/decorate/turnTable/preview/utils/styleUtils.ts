import { CSSProperties } from 'react';


// 处理图片路径：转换为完整URL格式
const normalizeImageProp = (path?: string) => {
  return path ? `url("${path.replace(/^url\(['"]?|['"]?\)$/g, '')}")` : undefined;
};

export const createTurnTableMainStyles = (main: any) => ({
  '--ruleBtnColor': main.ruleBtnColor,
  '--ruleBtn': normalizeImageProp(main.ruleBtn),
  '--actBgColor': main.actBgColor,
  '--userInfoBg': normalizeImageProp(main.userInfoBg),
  '--userInfoColor': main.userInfoColor,
  '--thresholdBg': normalizeImageProp(main.thresholdBg),
  '--stepBtnBg': normalizeImageProp(main.stepBtnBg),
  '--stepBtnSelectBg': normalizeImageProp(main.stepBtnSelectBg),
  '--prizeListBg': normalizeImageProp(main.prizeListBg),
  '--exchangeBtn': normalizeImageProp(main.exchangeBtn),
  '--skuListTitle': normalizeImageProp(main.skuListTitle),
  '--checkedSkuLine': main.checkedSkuLine,
  '--unCheckedSkuLine': main.unCheckedSkuLine,
  '--skuBg': normalizeImageProp(main.skuBg),
  '--bottomToTop': normalizeImageProp(main.bottomToTop),
  background: main.actBgColor,
} as CSSProperties);

export const scrollToTop = () => {
  const phonePreviewContainer = document.querySelector('.phone-preview-content');
  if (phonePreviewContainer) {
    phonePreviewContainer.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }
};