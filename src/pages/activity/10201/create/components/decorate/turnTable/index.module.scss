.collapse {
  position: relative;
  width: 1px;
  margin: 0 20px;
  background-color: #eee;

  .icon {
    position: absolute;
    top: 150px;
    left: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: #fff;
    border: 1px solid #eee;
    border-radius: 50%;
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 10%);
    transform: translate(-50%, -50%);
    cursor: pointer;
    transition: transform 200ms ease;

    // 当切换时旋转180度
    &.rotate {
      transform: rotate(180deg) translate(-50%, -50%);
    }

    img {
      width: 45%;
      margin-left: 2px;
      transition: all 200ms;
    }
  }
}