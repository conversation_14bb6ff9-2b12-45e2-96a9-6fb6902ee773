// export interface ThemeState {
//   current: 'main';
//   main: MainState;
// }

export interface ThemeState {
  kv: string;
  actBg: string;
  actBgColor: string;
  ruleBtn: string;
  ruleBtnColor: string;
  userInfoBg: string;
  userInfoColor: string;
  thresholdBg: string;
  stepBtnBg: string;
  stepBtnSelectBg: string;
  prizeListBg: string;
  prizeBg: string;
  exchangeBtn: string;
  skuListTitle: string;
  checkedSkuLine: string;
  unCheckedSkuLine: string;
  skuBg: string;
  bottomToTop: string;
}

type ThemeActionType = 'UPDATE_MODULE' | 'INIT_MODULE';

// 动作类型定义
export type UpdateModuleAction = {
  type: ThemeActionType;
  payload?: Partial<ThemeState>;
};

export type ThemeAction = UpdateModuleAction;