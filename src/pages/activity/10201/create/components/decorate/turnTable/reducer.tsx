import {
  ThemeState,
  ThemeAction,
} from './type';

// 原始默认主题状态，保持不变
const originalDefaultThemeState: ThemeState = {
  kv: 'https://img10.360buyimg.com/imgzone/jfs/t1/318402/25/21151/243798/68917421Fce0adf25/0d12f7780d85d0d8.png',
  actBg: '',
  actBgColor: '#ba1a29',
  ruleBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/328413/37/1037/3574/6891741fFf3eff195/691c2cd031c0e3cb.png',
  ruleBtnColor: '#bc1b2b',
  userInfoBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/229221/40/36028/194489/68917462Fc3d172e9/60d6afd08af586cb.png',
  userInfoColor: '#df1527',
  thresholdBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/287946/40/19969/57632/6891a76aFa361aa04/a28bd6bbbc8c02d5.png',
  stepBtnBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/319658/26/25486/3449/689daf0fF630d3d34/c4db5578aca3d28c.png',
  stepBtnSelectBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/320341/30/24771/3485/689dae99F4ef05ed7/a0ea078ee558af0c.png',
  prizeListBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/286380/4/18491/17863/68946189Fe23f2ba4/1bee82488aa27014.png',
  prizeBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/314180/20/23471/52330/68945d49F7e61dc5c/49ede3570e6dd39c.png',
  exchangeBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/313351/12/23063/3650/68917463F5036e1f6/d332c96def4b0813.png',
  skuListTitle: 'https://img10.360buyimg.com/imgzone/jfs/t1/309618/31/23130/160992/68917465F64b1bc4d/bdbc3e9f0db364bb.png',
  checkedSkuLine: 'https://img10.360buyimg.com/imgzone/jfs/t1/327024/19/1049/3354/68917461Fdf6ee685/f89b5816af1b7a01.png',
  unCheckedSkuLine: 'https://img10.360buyimg.com/imgzone/jfs/t1/325240/15/1056/3549/68917467Fa672d8ab/ee2a6d62526043e9.png',
  skuBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/287085/7/22882/32549/68917463F1493f2a0/a2119a05baba95f6.png',
  bottomToTop: 'https://img10.360buyimg.com/imgzone/jfs/t1/291432/25/23811/53634/68917467F198bde89/5bc08a9d08d8bc57.png',
};

// 导出的默认主题状态，用于重置
export let defaultThemeState: ThemeState = {
  ...originalDefaultThemeState,
};

// 移除updateDefaultTheme函数，因为它会破坏重置功能

export const themeState: ThemeState = {
  ...defaultThemeState,
};
// 主题Reducer
export function themeReducer(state: ThemeState, action: ThemeAction): ThemeState {
  switch (action.type) {
    case 'UPDATE_MODULE': {
      const processedPayload = { ...action.payload };

      // 遍历payload中的每个属性
      Object.keys(processedPayload).forEach((key) => {
        const defaultValue = originalDefaultThemeState[key];
        switch (processedPayload[key]) {
          // 重置为默认值
          case -1:
            processedPayload[key] = defaultValue;
            break;
          default:
            break;
        }
      });
      return {
        ...state,
        ...processedPayload,
      };
    }
    case 'INIT_MODULE': {
      return {
        ...state,
        ...action.payload,
      };
    }
    default:
      return state;
  }
}