import { AppState } from '@/pages/activity/10201/create/type';

/**
 * 验证分享
 * @param state 全局状态
 * @returns 错误信息数组
 */
export function validateShare(state: AppState): string[] {
  const errors: string[] = [];
  const { share } = state;
  if (!share.shareTitle) {
    errors.push('请输入分享标题');
  }
  if (!share.shareContent) {
    errors.push('请输入分享内容');
  }
  if (!share.mpImg) {
    errors.push('请上传分享图片');
  }
  return errors;
}

