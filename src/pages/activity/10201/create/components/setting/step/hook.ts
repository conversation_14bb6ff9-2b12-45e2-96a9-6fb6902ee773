import { useActivity } from '@/pages/activity/10201/create/reducer';
import { validateStep } from './validator';
import { StepState, ModuleType } from '@/pages/activity/10201/create/type';


/**
 *  阶梯设置的自定义Hook
 */
export function useStep() {
  const { state, dispatch } = useActivity();

  // 更新中奖限制并自动重新验证
  const updateStep = (data?: Partial<StepState>) => {
    if (!data) return;
    // 先更新数据
    dispatch({
      type: 'UPDATE_STEP',
      payload: data,
    });

    // 获取更新后的数据
    const updatedStateInfo = {
      ...state,
      step: {
        ...state.step,
        ...data,
      },
    };

    // 重新验证并更新错误状态
    const errors = validateStep(updatedStateInfo);
    dispatch({
      type: 'VALIDATE_MODULE',
      module: ModuleType.STEP,
      payload: errors,
    });
  };

  return {
    step: state.step,
    errors: state.errors[ModuleType.STEP] || [],
    updateStep,
    dispatch,
  };
}
