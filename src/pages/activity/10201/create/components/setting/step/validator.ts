import { AppState } from '@/pages/activity/10201/create/type';

/**
 * 验证是否限制活动内总兑换次数设置
 * @param state
 */
export function validateStep(state: AppState): string[] {
    const errors: string[] = [];
    const { step, prizeAndSku } = state;
    // actLimitActTotalReceiveCount是否限制活动内总兑换次数 0 不限制 1 限制
    if (step.actLimitActTotalReceiveCount === 1 && step.actTotalReceiveCount <= 0) {
        errors.push('请输入活动内总兑换次数');
    }
    if (!step.fileList.length) {
        errors.push('请上传订单商品系列数据');
    }
    return errors;
}

