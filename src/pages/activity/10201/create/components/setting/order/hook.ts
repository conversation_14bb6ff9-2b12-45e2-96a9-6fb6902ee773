import { useActivity } from '@/pages/activity/10201/create/reducer';
import { validateOrder } from './validator';
import { OrderState, ModuleType } from '@/pages/activity/10201/create/type';


/**
 *  订单规则模块的自定义Hook
 *  提供对订单规则状态的访问和更新方法
 */
export function useOrder() {
  const { state, dispatch } = useActivity();

  // 更新中奖限制并自动重新验证
  const updateOrder = (data?: Partial<OrderState>) => {
    if (!data) return;
    // 先更新数据
    dispatch({
      type: 'UPDATE_ORDER',
      payload: data,
    });

    // 获取更新后的数据
    const updatedStateInfo = {
      ...state,
      order: {
        ...state.order,
        ...data,
      },
    };

    // 重新验证并更新错误状态
    const errors = validateOrder(updatedStateInfo);
    dispatch({
      type: 'VALIDATE_MODULE',
      module: ModuleType.ORDER,
      payload: errors,
    });
  };

  return {
    order: state.order,
    errors: state.errors[ModuleType.ORDER] || [],
    updateOrder,
    dispatch,
  };
}
