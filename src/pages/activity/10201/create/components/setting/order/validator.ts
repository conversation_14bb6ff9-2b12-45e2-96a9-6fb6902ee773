import { AppState } from '@/pages/activity/10201/create/type';

/**
 * 验证订单设置
 * @param state
 */
export function validateOrder(state: AppState): string[] {
    const errors: string[] = [];
    const { order, base } = state;
    if (!order.orderStartTime) {
        errors.push('订单开始时间不能为空');
    }
    if (!order.orderEndTime) {
        errors.push('订单结束时间不能为空');
    }
    if (order.orderEndTime > base.endTime) {
        errors.push('订单结束时间不能晚于活动结束时间');
    }
    return errors;
}

