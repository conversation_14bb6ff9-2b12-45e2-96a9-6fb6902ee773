import Container from '@/components/Container';
import { useOrder } from './hook';
import { DatePicker2, Form, Radio } from '@alifd/next';
import dayjs from 'dayjs';
import NumberInput from '@/components/NumberInput/NumberInput';

export default function Order() {
  const { order, updateOrder } = useOrder();

  // 手动选择日期区间
  const handleDateChange = (value: any) => {
    if (value && value.length === 2) {
      updateOrder({
        orderStartTime: dayjs(value[0]).format('YYYY-MM-DD HH:mm:ss'),
        orderEndTime: dayjs(value[1]).format('YYYY-MM-DD HH:mm:ss'),
      });
    }
  };

  return (
    <Container title="订单限制" style={{ marginBottom: 10 }}>
      <Form.Item
        label="订单限制"
        required
      >
        <Radio.Group value={order.limitOrder} onChange={(value: number) => updateOrder({ limitOrder: value })}>
          <Radio value={1}>限制</Radio>
          {/* <Radio value={2}>不限制</Radio> */}
        </Radio.Group>
      </Form.Item>
      <Form.Item label="订单时间" required>
        <DatePicker2.RangePicker
          showTime
          value={order.orderStartTime && order.orderEndTime ? [order.orderStartTime, order.orderEndTime] : undefined}
          onChange={handleDateChange}
          hasClear={false}
          format="YYYY-MM-DD HH:mm:ss"
          outputFormat="YYYY-MM-DD HH:mm:ss"
          timePanelProps={{ defaultValue: ['00:00:00', '23:59:59'] }}
          disabledDate={() => {
            // 订单开始时间可以选择之前时间，不再限制在活动时间范围内
            // 让用户可以自由选择订单时间范围
            return false;
          }}
          style={{ width: 'var(--activity-form-item-width)' }}
        // disabled={[(isEdit || isView) && unStart, isView]}
        />
      </Form.Item>
      <Form.Item label="是否延迟发奖" required>
        <Radio.Group value={order.isAwardDays} onChange={(value: number) => updateOrder({ isAwardDays: value })}>
          <Radio value={1}>延迟发奖</Radio>
          <Radio value={0}>不延迟发奖</Radio>
        </Radio.Group>
      </Form.Item>
      {
            order.isAwardDays === 1 && (
            <Form.Item label="延迟发放天数" required>
              <NumberInput
                name={'awardDays'}
                min={1}
                max={7}
                step={1}
                type="inline"
                value={order.awardDays}
                onChange={value => updateOrder({ awardDays: Number(value) })}
              />{' '}天
            </Form.Item>
            )
        }
    </Container>
  );
}