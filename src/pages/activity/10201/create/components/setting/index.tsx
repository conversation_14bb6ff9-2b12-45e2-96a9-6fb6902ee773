import { Form, Button, Box } from '@alifd/next';
import Base from './base';
import { useActivity } from '../../reducer';
import { ModuleType } from '@/pages/activity/10201/create/type';
import { validateBaseInfo } from './base/validator';
import { showErrorMessageDialog } from '@/utils';
import { validateThreshold } from './threshold/validator';
import Threshold from './threshold';
import { validateOrder } from './order/validator';
import { validateStep } from './step/validator';
import { validatePrizeAndSku } from './prizeAndSku/validator';
import { ACTIVITY_STATUS } from '@/utils/constant';
import Order from './order';
import Step from './step';
import PrizeAndSku from './prizeAndSku';

// 定义一个统一的验证器接口
interface Validator {
  module: ModuleType;
  getErrors: () => string[];
}

export default function Setting({ goNextStep }) {
  // 使用根级别的状态
  const { state, dispatch } = useActivity();
  const { operationType, activityStatus } = state.extra;

  const isEdit = operationType === 'edit';
  const isView = operationType === 'view';
  const notStart = activityStatus !== ACTIVITY_STATUS.NOT_STARTED;
  const needDisable = (isEdit || isView) && notStart;

  // 创建验证器列表
  const validators: Validator[] = [
    { module: ModuleType.BASE, getErrors: () => validateBaseInfo(state) },
    { module: ModuleType.THRESHOLD, getErrors: () => validateThreshold(state) },
    { module: ModuleType.ORDER, getErrors: () => validateOrder(state) },
    { module: ModuleType.STEP, getErrors: () => validateStep(state) },
    { module: ModuleType.PRIZE_AND_SKU, getErrors: () => validatePrizeAndSku(state) },
    // 可以在此处添加更多模块的验证器
  ];

  // 统一的验证函数，返回验证结果(是否通过)
  const validateModules = () => {
    let isValid = true;
    const allErrors: string[] = [];
    // 遍历所有验证器获取错误状态
    validators.forEach((validator) => {
      const errors = validator.getErrors();
      dispatch({
        type: 'VALIDATE_MODULE',
        module: validator.module,
        payload: errors,
      });
      allErrors.push(...errors);
      // 如果有错误，则验证不通过
      if (errors.length > 0) {
        isValid = false;
      }
    });
    if (!isValid) {
      showErrorMessageDialog(allErrors);
    }
    return isValid;
  };

  const handleSubmit = () => {
    // 验证所有模块
    const isValid = validateModules();
    // 如果验证通过，进入下一步
    console.log(state, '给zzp看的数据');
    isValid && goNextStep();
  };

  return (
    <Form disabled={needDisable} className="activity-setting">
      <Base />
      <Threshold />
      <Order />
      <Step />
      {!!state.prizeAndSku.seriesList[0].seriesSkuList.length && (
        <PrizeAndSku />
      )}
      <Box
        direction="row"
        justify="center"
        style={{
          position: 'fixed',
          bottom: 22,
          left: '50%',
          transform: 'translateX(calc(-50% + 110px))',
        }}
      >
        <Button style={{ width: 150 }} type="primary" onClick={handleSubmit}>
          下一步：规则设置
        </Button>
      </Box>
    </Form>
  );
}
