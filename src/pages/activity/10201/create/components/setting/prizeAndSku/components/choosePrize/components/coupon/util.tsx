import { Dialog } from '@alifd/next';
import React from 'react';
import Coupon from './index';
import { addDialogRef } from '@/utils/dialogMapper';

export default function chooseCoupon({ record, activityType, status }) {
  return new Promise(resolve => {
    const dialogRef = Dialog.show({
      v2: true,
      title: '编辑优惠券',
      width: 600,
      centered: true,
      closeMode: ['close'],
      content: (
        <Coupon
          record={record}
          onSelectPrize={result => {
            resolve(result); // result为表单数据
            dialogRef.hide();
          }}
          activityType={activityType}
          status={status}
        />
      ),
      footer: false,
    });
    addDialogRef(dialogRef);
  });
}
