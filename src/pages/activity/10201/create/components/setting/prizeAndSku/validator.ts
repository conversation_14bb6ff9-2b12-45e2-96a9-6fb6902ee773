import { AppState } from '@/pages/activity/10201/create/type';
import { ACTIVITY_STATUS } from '@/utils/constant';

/**
 * 验证奖品设置
 * @param state 奖品设置数据
 * @returns 错误信息数组
 */
export function validatePrizeAndSku(state: AppState): string[] {
    const errors: string[] = [];
    const { prizeAndSku } = state;
    if (prizeAndSku.seriesList[0].stepList.length === 0) {
        errors.push('请至少添加1个奖品');
    }

    const { stepList } = prizeAndSku.seriesList[0];

    // 验证阶梯罐数递增
    for (let i = 1; i < stepList.length; i++) {
        if (stepList[i].tankNum <= stepList[i - 1].tankNum) {
            errors.push(`第${i + 1}个阶梯罐数必须大于第${i}个阶梯罐数，请确保阶梯罐数递增`);
        }
    }

    stepList.forEach((step, index) => {
        if (step.stepName === '') {
            errors.push(`第${index + 1}个阶梯名称不能为空`);
            return;
        }
        if (step.tankNum <= 0) {
            errors.push(`第${index + 1}个阶梯罐数不能小于1`);
            return;
        }
        if (step.prizeList.length === 0) {
            errors.push(`第${index + 1}个阶梯奖品不能为空`);
            return;
        }

        // 验证阶梯下不能只有全是下架的奖品（编辑活动时）
        const { operationType } = state.extra || {};
        const isEditingRunningActivity = operationType === 'edit';

        if (isEditingRunningActivity && step.prizeList.length > 0) {
            const allPrizesOffShelf = step.prizeList.every(prize => prize.status === 0);
            if (allPrizesOffShelf) {
                errors.push(`第${index + 1}个阶梯下不能只有全是下架的奖品，请至少保留一个上架奖品`);
            }
        }

        step.prizeList.forEach((prize, prizeIndex) => {
            if (prize.lotteryName === '' || prize.lotteryName === '') {
                errors.push(`第${index + 1}个阶梯第${prizeIndex + 1}个奖品不能为空`);
            }
        });
    });
    return errors;
}

