import { useActivity } from '@/pages/activity/10201/create/reducer';
import { validatePrizeAndSku } from './validator';
import { PrizeAndSkuState, ModuleType } from '@/pages/activity/10201/create/type';


/**
 * 系列sku以及奖品设置模块的自定义Hook
 * 提供对系列sku以及奖品设置状态的访问和更新方法
 */
export function usePrizeAndSku() {
  const { state, dispatch } = useActivity();

  // 更新系例sku等并自动重新验证
  const updatePrizeAndSku = (data?: Partial<PrizeAndSkuState>) => {
    if (!data) return;
    // 先更新数据
    dispatch({
      type: 'UPDATE_PRIZE_AND_SKU',
      payload: data,
    });

    // 获取更新后的数据
    const updatedStateInfo = {
      ...state,
      prizeAndSku: {
        ...state.prizeAndSku,
        ...data,
      },
    };

    // 重新验证并更新错误状态
    const errors = validatePrizeAndSku(updatedStateInfo);
    dispatch({
      type: 'VALIDATE_MODULE',
      module: ModuleType.PRIZE_AND_SKU,
      payload: errors,
    });
  };

  return {
    prizeAndSku: state.prizeAndSku,
    errors: state.errors[ModuleType.PRIZE_AND_SKU] || [],
    updatePrizeAndSku,
    dispatch,
  };
}
