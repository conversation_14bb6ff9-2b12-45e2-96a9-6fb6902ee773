import { Box, Form, Field, Input, Button, DatePicker2, Message, Radio, Divider } from '@alifd/next';
import ImgUploadFromItem from '@/components/ImgUpload';
import NumberInput from '@/components/NumberInput/NumberInput';
import { PrizeTypeEnum, getPrizeTypeLabel } from '@/utils';
import { ACTIVITY_STATUS } from '@/utils/constant';

/*
  选择实物奖品-编辑奖品弹窗 - 10201活动专用
*/
export default function ChoosePhysicalGoodDialogContent({
  record,
  prizeType,
  onResolve,
  field,
  activityType,
  status,
}: any) {
  const [activityStatus, operationType] = status || [];
  const AF_Act_Not_Start = field && field.getValue('AF_Act_Not_Start') || activityStatus !== 1;

  const isEdit = operationType === 'edit';
  const isView = operationType === 'view';
  const isEnded = activityStatus === ACTIVITY_STATUS.ENDED;

  // 10201活动专用：只有在查看模式时才禁用
  const needDisable = isView;

  // console.log('AF_Act_Not_Start', AF_Act_Not_Start, activityStatus);
  const physicalGoodField = Field.useField({
    values: {
      // 奖品图（可编辑）
      showImage: record.showImage ?? record.skuMainPicture ?? '',
      // 奖品名称（可编辑）
      lotteryName: record.lotteryName ?? record.benefitName ?? '',
      // 发放份数（用户手填）
      prizeNum: typeof record.prizeNum === 'number' ? record.prizeNum : 1,
      // 发货时间范围（[startTime, endTime]）
      deliveryTimeRange: [record.deliveryStartTime || '', record.deliveryEndTime || ''],
      // 备注
      remark: record.remark || '',
      // 中奖概率
      probability: record.probability || 0,
      // 每日限额 1 限制 2 不限制
      dayLimitType: record.dayLimitType || 2,
      // 每日限额份数
      dayLimitCount: record.dayLimitCount || 1,
      // 中奖限制
      awardLimitType: record.awardLimitType || 2,
      // 中奖限制份数
      awardLimitCount: record.awardLimitCount || 1,
    },
  });

  // 点击"返回列表"，关闭弹窗
  const handleCancel = () => {
    onResolve(null); // 返回 null 表示放弃编辑
  };


  // 点击"保存"，进行校验后返回修改后的奖品信息，并关闭弹窗
  const handleSave = () => {
    physicalGoodField.validate((errors, values: any) => {
      if (errors) return;
      const { deliveryTimeRange = [] } = values;
      // 进行中的活动编辑
      if (isEdit) {
        if (values.prizeNum > Number(record.remainPrizeQuantity)) {
          Message.error(`发放份数不能超过可用库存 ${Number(record.remainPrizeQuantity)}`);
          return;
        }
      }

      // 校验每日限额不得超过发放份数
      if (
          values.dayLimitType === 1 &&
          values.dayLimitCount >
          values.prizeNum) {
        Message.error(`每日限额不能超过发放份数 ${values.prizeNum}`);
        return;
      }

      // 校验每人最多抽中次数不得超过发放份数
      if (values.awardLimitType === 1 && values.awardLimitCount > values.prizeNum) {
        Message.error(`每人最多抽中次数不能超过发放份数 ${values.prizeNum}`);
        return;
      }
      const [deliveryStartTime, deliveryEndTime] = deliveryTimeRange;
      const result = {
        showImage: values.showImage,
        lotteryName: values.lotteryName,
        lotteryType: prizeType,
        lotteryValue: record.skuCode,
        price: record.price ?? record.amount ?? '',
        prizeNum: values.prizeNum,
        deliveryStartTime: deliveryStartTime || '',
        deliveryEndTime: deliveryEndTime || '',
        remark: values.remark,
        probability: values.probability,
        dayLimitType: values.dayLimitType,
        dayLimitCount: values.dayLimitCount,
        awardLimitType: values.awardLimitType,
        awardLimitCount: values.awardLimitCount,
        amount: record.amount,
      };
      // 返回修改后的奖品信息，并关闭弹窗
      onResolve(result);
    });
  };

  return (
    <Form
      field={physicalGoodField}
      className="edit-prize-form"
    >
      {/* 奖品图 */}
      <ImgUploadFromItem
        name="showImage"
        label="奖品图"
        required
        requiredMessage="请上传奖品图"
        disabled={needDisable}
        img={{
          value: physicalGoodField.getValue('showImage'),
          width: 500,
          height: 500,
        }}
        onSuccess={(url: string) => {
          physicalGoodField.setValue('showImage', url);
        }}
        onReset={() => physicalGoodField.setValue('showImage', '')}
      />
      {/* 奖品类型 (只读) */}
      <Form.Item label="奖品类型">
        <Input
          value={getPrizeTypeLabel(prizeType)}
          disabled
          readOnly
        />
      </Form.Item>
      {/* 实物名称 (只读) */}
      <Form.Item label="实物名称">
        <Input
          value={record.benefitName}
          disabled
          readOnly
        />
      </Form.Item>
      {/* 奖品价值 (只读) */}
      <Form.Item label="奖品价值">
        <Input
          value={`${record.price ?? record.amount ?? ''}元`}
          disabled
          readOnly
        />
      </Form.Item>
      {/* 领取限制 (固定为"每人限领1次"，只读) */}
      <Form.Item label="领取限制">
        <Input
          value="每人限领1次"
          disabled
          readOnly
        />
      </Form.Item>
      {/* 奖品名称 (可编辑) */}
      <Form.Item
        name="lotteryName"
        label="奖品名称"
        required
        requiredMessage="请输入奖品名称"
        pattern="^[\u4E00-\u9FA5A-Za-z0-9_\-/.]+$"
        patternMessage="不可输入特殊符号，支持中文、英文、数字及下划线"
        disabled={needDisable}
      >
        <Input
          maxLength={16}
          trim
          composition
          showLimitHint
          disabled={needDisable}
          value={physicalGoodField.getValue('lotteryName')}
          onChange={(val) => physicalGoodField.setValue('lotteryName', val)}
        />
      </Form.Item>
      {/* 发放份数 (可编辑) */}
      <Form.Item
        name="prizeNum"
        label="发放份数"
        required
        requiredMessage="请输入发放份数"
        disabled={needDisable}
        extra={
          <div className="form-extra">
            <>
              当前可用库存：<div style={{ color: 'var(--base-red)' }}>{record.remainPrizeQuantity}</div>
            </>
          </div>
        }
      >
        <NumberInput
          min={1}
          max={record.remainPrizeQuantity}
          disabled={needDisable}
          value={physicalGoodField.getValue('prizeNum')}
          onChange={(val) => physicalGoodField.setValue('prizeNum', val)}
          style={{ width: '100%' }}
        />
      </Form.Item>
      {/* 发货时间 (可编辑) */}
      {prizeType === PrizeTypeEnum.PRACTICALITY.value && (
        <Form.Item
          name="deliveryTimeRange"
          label="发货时间"
          disabled={needDisable}
          extra={<p className="form-extra">对外展示的预估发货时间，不代表实际发货时间</p>}
        >
          <DatePicker2.RangePicker
            disabled={needDisable}
            value={physicalGoodField.getValue('deliveryTimeRange')}
            onChange={(val) => physicalGoodField.setValue('deliveryTimeRange', val)}
            showTime
            format="YYYY-MM-DD HH:mm:ss"
            outputFormat="YYYY-MM-DD HH:mm:ss"
            timePanelProps={{ defaultValue: ['00:00:00', '23:59:59'] }}
            style={{ width: '100%' }}
          />
        </Form.Item>
      )}
      {prizeType === PrizeTypeEnum.PRACTICALITY_BY_ORDER.value && (
        <Form.Item
          label="订单备注"
          required
          requiredMessage="请输入订单备注"
          disabled={needDisable}
          extra={
            <Box
              className="form-extra"
              spacing={3}
            >
              <div>提示：活动创建后，备注内容前后将自动生成超级复购前缀标识。</div>
              <div>例：【超级复购-备注内容】</div>
            </Box>
          }
          style={{ marginTop: -20 }}
        >
          <p className="form-extra">完成任务获得奖品后，自动备注到最新支付的一笔未发货订单</p>
          <Input.TextArea
            name="remark"
            maxLength={200}
            rows={6}
            cutString
            showLimitHint
            composition
            disabled={needDisable}
            value={physicalGoodField.getValue('remark')}
            onChange={(val) => physicalGoodField.setValue('remark', val)}
            placeholder="请输入需要备注到订单的内容"
          />
        </Form.Item>
      )}
      {/* 底部按钮 */}
      {
        activityType === 'wheel' && (
          <>
            <Divider />
            <Form.Item
              label="中奖概率"
              required
              disabled={isView || isEnded}
              name={'probability'}
              validator={(_, value: number) => {
                if (value <= 0) {
                  return Promise.reject('请设置中奖概率');
                }
                return Promise.resolve();
              }}
            >
              <NumberInput
                name="probability"
                precision={3}
                style={{ width: 150 }}
                min={0.001}
                max={99.999}
                innerAfter={<span>%</span>}
                maxLength={7}
                disabled={isView || isEnded}
                value={physicalGoodField.getValue('probability')}
                onChange={(val) => physicalGoodField.setValue('probability', val)}
              />
              <div className="form-extra">所有奖品中奖概率之和不可超过99.999%</div>
            </Form.Item>
            <Form.Item label="每日限额" required requiredMessage="请选择每日限额" disabled={needDisable}>
              <Radio.Group
                disabled={needDisable}
                value={physicalGoodField.getValue('dayLimitType')}
                onChange={(val) => physicalGoodField.setValue('dayLimitType', val)}
              >
                <Radio value={1}>限制</Radio>
                <Radio value={2}>不限制</Radio>
              </Radio.Group>
            </Form.Item>
            {
              physicalGoodField.getValue('dayLimitType') === 1 && (
                <Form.Item label=" " disabled={needDisable}>
                  <NumberInput
                    style={{ width: 150, marginRight: 6 }}
                    min={1}
                    disabled={needDisable}
                    value={physicalGoodField.getValue('dayLimitCount')}
                    onChange={(val) => physicalGoodField.setValue('dayLimitCount', val)}
                    placeholder="请输入每日限额份数"
                  />份
                </Form.Item>
              )
            }
            <Form.Item label="中奖限制" required requiredMessage="请选择中奖限制" disabled={needDisable}>
              <Radio.Group
                disabled={needDisable}
                value={physicalGoodField.getValue('awardLimitType')}
                onChange={(val) => physicalGoodField.setValue('awardLimitType', val)}
              >
                <Radio value={1}>限制</Radio>
                <Radio value={2}>不限制</Radio>
              </Radio.Group>

            </Form.Item>
            {
              physicalGoodField.getValue('awardLimitType') === 1 && (
                <Form.Item label=" " disabled={needDisable}>
                  每人最多抽中本奖品
                  <NumberInput
                    style={{ width: 150, margin: '0 6px' }}
                    min={1}
                    disabled={needDisable}
                    value={physicalGoodField.getValue('awardLimitCount')}
                    onChange={(val) => physicalGoodField.setValue('awardLimitCount', val)}
                    placeholder="请输入中奖限制份数"
                  />次
                </Form.Item>
              )
            }

          </>
        )
      }
      <Box
        direction="row"
        justify="center"
        spacing={16}
        margin={[20, 0, 0, 0]}
      >
        {
          !AF_Act_Not_Start && <Button onClick={handleCancel}>返回列表</Button>
        }
        <Button
          type="primary"
          onClick={handleSave}
        >
          保存
        </Button>
      </Box>
    </Form>
  );
}