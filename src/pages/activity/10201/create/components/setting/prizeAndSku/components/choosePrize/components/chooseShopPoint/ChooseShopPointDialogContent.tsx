import { Box, Form, Field, Input, Button, Radio, Divider, Message } from '@alifd/next';
import ImgUploadFromItem from '@/components/ImgUpload';
import NumberInput from '@/components/NumberInput/NumberInput';
import { PrizeTypeEnum, getPrizeTypeLabel } from '@/utils';
import { ACTIVITY_STATUS } from '@/utils/constant';

/*
  选择店铺积分奖品-编辑奖品弹窗
*/
export default function ChooseShopPointDialogContent({ record, onResolve, field, activityType, status }) {
  const [activityStatus, operationType] = status || [];
  const AF_Act_Not_Start = field && field.getValue('AF_Act_Not_Start') || activityStatus !== 1;
  const isView = operationType === 'view';
  const isEnded = activityStatus === ACTIVITY_STATUS.ENDED;
  const needDisable = isView;

  const shopPointField = Field.useField({
    values: {
      // 奖品图
      showImage: record?.showImage ?? '',
      // 奖品类型 只读
      lotteryType: record?.lotteryType ?? PrizeTypeEnum.MEMBER_POINT.value,
      // 奖品名称（可编辑）
      lotteryName: record?.lotteryName ?? '',
      // 单份积分值（可编辑）
      lotteryValue:
        typeof record?.lotteryValue === 'number' || typeof record?.lotteryValue === 'string' ? record.lotteryValue : 1,
      // 发放份数（积分的奖品数量 = 发放份数）
      prizeNum: typeof record?.prizeNum === 'number' ? record.prizeNum : 1,
      // 中奖概率
      probability: record?.probability || 0,
      // 每日限额 1 限制 2 不限制
      dayLimitType: record?.dayLimitType || 2,
      // 每日限额份数
      dayLimitCount: record?.dayLimitCount || 1,
      // 中奖限制
      awardLimitType: record?.awardLimitType || 2,
      // 中奖限制份数
      awardLimitCount: record?.awardLimitCount || 1,
      // 中奖弹窗引导按钮文案 未填写默认“我知道了”
      guideButtonText: record?.guideButtonText || '',
      // 引导按钮跳转链接  1 首页 2关闭
      guideButtonLink: record?.guideButtonLink || 2,
      // 关闭按钮触发引导跳转  1 开启 2 不开启"
      guideCloseButtonType: record?.guideCloseButtonType || 2,
    },
  });

  const handleCancel = () => {
    onResolve(null);
  };

  // 点击“保存”时进行表单校验，并组织最终返回的数据格式
  const handleSave = () => {
    shopPointField.validate((errors, values: any) => {
      if (errors) return;
       // 校验每日限额不得超过发放份数
       if (values.dayLimitType === 1 && values.dayLimitCount > values.prizeNum) {
        Message.error(`每日限额不能超过发放份数 ${values.prizeNum}`);
        return;
      }

      // 校验每人最多抽中次数不得超过发放份数
      if (values.awardLimitType === 1 && values.awardLimitCount > values.prizeNum) {
        Message.error(`每人最多抽中次数不能超过发放份数 ${values.prizeNum}`);
        return;
      }
      const result = {
        showImage: values.showImage,
        lotteryType: values.lotteryType,
        lotteryName: values.lotteryName, // 积分奖品名称
        lotteryValue: String(values.lotteryValue), // 单份积分值
        price: String(values.lotteryValue), // 价值等于单份积分值
        prizeNum: Number(values.prizeNum), // 奖品数量
        probability: values.probability,
        dayLimitType: values.dayLimitType,
        dayLimitCount: values.dayLimitCount,
        awardLimitType: values.awardLimitType,
        awardLimitCount: values.awardLimitCount,
        guideButtonText: values.guideButtonText,
        guideButtonLink: values.guideButtonLink,
        guideCloseButtonType: values.guideCloseButtonType,
      };
      onResolve(result);
    });
  };

  return (
    <Form field={shopPointField} className="edit-prize-form">
      {/* 奖品图 */}
      <ImgUploadFromItem
        name="showImage"
        label="奖品图"
        required
        requiredMessage="请上传奖品图"
        disabled={needDisable}
        img={{
          value: shopPointField.getValue('showImage'),
          width: 500,
          height: 500,
        }}
        onSuccess={(url: string) => {
          shopPointField.setValue('showImage', url);
          shopPointField.setError('showImage', ' ');
        }}
        onReset={() => shopPointField.setValue('showImage', '')}
      />
      {/* 奖品类型（只读） */}
      <Form.Item label="奖品类型">
        <Input value={getPrizeTypeLabel(PrizeTypeEnum.MEMBER_POINT.value)} disabled readOnly />
      </Form.Item>
      {/* 奖品名称 */}
      <Form.Item
        label="奖品名称"
        required
        requiredMessage="请输入奖品名称"
        pattern="^[\u4E00-\u9FA5A-Za-z0-9_\-/.]+$"
        patternMessage="不可输入特殊符号，支持中文、英文、数字及下划线"
        disabled={needDisable}
      >
        <Input
          name="lotteryName"
          maxLength={16}
          trim
          composition
          showLimitHint
          disabled={needDisable}
          value={shopPointField.getValue('lotteryName')}
          onChange={(val) => shopPointField.setValue('lotteryName', val)}
        />
      </Form.Item>
      {/* 单份积分值 */}
      <Form.Item label="单份积分值" required requiredMessage="请输入单份积分值" min={1} disabled={needDisable}>
        <NumberInput
          name="lotteryValue"
          min={1}
          value={shopPointField.getValue('lotteryValue')}
          onChange={(val: number) => shopPointField.setValue('lotteryValue', val)}
          style={{ width: '100%' }}
        />
      </Form.Item>
      {/* 发放份数 */}
      <Form.Item label="发放份数" required requiredMessage="请输入发放份数" min={1} disabled={needDisable}>
        <NumberInput
          name="prizeNum"
          min={1}
          value={shopPointField.getValue('prizeNum')}
          onChange={(val: number) => shopPointField.setValue('prizeNum', val)}
          style={{ width: '100%' }}
        />
      </Form.Item>
      {/* 转盘活动的特殊配置 */}
      {
        activityType === 'wheel' && (
          <>
            <Divider />
            <Form.Item
              label="中奖概率"
              required
              disabled={isView || isEnded}
              name={'probability'}
              validator={(_, value: number) => {
                if (value <= 0) {
                  return Promise.reject('请设置中奖概率');
                }
                return Promise.resolve();
              }}
            >
              <NumberInput
                name="probability"
                precision={3}
                style={{ width: 150 }}
                min={0.001}
                max={99.999}
                maxLength={7}
                value={shopPointField.getValue('probability')}
                onChange={val => shopPointField.setValue('probability', val)}
              />
            </Form.Item>
            <Form.Item label="每日限额" required requiredMessage="请选择每日限额" disabled={needDisable}>
              <Radio.Group
                value={shopPointField.getValue('dayLimitType')}
                onChange={val => shopPointField.setValue('dayLimitType', val)}
              >
                <Radio value={1}>限制</Radio>
                <Radio value={2}>不限制</Radio>
              </Radio.Group>
            </Form.Item>
            {
              shopPointField.getValue('dayLimitType') === 1 && (
                <Form.Item label=" " disabled={needDisable}>
                  <NumberInput
                    style={{ width: 150, marginRight: 6 }}
                    min={1}
                    value={shopPointField.getValue('dayLimitCount')}
                    onChange={val => shopPointField.setValue('dayLimitCount', val)}
                    placeholder="请输入每日限额份数"
                  />份
                </Form.Item>
              )
            }
            <Form.Item label="中奖限制" required requiredMessage="请选择中奖限制" disabled={needDisable}>
              <Radio.Group
                value={shopPointField.getValue('awardLimitType')}
                onChange={val => shopPointField.setValue('awardLimitType', val)}
              >
                <Radio value={1}>限制</Radio>
                <Radio value={2}>不限制</Radio>
              </Radio.Group>
            </Form.Item>
            {
              shopPointField.getValue('awardLimitType') === 1 && (
                <Form.Item label=" " disabled={needDisable}>
                  每人最多抽中本奖品
                  <NumberInput
                    style={{ width: 150, margin: '0 6px' }}
                    min={1}
                    value={shopPointField.getValue('awardLimitCount')}
                    onChange={val => shopPointField.setValue('awardLimitCount', val)}
                    placeholder="请输入中奖限制份数"
                  />次
                </Form.Item>
              )
            }
            <Divider />
            <Form.Item label="中奖弹窗引导按钮文案" disabled={needDisable}>
              <Input
                name="guideButtonText"
                placeholder="未填写默认为：我知道了"
                maxLength={5}
                showLimitHint
                value={shopPointField.getValue('guideButtonText')}
                onChange={val => shopPointField.setValue('guideButtonText', val)}
              />
            </Form.Item>
            <Form.Item label="引导按钮跳转链接" disabled={needDisable}>
              <Radio.Group
                value={shopPointField.getValue('guideButtonLink')}
                onChange={val => shopPointField.setValue('guideButtonLink', val)}
              >
                <Radio value={2}>关闭</Radio>
                <Radio value={1}>首页</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item label="关闭按钮触发引导跳转" disabled={needDisable}>
              <Radio.Group
                value={shopPointField.getValue('guideCloseButtonType')}
                onChange={val => shopPointField.setValue('guideCloseButtonType', val)}
              >
                <Radio value={2}>不开启</Radio>
                <Radio value={1}>开启</Radio>
              </Radio.Group>
            </Form.Item>
          </>
        )
      }
      {/* 底部按钮 */}
      <Box direction="row" justify="center" spacing={16} margin={[20, 0, 0, 0]}>
        {!AF_Act_Not_Start && <Button onClick={handleCancel}>{record ? '返回列表' : '取消'}</Button>}
        <Button type="primary" onClick={handleSave}>
          保存
        </Button>
      </Box>
    </Form>
  );
}
