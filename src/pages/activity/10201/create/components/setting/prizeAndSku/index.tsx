import Container from '@/components/Container';
import { usePrizeAndSku } from './hooks';
import { Box, Button, Form, Dialog, Table, Radio, Input, Divider, Message } from '@alifd/next';
import { useEffect, useState } from 'react';
import { useActivity } from '../../../reducer';
import NumberInput from '@/components/NumberInput/NumberInput';
import { ACTIVITY_STATUS } from '@/utils/constant';
import { PRIZE_INFO } from '@/pages/activity/10201/create/type';
import PrizeTable from '../prizeAndSku/components/PrizeTable';
import { useLocation } from 'ice';
import { activityCustomizeActivityMinusLotteryNum } from '@/api/b';
import { PrizeTypeEnum } from '@/utils';
import { getActivityPrize } from '@/api/v10201';

export default function Prize() {
  const { prizeAndSku, updatePrizeAndSku, errors } = usePrizeAndSku();
  const [skuVisible, setSkuVisible] = useState(false);
  const { state } = useActivity();
  const [seriesSkuList, setSeriesSkuList] = useState<any>([]);
  const { operationType, activityStatus } = state.extra;

  const location = useLocation();
  const { activityId } = location.state || {};

  const isEdit = operationType === 'edit';
  const isView = operationType === 'view';
  const unStart = activityStatus !== ACTIVITY_STATUS.NOT_STARTED;

  const needDisable = (isEdit || isView) && unStart;

  // 系列内总兑换次数最大值
  const [totalReceiveCountMax, setTotalReceiveCountMax] = useState(1);

  // 获取奖品的prizeId
  const getPrizeIds = async (stepPrizeList: any[]) => {
    try {
      const res = await getActivityPrize({ activityId });
      // 为每个奖品匹配对应的prizeId
      stepPrizeList.forEach((prize) => {
        const matchedPrize = res.find((item) => item.prizeSort === prize.sortId);
        if (matchedPrize) {
          prize.prizeId = matchedPrize.prizeId;
        }
      });
    } catch (error) {
      console.error('获取奖品ID失败：', error);
    }
  };

  // 释放阶梯下所有奖品的库存
  const releaseStepPrizeStock = async (stepPrizeList: any[]) => {
    try {
      // 先获取所有奖品的prizeId
      await getPrizeIds(stepPrizeList);

      // 只处理实物奖品且有prizeId的奖品
      const physicalPrizes = stepPrizeList.filter(prize =>
        prize.lotteryType === PrizeTypeEnum.PRACTICALITY.value && prize.prizeId,
      );

      // 并行释放所有实物奖品的库存
      await Promise.all(
        physicalPrizes.map(prize =>
          activityCustomizeActivityMinusLotteryNum({
            lotteryId: prize.prizeId,
            num: prize.prizeNum,
          }),
        ),
      );
    } catch (error) {
      console.error('释放阶梯奖品库存失败：', error);
      throw error;
    }
  };

  useEffect((): void => {
    const perReceiveCount = +prizeAndSku.seriesList[0].perReceiveCount || 1;
    const stepListLength = prizeAndSku.seriesList[0].stepList.length || 1;
    const maxCount = perReceiveCount * stepListLength;
    setTotalReceiveCountMax(maxCount);

    // 如果当前的总兑换次数为0，自动设置为1（确保有合理的默认值）
    if (+prizeAndSku.seriesList[0].totalReceiveCount === 0) {
      const newSeriesList = [...prizeAndSku.seriesList];
      newSeriesList[0].totalReceiveCount = 1;
      updatePrizeAndSku({ seriesList: newSeriesList });
    }
    if (+prizeAndSku.seriesList[0].totalReceiveCount > perReceiveCount * stepListLength) {
      const newSeriesList = [...prizeAndSku.seriesList];
      newSeriesList[0].totalReceiveCount = perReceiveCount * stepListLength;
      updatePrizeAndSku({ seriesList: newSeriesList });
    }
  }, [prizeAndSku.seriesList[0].perReceiveCount,
      prizeAndSku.seriesList[0].stepList.length,
      prizeAndSku.seriesList[0].totalReceiveCount]);

  const hasPrizeError = errors.some(err => err.includes('请至少添加一个奖品'));

  return (
    <Container title={`${prizeAndSku.seriesList[0].seriesName}满罐礼`}>
      <Form.Item label="SPU列表" disabled={false}>
        <Button onClick={() => {
          setSeriesSkuList(prizeAndSku.seriesSkuList);
          console.log('seriesSkuList', seriesSkuList, 'prizeAndSku', prizeAndSku);
          setSkuVisible(true);
        }}
        >
          查看SPU
        </Button>
      </Form.Item>
      <Form.Item required requiredMessage={'请输入系列内总兑换次数'} label="系列内总兑换次数">
        <NumberInput
          min={1}
          max={totalReceiveCountMax}
          step={1}
          type="inline"
          value={prizeAndSku.seriesList[0].totalReceiveCount}
          onChange={(totalReceiveCount: number) => {
            const newSeriesList = [...prizeAndSku.seriesList];
            newSeriesList[0].totalReceiveCount = totalReceiveCount;
            updatePrizeAndSku({ seriesList: newSeriesList });
          }}
          name={'totalReceiveCount'}
        />
        <div style={{ color: '#8d9299', display: 'inline-block' }}>系列内最多可兑换{totalReceiveCountMax}次</div>
      </Form.Item>
      <Form.Item required requiredMessage={'请输入每个阶梯下奖品可领取次数'} label="每个阶梯下奖品可领取次数">
        <NumberInput
          min={1}
          max={9999999}
          step={1}
          type="inline"
          value={prizeAndSku.seriesList[0].perReceiveCount}
          onChange={(perReceiveCount: number) => {
            const newSeriesList = [...prizeAndSku.seriesList];
            newSeriesList[0].perReceiveCount = perReceiveCount;
            updatePrizeAndSku({ seriesList: newSeriesList });
          }}
          name={'perReceiveCount'}
        />
      </Form.Item>
      <Form.Item
        label="相同奖品是否可以重复领取"
        required
      >
        <Radio.Group
          value={prizeAndSku.seriesList[0].prizeReceiveLimit}
          onChange={(value: number) => {
            const newSeriesList = [...prizeAndSku.seriesList];
            newSeriesList[0].prizeReceiveLimit = value;
            updatePrizeAndSku({ seriesList: newSeriesList });
          }}
        >
          <Radio value={1}>可以重复</Radio>
          <Radio value={2}>不可以重复</Radio>
        </Radio.Group>
      </Form.Item>
      {
        prizeAndSku.seriesList[0].stepList.map((step, index) => {
          return (
            <>
              <Divider dashed />
              {prizeAndSku.seriesList[0].stepList.length > 1 && !needDisable && (
                <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                  <Button
                    text
                    type="primary"
                    onClick={() => {
                      // 根据不同场景生成不同的提示内容
                      const getDeleteContent = () => {
                        const stepName = step.stepName || `阶梯${index + 1}`;
                        const baseContent = `确定要删除阶梯"${stepName}"吗？删除后不可恢复。`;

                        // 如果是编辑未开始的活动，且该阶梯有实物奖品，需要特别提醒
                        if (isEdit && activityStatus === ACTIVITY_STATUS.NOT_STARTED) {
                          const hasPhysicalPrizes = step.prizeList.some(prize =>
                            prize.lotteryType === PrizeTypeEnum.PRACTICALITY.value,
                          );
                          if (hasPhysicalPrizes) {
                            return `${baseContent}\n\n删除阶梯将释放该阶梯下所有实物奖品的已冻结库存，请务必重新完整保存活动。`;
                          }
                        }

                        return baseContent;
                      };

                      Dialog.confirm({
                        title: '确认删除',
                        content: getDeleteContent(),
                        onOk: async () => {
                          try {
                            // 如果是编辑未开始的活动，需要释放该阶梯下所有奖品的库存
                            if (isEdit && activityStatus === ACTIVITY_STATUS.NOT_STARTED) {
                              await releaseStepPrizeStock(step.prizeList);
                            }

                            const newSeriesList = [...prizeAndSku.seriesList];
                            newSeriesList[0].stepList.splice(index, 1);
                            updatePrizeAndSku({ seriesList: newSeriesList });
                            Message.success('阶梯删除成功');
                          } catch (error) {
                            console.error('删除阶梯失败：', error);
                            Message.error('删除阶梯失败，请重试');
                          }
                        },
                      });
                    }}
                  >
                    <i className={'iconfont icon-shanchu'} />
                  </Button>
                </div>
              )}
              <Form.Item label="阶梯名称" required requiredMessage="请输入阶梯名称">
                <Input
                  name={`stepName${index}`}
                  value={step.stepName}
                  placeholder="请输入阶梯名称"
                  maxLength={6}
                  onChange={(stepName: string) => {
                    const newSeriesList = [...prizeAndSku.seriesList];
                    newSeriesList[0].stepList[index].stepName = stepName;
                    updatePrizeAndSku({ seriesList: newSeriesList });
                  }}
                  style={{ width: 'var(--activity-form-item-width)' }}
                />
              </Form.Item>
              <Form.Item label="罐数" required requiredMessage="请输入罐数">
                <NumberInput
                  name={`stepPic${index}`}
                  min={1}
                  max={9999999}
                  step={1}
                  type="inline"
                  value={step.tankNum}
                  onChange={(tankNum: number) => {
                    const newSeriesList = [...prizeAndSku.seriesList];
                    newSeriesList[0].stepList[index].tankNum = tankNum;
                    updatePrizeAndSku({ seriesList: newSeriesList });
                  }}
                />
              </Form.Item>
              <Form.Item
                label="奖品列表"
                validateState={hasPrizeError ? 'error' : undefined}
                help={hasPrizeError ? errors.find(err => err.includes('请至少添加一个奖品')) : undefined}
              >
                <Box margin={[6, 0, 0, 0]}>
                  <PrizeTable
                    disabledTabs={[6, 9]}
                    status={[activityStatus, operationType]}
                    prizeList={step.prizeList}
                    onPrizeChange={(newPrizeList) => {
                      const newSeriesList = [...prizeAndSku.seriesList];
                      newSeriesList[0].stepList[index].prizeList = newPrizeList;
                      updatePrizeAndSku({ seriesList: newSeriesList });
                    }}
                    activityId={activityId}
                  />
                  <div className="form-extra">注：虚拟奖项一旦开奖自动发放，实物奖项需要用户在中奖后主动领取（需填写收货地址）</div>
                </Box>
              </Form.Item>
              <Form.Item label=" " disabled={step.prizeList.length >= 10 || isView}>
                <Button
                  disabled={step.prizeList.length >= 10}
                  type="primary"
                  onClick={() => {
                    const newSeriesList = [...prizeAndSku.seriesList];
                    // 创建新奖品，设置sortId为当前奖品在该阶梯中的位置（从1开始）
                    const newPrize = { ...PRIZE_INFO, sortId: step.prizeList.length + 1, isAddNew: true };
                    newSeriesList[0].stepList[index].prizeList = [...step.prizeList, newPrize];
                    updatePrizeAndSku({ seriesList: newSeriesList });
                  }}
                >
                  +添加奖品（{step.prizeList.length}/10）
                </Button>
              </Form.Item>

            </>
          );
        })
      }
      <Form.Item label=" ">
        {!needDisable && (
          <Button
            disabled={prizeAndSku.seriesList[0].stepList.length >= 7}
            type="secondary"
            style={{ width: '100%' }}
            onClick={() => {
              const newSeriesList = [...prizeAndSku.seriesList];
              const { stepList } = newSeriesList[0];
              // 计算新阶梯的默认罐数：如果有前一个阶梯，则为前一个阶梯罐数+1，否则为1
              const defaultTankNum = stepList.length > 0 ? stepList[stepList.length - 1].tankNum + 1 : 1;
              newSeriesList[0].stepList.push({
                stepName: '',
                tankNum: defaultTankNum,
                prizeList: [],
              });
              updatePrizeAndSku({ seriesList: newSeriesList });
            }}
          >
            {prizeAndSku.seriesList[0].stepList.length}/7添加阶梯
          </Button>
        )}
      </Form.Item>

      <Dialog title="查看SPU" visible={skuVisible} footer={false} onClose={() => { setSkuVisible(false); }}>
        {seriesSkuList.length && (
          <div style={{ margin: '10px' }}>
            <Table dataSource={seriesSkuList} fixedHeader>
              <Table.Column title="spuId" dataIndex="spuId" />
              <Table.Column title="skuId" dataIndex="skuId" />
              <Table.Column title="罐数" dataIndex="tankNum" />
              <Table.Column title="品线" dataIndex="productLine" />
              <Table.Column title="排序" dataIndex="sort" />
            </Table>
          </div>
        )}
      </Dialog>
    </Container>
  );
}
