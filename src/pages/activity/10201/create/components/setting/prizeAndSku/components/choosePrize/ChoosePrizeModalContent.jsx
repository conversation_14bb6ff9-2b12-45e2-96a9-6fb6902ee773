import React, { useState } from 'react';
import { Box, Tab, Message } from '@alifd/next';
import PhysicalGoodsList from '@/pages/prize/physical/components/PhysicalGoodsList';
import { PrizeTypeEnum } from '@/utils';
import ChooseShopPointDialogContent
  from '../choosePrize/components/chooseShopPoint/ChooseShopPointDialogContent';
import Coupon from './components/coupon';

export default function ChoosePrizeModalContent({ onSelectPrize, disabledTabs = [], activityType, status, needDisable }) {
  // 将所有 Tab 配置信息放到一个数组中管理
  const tabItems = [
    {
      key: PrizeTypeEnum.PRACTICALITY.value,
      tab: '实物（手工发货）',
      content: (
        <Box spacing={30} padding={[20, 0]}>
          <Message type="warning">
            {`此项奖品需要商家导出用户填写的收货信息，线下自行发货
用户领取奖品：用户获得实物奖品后需要填写收货信息，商家根据收货信息线下发货`}
          </Message>
          <PhysicalGoodsList
            activityType={activityType}
            isChoosePrize
            prizeType={PrizeTypeEnum.PRACTICALITY.value}
            onSelectPrize={onSelectPrize}
          />
        </Box>
      ),
    },
    {
      key: PrizeTypeEnum.PRACTICALITY_BY_ORDER.value,
      tab: '实物（随单发货）',
      content: (
        <Box spacing={30} padding={[20, 0]}>
          <Message type="warning">{'此项奖品可在用户下单时备注到最近一笔未发货订单中，由平台下发给OMS发货'}</Message>
          <PhysicalGoodsList
            activityType={activityType}
            isChoosePrize
            prizeType={PrizeTypeEnum.PRACTICALITY_BY_ORDER.value}
            onSelectPrize={onSelectPrize}
          />
        </Box>
      ),
    },
    {
      key: PrizeTypeEnum.COUPON.value,
      tab: '优惠券',
      content: (
        <Box padding={[20, 0, 0]}>
          <Message type="warning">
            {'此奖品仅支持【自由渠道】券'}
          </Message>
          <Coupon onSelectPrize={onSelectPrize} activityType={activityType} status={status} needDisable={needDisable} />
        </Box>
      ),
    },
    {
      key: PrizeTypeEnum.MEMBER_POINT.value,
      tab: '店铺会员积分',
      content: (
        <Box padding={[20, 0, 0]}>
          <ChooseShopPointDialogContent onResolve={onSelectPrize} activityType={activityType} status={status} />
        </Box>
      ),
    },
  ];

  // 初始化默认激活 tab（不能是被禁用的）
  const getInitialTabKey = () => {
    const availableTabs = tabItems.map(t => t.key).filter(key => !disabledTabs.includes(key));
    return availableTabs.length > 0 ? availableTabs[0] : '';
  };
  const [activeTabKey, setActiveTabKey] = useState(getInitialTabKey());

  return (
    <Tab
      activeKey={activeTabKey}
      onChange={setActiveTabKey}
      className="choose-prize-modal-tab"
      navClassName="choose-prize-modal-tab-nav"
      contentClassName="choose-prize-modal-tab-content"
    >

      {tabItems.map((item) => (
        <Tab.Item
          key={item.key}
          tab={item.tab}
          disabled={disabledTabs.includes(item.key)}
        >
          {item.content}
        </Tab.Item>
      ))}
    </Tab>
  );
}
