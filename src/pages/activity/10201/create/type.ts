import { PrizeTypeEnum } from '@/utils';

export enum ModuleType {
  BASE = 'base',
  THRESHOLD = 'threshold',
  ORDER = 'order',
  STEP = 'step',
  PRIZE_AND_SKU = 'prizeAndSku',
  RULE = 'rule',
  SHARE = 'share',
}
// Action类型定义
type ActionType =
  | 'UPDATE_BASE'
  | 'UPDATE_THRESHOLD'
  | 'UPDATE_ORDER'
  | 'UPDATE_STEP'
  | 'UPDATE_PRIZE_AND_SKU'
  | 'UPDATE_RULE'
  | 'UPDATE_SHARE'
  | 'UPDATE_DECORATE'
  | 'VALIDATE_MODULE'
  | 'UPDATE_EXTRA'
  | 'CLEAR_ERRORS'
  | 'INIT_MODULE';

export interface Action {
  type: ActionType;
  payload?: any;
  module?: ModuleType;
}

// 基础信息模块状态定义
export interface BaseInfoState {
  activityType: number;
  activityName: string;
  startTime: string;
  endTime: string;
  expireDay: number;
  templateCode: number;
  activityId?: string;
}
export interface ExtraState {
  operationType: 'edit' | 'add' | 'copy' | 'view';
  originalEndTime: string;
  // 活动状态 1未开始 2进行中 3已结束
  activityStatus: number;
  activityUrl: string;
}

export interface ThresholdState {
  thresholdType: number;
  thresholdInfo: any;
  isMember: number;
}

export interface OrderState {
  // 订单是否限制 1限制 2不限制
  limitOrder: number;
  // 订单开始时间
  orderStartTime: string;
  // 订单结束时间
  orderEndTime: string;
  // 是否延迟发放 0 否 1 是
  isAwardDays: number;
  // 延迟发放天数
  awardDays: number;
}

export interface StepState {
  // 是否限制活动内总兑换次数 0 不限制 1 限制
  actLimitActTotalReceiveCount: number;
  // 活动内总兑换次数
  actTotalReceiveCount: number;
  fileList: any[];
}

export interface SeriesSkuList {
  skuId: string;
  tankNum: number;
}

export interface PrizeInfo {
  potNum: string;
  lotteryName: string;
  lotteryType: string | number;
  lotteryValue: string;
  prizeNum: string | number;
  dayLimitType: string | number;
  awardLimitType: string | number;
  awardLimitCount: string | number;
  sortId?: number;
  showImage: string;
  price?: string;
  status?: number;
  isAddNew: boolean;
}

export interface StepList {
  // 阶梯名称
  stepName: string;
  // 罐数
  tankNum: number;
  // 奖品列表
   prizeList: PrizeInfo[];
}

export const PRIZE_INFO: PrizeInfo = {
  potNum: '',
  lotteryName: '', // 默认奖品名称
  lotteryType: PrizeTypeEnum.THANKS.value, // 默认设置为谢谢惠顾
  lotteryValue: '', // 默认类型不需要值
  prizeNum: 1, // 默认发放份数
  dayLimitType: 2, // 默认值
  awardLimitType: 2, // 默认值
  awardLimitCount: 1, // 默认值
  price: '0',
  showImage: '',
  sortId: 0,
  // 状态 0-下架 1-上架
  status: 1,
  // 是否是以前存在的奖品
  isAddNew: true, // 新添加的奖品默认为true
};

export interface SeriesList {
  seriesName: string;
  seriesSkuList: SeriesSkuList[];
  totalReceiveCount: number | string;
  perReceiveCount: number | string;
  prizeReceiveLimit: number;
  stepList: StepList[];
  previewSpuList: any[];
}

export interface PrizeAndSkuState {
  // SKU列表
  seriesSkuList: SeriesSkuList[];
  // 系列及奖品列表
  seriesList: SeriesList[];
}

export interface ShareState {
  shareTitle: string;
  shareContent: string;
  mpImg: string;
}

export interface AppState {
  extra: ExtraState;
  base: BaseInfoState;
  threshold: ThresholdState;
  order: OrderState;
  step: StepState;
  prizeAndSku: PrizeAndSkuState;
  rule: string;
  decorate: string;
  share: ShareState;
  errors: Record<ModuleType, string[]>;
}
