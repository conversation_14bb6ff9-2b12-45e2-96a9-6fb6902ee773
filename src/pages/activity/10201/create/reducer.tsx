import React, { createContext, useReducer, ReactNode, useContext } from 'react';
import { Action, AppState, ModuleType } from './type';
import dayjs from 'dayjs';
import { getExpireDay } from '@/utils';

const expireDay = getExpireDay();
// 初始状态
const initialState: AppState = {
  extra: {
    // 操作类型 add 新增 edit 编辑 view 预览 copy 复制
    operationType: 'add',
    // 原始结束时间 用于复制活动时，判断不可选择原始活动之前的时间
    originalEndTime: dayjs().add(expireDay > 15 ? 15 : expireDay, 'day').format('YYYY-MM-DD 00:00:00'),
    // 活动状态 1未开始 2进行中 3已结束
    activityStatus: 1,
    // 活动地址
    activityUrl: '',
  },
  base: {
    // 活动类型
    activityType: 10201,
    // 模板ID
    templateCode: 1001,
    // 活动名称
    activityName: '满罐阶梯礼',
    // 活动开始时间
    startTime: dayjs().format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: dayjs().add(expireDay > 15 ? 15 : expireDay, 'day').format('YYYY-MM-DD 00:00:00'),
    // 过期时间
    expireDay,
  },
  threshold: {
    thresholdType: -1,
    thresholdInfo: {
      crowdName: '',
      conditions: {},
    },
    isMember: 1,
  },
  order: {
    // 订单是否限制 1限制 2不限制
    limitOrder: 1,
    // 订单开始时间
    orderStartTime: dayjs().format('YYYY-MM-DD 00:00:00'),
    // 订单结束时间
    orderEndTime: dayjs().add(expireDay > 15 ? 15 : expireDay, 'day').format('YYYY-MM-DD 00:00:00'),
    // 是否延迟发奖  是否延迟订单 0否 1是
    isAwardDays: 0,
    // 延迟发放天数
    awardDays: 1,
  },
  step: {
    // 是否限制活动内总兑换次数 0 不限制 1 限制
    actLimitActTotalReceiveCount: 0,
    // 活动内总兑换次数
    actTotalReceiveCount: 1,
    // 文件
    fileList: [],
  },
  prizeAndSku: {
    // SKU列表
    seriesSkuList: [],
    // 系列及奖品列表
    seriesList: [
      {
          // 系列名
          seriesName: '',
          // sku列表
          seriesSkuList: [],
          // 系列内总兑换次数
          totalReceiveCount: 1,
          // 每个阶梯下奖品可领取次数
          perReceiveCount: 1,
          // 相同奖品是否可以重复领取 1 可以重复 2不可以重复
          prizeReceiveLimit: 0,
          // 各阶梯奖品列表
          stepList: [{
            // 阶梯名称`
            stepName: '',
            // 罐数
            tankNum: 1,
            // 奖品列表
            prizeList: [],
          }],
          previewSpuList: [],
      },
    ],
  },
  rule: '',
  decorate: '',
  share: {
    shareTitle: '满罐赢好礼，大奖等你领！',
    shareContent: '消费达标赠惊喜好物',
    mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
  },
  errors: {
    [ModuleType.BASE]: [],
    [ModuleType.THRESHOLD]: [],
    [ModuleType.ORDER]: [],
    [ModuleType.STEP]: [],
    [ModuleType.PRIZE_AND_SKU]: [],
    [ModuleType.RULE]: [],
    [ModuleType.SHARE]: [],
  },
};


// Reducer函数
function reducer(state: AppState, action: Action): AppState {
  switch (action.type) {
    case 'UPDATE_BASE':
      return {
        ...state,
        base: {
          ...state.base,
          ...action.payload,
        },
      };
    case 'UPDATE_THRESHOLD':
      console.log('UPDATE_THRESHOLD', {
        ...state.threshold,
        ...action.payload,
      });
      return {
        ...state,
        threshold: {
          ...state.threshold,
          ...action.payload,
        },
      };
    case 'UPDATE_ORDER':
      console.log('UPDATE_ORDER', {
        ...state.order,
        ...action.payload,
      });
      return {
        ...state,
        order: {
          ...state.order,
          ...action.payload,
        },
      };
    case 'UPDATE_STEP':
      console.log('UPDATE_STEP', {
        ...state.step,
        ...action.payload,
      });
      return {
        ...state,
        step: {
          ...state.step,
          ...action.payload,
        },
      };
    case 'UPDATE_PRIZE_AND_SKU':
      console.log('UPDATE_PRIZE_AND_SKU', {
        ...state.prizeAndSku,
        ...action.payload,
      });
      return {
        ...state,
        prizeAndSku: {
          ...state.prizeAndSku,
          ...action.payload,
        },
      };
    case 'UPDATE_RULE':
      return {
        ...state,
        rule: action.payload,
      };
    case 'UPDATE_SHARE':
      return {
        ...state,
        share: {
          ...state.share,
          ...action.payload,
        },
      };
    case 'UPDATE_DECORATE':
        return {
          ...state,
          decorate: action.payload,
        };
    case 'VALIDATE_MODULE':
      if (!action.module) return state;
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.module]: action.payload,
        },
      };
    case 'CLEAR_ERRORS':
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.module as ModuleType]: [],
        },
      };
    case 'UPDATE_EXTRA':
      console.log('UPDATE_EXTRA', {
        extra: {
          ...state.extra,
          ...action.payload,
        },
      });
      return {
        ...state,
        extra: {
          ...state.extra,
          ...action.payload,
        },
      };
    case 'INIT_MODULE': {
      const { activityData } = action.payload;

      // 处理奖品数据，确保从接口返回的奖品 isAddNew 为 false
      if (activityData.prizeAndSku?.seriesList) {
        activityData.prizeAndSku.seriesList.forEach((series: any) => {
          if (series.stepList) {
            series.stepList.forEach((step: any) => {
              if (step.prizeList) {
                step.prizeList = step.prizeList.map((prize: any) => ({
                  ...prize,
                  // 从接口返回的奖品标记为非新添加
                  isAddNew: false,
                }));
              }
            });
          }
        });
      }

      console.log('INIT_MODULE', {
        ...state,
        ...activityData,
        decorate: action.payload.decorationData,
      });
      return {
        ...state,
        ...activityData,
        decorate: action.payload.decorationData,
      };
    }
    default:
      return state;
  }
}

// 创建Context
interface ActivityContextType {
  state: AppState;
  dispatch: React.Dispatch<Action>;
}

export const ActivityContext = createContext<ActivityContextType>({
  state: initialState,
  dispatch: () => null,
});

// Context Provider组件
interface ActivityProviderProps {
  children: ReactNode;
}

export function ActivityProvider({ children }: ActivityProviderProps) {
  const [state, dispatch] = useReducer(reducer, initialState);

  return (
    <ActivityContext.Provider value={{ state, dispatch }}>
      {children}
    </ActivityContext.Provider>
  );
}

// 自定义Hook方便使用Context
export function useActivity() {
  const context = useContext(ActivityContext);
  if (!context) {
    // 改成中文
    throw new Error('useActivity必须使用ActivityProvider包裹');
  }
  return context;
}
