import Container from '@/components/Container';
import { Component, useState } from 'react';
import { useLocation } from '@ice/runtime/router';
import ActivityDatas from './components/data';
import ExchangeRecord from './components/ExchangeRecord';
import ProductLineData from './components/ProductLineData';
import NumberCansDetail from './components/NumberCansDetail';
import { Box, Tab } from '@alifd/next';

const tabList = [
  { title: '兑换记录', key: 'award', component: ExchangeRecord },
  { title: '品线数据', key: 'physical', component: ProductLineData },
  { title: '罐数详情', key: 'order', component: NumberCansDetail },
  { title: '活动数据', key: 'data', component: ActivityDatas },
];
export default function Data() {
  const [activeTab, setActiveTab] = useState(tabList[0].key);
  const location = useLocation();
  const { record } = location.state || {};

  return (
    <div>
      <Tab
        shape="pure"
        activeKey={activeTab}
        onChange={setActiveTab}
      >
        {tabList.map(tab => {
          const Component = tab.component;
          return (
            <Tab.Item
              key={tab.key}
              title={tab.title}
            >
              <Box padding={[20, 0]}>
                <Container>
                  {activeTab === tab.key && <Component record={record} />}
                </Container>
              </Box>
            </Tab.Item>
          );
        })}
        Record
      </Tab>
    </div>
  );
}