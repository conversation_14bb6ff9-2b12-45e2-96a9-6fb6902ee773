import { Box, Button, DatePicker2, Input, Loading, Message, Pagination, Select, Table } from '@alifd/next';
import { useEffect, useState } from 'react';
import { Auth } from '@/components/Auth';
import usePagination from '@/hooks/usePagination';
import dayjs, { Dayjs } from 'dayjs';
import { userPotRecordPage, userPotRecordPageExport } from '@/api/v10201Data';
import { Activity10201UserPotRecordPageResponse, IPageActivity10201UserPotRecordPageResponse } from '@/api/types';
import { downloadExcel, mask, copyText } from '@/utils';

export default function NumberCansDetail(record) {
  const { activityId, startTime, endTime } = record?.record;
  const [searchParams, setSearchParams] = useState({
    pin: '',
  });
  // 表格相关
  const [tableData, setTableData] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const defaultRangeVal = [
    dayjs(new Date(startTime)).format('YYYY-MM-DD 00:00:00'),
    dayjs(new Date(endTime)).format('YYYY-MM-DD 23:59:59'),
  ];
  let pageNum = 1;
  let pageSize = 10;

  // 查询条件
  const [timeRange, setTimeRange] = useState<any>(defaultRangeVal);

  // 通用的参数更新函数
  const updateSearchParam = (key: string, value: any) => {
    setSearchParams(prev => ({ ...prev, [key]: value }));
  };

  // 使用分页Hook
  const pagination = usePagination(pageNum, pageSize, async (current, size, resetParams) => {
    fetchData(current, size, resetParams);
  });
  useEffect(() => {
    fetchData();
  }, [activityId]);
  const fetchData = async (pageNum = 1, pageSize = 10, resetParams?: any) => {
    // console.log(resetParams,searchParams, timeRange, '查询====fetchData');
    setLoading(true);
    try {
      const data: IPageActivity10201UserPotRecordPageResponse = await userPotRecordPage({
        activityId: activityId,
        pageNum: pageNum,
        pageSize: pageSize,
        dateRange: timeRange,
         ...searchParams,
        ...resetParams,
      });
      console.log(data, 'res=====');
      pagination.setTotal(data?.total || 0);
      // console.log(data.total, 'total1=========');
      setTableData(data.records as Activity10201UserPotRecordPageResponse[]);
      setLoading(false);
    } catch (error) {
      console.error('查询失败', error);
      setLoading(false);
    }
  };
  const datePickerChange = (dates: Dayjs[]) => {
    // console.log(dates, 'dates====');
    setTimeRange([dayjs(dates[0]).format('YYYY-MM-DD HH:mm:ss'), dayjs(dates[1]).format('YYYY-MM-DD HH:mm:ss')]);
  };
  const handleSearch = () => {
    // console.log('查询====');
    pagination.changePage(pageNum);
  };
  const handleReset = () => {
    // console.log('重置====');
    setSearchParams({
      pin: '',
    });
    setTimeRange(defaultRangeVal);
    pagination.reset({
      pin: '',
      dateRange: defaultRangeVal,
    });
  };
  const handleExport = async () => {
    // console.log('导出====');
    setLoading(true);
    try {
      const data: any = await userPotRecordPageExport({
        activityId: activityId,
        dateRange: timeRange,
        ...searchParams,
      } as any);
      downloadExcel(data, `罐数详情${dayjs().format('YYYY-MM-DD HH:mm:ss')}`);
    } catch (err) {
      console.error(err);
      Message.error('导出失败');
    } finally {
      setLoading(false);
    }
  };
  return (
    <Box spacing={16}>
      <Box direction="row" spacing={10} align="center">
        <Input
          label="用户pin"
          placeholder="请输入用户pin"
          value={searchParams.pin}
          onChange={val => updateSearchParam('pin', val)}
        />
        <DatePicker2.RangePicker
          label="参与时间"
          showTime
          value={timeRange}
          defaultValue={defaultRangeVal}
          onChange={datePickerChange}
        />

        <Button
          type="primary"
          onClick={handleSearch}
        >
          查询
        </Button>
        <Button
          type="normal"
          onClick={handleReset}
        >
          重置
        </Button>
        <Auth authKey={'activity_list_data_export'}>
          <Button
            type="secondary"
            onClick={handleExport}
          >
            导出
          </Button>
        </Auth>
      </Box>
      <Box padding={[20]}>
        <Loading tip={'加载中...'} visible={loading}>
          <Table dataSource={tableData}>
            <Table.Column
              title="用户pin"
              dataIndex="encryptPin"
              cell={(_, index, row) => (
                <div>
                  {
                    <div className="lz-copy-text">
                      {row.encryptPin ? mask(row.encryptPin) : '-'}
                      {row.encryptPin && (
                      <span
                        className={'iconfont icon-fuzhi'}
                        style={{ marginLeft: 5 }}
                        onClick={() => {
                          copyText(row.encryptPin);
                        }}
                      />
                    )}
                    </div>
                }
                </div>
            )}
            />
            <Table.Column
              title="用户昵称"
              dataIndex="nickName"
              cell={(_, index, row) => (
                <div>
                  {
                    <div className="lz-copy-text">
                      {row.nickName ? mask(row.nickName) : '-'}
                      {row.nickName && (
                      <span
                        className={'iconfont icon-fuzhi'}
                        style={{ marginLeft: 5 }}
                        onClick={() => {
                          copyText(row.nickName);
                        }}
                      />
                    )}
                    </div>
                }
                </div>
            )}
            />

            <Table.Column
              title="参与时间"
              dataIndex="createTime"
              cell={(data, index, row) => {
              return <div>{dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')}</div>;
            }}
            />
            <Table.Column title="系列名称" dataIndex="seriesId" />
            <Table.Column title="总罐数" dataIndex="totalPot" />
            <Table.Column title="使用罐数" dataIndex="usePot" />
            <Table.Column title="剩余罐数" dataIndex="leftPot" />
          </Table>
        </Loading>
      </Box>
      <Box direction="row" justify="flex-end" margin={[16, 0, 0, 0]}>
        <Pagination
          {...pagination.paginationProps}
          shape="arrow-only"
          totalRender={(total1) => `共 ${total1} 条`}
          pageSizeSelector="dropdown"
          pageSizePosition="end"
        />
      </Box>
    </Box>
  );
}