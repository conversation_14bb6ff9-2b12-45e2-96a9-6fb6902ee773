import { Box, Button, DatePicker2, Loading, Message, Table } from '@alifd/next';
import { useEffect, useState } from 'react';
import { Auth } from '@/components/Auth';
import { downloadExcel } from '@/utils';
import dayjs, { Dayjs } from 'dayjs';
import { reportPageNew, reportPageNewExport } from '@/api/v10201Data';
import { CollectingTankNewResponse } from '@/api/types';

const defaultTableColumns = () => {
  return [
    {
      title: '活动日期',
      dataIndex: 'activityTime',
      lock: true,
      width: 120,
    },
    {
      title: '总数据',
      children: [
        {
          title: 'PV',
          dataIndex: 'pv',
          width: 90,
        },
        {
          title: 'UV',
          dataIndex: 'uv',
          width: 90,
        },
        {
          title: '付款人数',
          dataIndex: 'payUser',
          width: 90,
        },
        {
          title: '付款金额',
          dataIndex: 'payAmount',
          width: 90,
        },
        {
          title: '开卡人数',
          dataIndex: 'openCardUser',
          width: 90,
        },
        {
          title: '领取人数',
          dataIndex: 'receiveUser',
          width: 90,
        },
      ],
    },
  ];
};
export default function ActivityData(record) {
  // const activityId = '1954810298158415874';
  // const startTime = '2025-01-01 00:00:00';
  // const endTime = '2025-12-01 23:59:59';
  const { activityId, startTime, endTime } = record?.record;
  // 表格相关
  const [tableData, setTableData] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const [tableCloumns, setTableColumns] = useState<any[]>(defaultTableColumns());
  const defaultRangeVal = [
    dayjs(new Date(startTime)).format('YYYY-MM-DD'),
    dayjs(new Date(endTime)).format('YYYY-MM-DD'),
  ];
  // 查询条件
  const [timeRange, setTimeRange] = useState<any>(defaultRangeVal);

  useEffect(() => {
    fetchData();
  }, [activityId]);
  const fetchData = async (resetParams?: any) => {
    // console.log(resetParams, timeRange, '查询====fetchData');
    setLoading(true);
    try {
      const data: CollectingTankNewResponse[] = await reportPageNew({
        activityId: activityId,
        startDate: dayjs(timeRange[0]).format('YYYY-MM-DD'),
        endDate: dayjs(timeRange[1]).format('YYYY-MM-DD'),
        ...resetParams,
      });
      // console.log(data, 'res=====');
      if (data[0] && data[0].stepDataResponseList) {
        const columns: any = [];
        data[0].stepDataResponseList.forEach((item: any) => {
          const colmIndex = columns.findIndex((col: any) => col.title === item.seriesName);
          const stepColum = {
            title: item.stepName,
            stepSort: item.stepSort,
            children: [
              {
                title: '领取人数',
                dataIndex: `receiveUser${item.stepId}`,
                width: 90,
              },
              {
                title: '付款人数',
                dataIndex: `payUser${item.stepId}`,
                width: 90,
              },
              {
                title: '付款金额',
                dataIndex: `payAmount${item.stepId}`,
                width: 90,
              },
            ],
          };
          if (colmIndex === -1) {
            columns.push({
              title: item.seriesName,
              seriesSort: item.seriesSort,
              children: [stepColum],
            });
          } else {
            columns[colmIndex].children.push(stepColum);
          }
        });
        columns.sort((a, b) => a.seriesSort - b.seriesSort);
        columns.forEach((item) => {
          item.children.sort((a, b) => a.stepSort - b.stepSort);
        });
        setTableColumns([...defaultTableColumns(), ...columns]);
      }
      data.forEach((item: any) => {
        item.stepDataResponseList.forEach((step) => {
          item[`receiveUser${step.stepId}`] = step.receiveUser;
          item[`payUser${step.stepId}`] = step.payUser;
          item[`payAmount${step.stepId}`] = step.payAmount;
        });
      });
      setTableData(data);
      setLoading(false);
    } catch (error) {
      console.error('查询失败', error);
      setLoading(false);
    }
  };
  const datePickerChange = (dates: Dayjs[]) => {
    setTimeRange(dates);
  };
  const handleSearch = () => {
    // console.log('查询====');
    fetchData();
  };
  const handleReset = () => {
    // console.log('重置====');
    setTimeRange(defaultRangeVal);
    fetchData({
      startDate: dayjs(defaultRangeVal[0]).format('YYYY-MM-DD'),
      endDate: dayjs(defaultRangeVal[1]).format('YYYY-MM-DD'),
    });
  };
  const handleExport = async () => {
    console.log('导出====');
    const [start, end] = timeRange;
    setLoading(true);
    try {
      const data: any = await reportPageNewExport({
        activityId: activityId,
        startDate: dayjs(timeRange[0]).format('YYYY-MM-DD'),
        endDate: dayjs(timeRange[1]).format('YYYY-MM-DD'),
      } as any);
      downloadExcel(data, `满罐有礼数据报表${dayjs().format('YYYY-MM-DD HH:mm')}`);
    } catch (err) {
      console.error(err);
      Message.error('导出失败');
    } finally {
      setLoading(false);
    }
  };
  return (
    <Box spacing={16}>
      <Box direction="row" spacing={10} align="center">
        <DatePicker2.RangePicker
          label="时间范围"
          value={timeRange}
          defaultValue={defaultRangeVal}
          onChange={datePickerChange}
        />
        <Button
          type="primary"
          onClick={handleSearch}
        >
          查询
        </Button>
        <Button
          type="normal"
          onClick={handleReset}
        >
          重置
        </Button>
        <Auth authKey={'activity_list_data_export'}>
          <Button
            type="secondary"
            onClick={handleExport}
          >
            导出
          </Button>
        </Auth>
      </Box>
      <Box padding={[20]}>
        <Loading tip={'加载中...'} visible={loading}>
          <Table dataSource={tableData} columns={tableCloumns} />
        </Loading>
      </Box>
    </Box>
  );
}