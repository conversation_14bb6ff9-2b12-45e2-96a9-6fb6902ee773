import { Box, Loading, Table } from '@alifd/next';
import { useEffect, useState } from 'react';
import { reportPage } from '@/api/v10201Data';
import { ActivityThresholdResponse, CollectingTankDTO } from '@/api/types';
export default function ProductLineData(record) {
  const { activityId } = record?.record;
  // 表格相关
  const [tableData, setTableData] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const [columns, setColumns] = useState<any[]>([]);

  // 查询条件
  useEffect(() => {
    fetchData();
  }, [activityId]);
  const fetchData = async (resetParams?: any) => {
    setLoading(true);
    try {
      const res: CollectingTankDTO = await reportPage({
        activityId: activityId,
        ...resetParams,
      });
      const _columns = res?.lineName?.map((item, index) => {
        return {
          title: item,
          dataIndex: `line${index}`,
        };
      });
      const _tableData = res?.thresholdData?.map((item: ActivityThresholdResponse) => {
        const row = {};
        item.thresholdNum?.forEach((data, index) => {
          row[`line${index}`] = data;
        });
        return row;
      });
      setTableData(_tableData);
      setColumns(_columns || []);
      setLoading(false);
    } catch (error) {
      console.error('查询失败', error);
      setLoading(false);
    }
  };
  return (
    <Box spacing={16}>
      <Box padding={[0, 20]}>
        <Loading tip={'加载中...'} visible={loading}>
          <Table columns={columns} dataSource={tableData} />
        </Loading>
      </Box>

    </Box>
  );
}