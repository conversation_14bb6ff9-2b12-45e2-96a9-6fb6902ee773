import styles from './index.module.scss';
import Intro from '@/components/Intro';
import Index from '@/components/Container';
import ActivitySkinSelector from './components/ActivitySkinSelector/ActivitySkinSelector';


const instructions = `伊利定制-满罐有礼
1、活动效果：会员下单集罐、提升会员复购转化
2、活动门槛支持设置会员，满足活动门槛的买家才可参与活动；奖品支持实物奖品，礼品卡，优惠券`;

export default function CustomActivity() {
  return (
    <div className={styles.container}>
      <Intro
        activityName="伊利定制-满罐有礼"
        docLink="https://gyj4qdmjsv.feishu.cn/wiki/UDKWw35f5im9frkRDNtcg4cgn8b?from=from_copylink"
        instructions={instructions}
      />
      <Index
        title="选择模板"
        style={{ padding: 20, marginTop: 20 }}
      >
        <ActivitySkinSelector activityType={10201} />
      </Index>
    </div>
  );
}
