import { logout } from '@/utils/auth';
import { useEffect, useState } from 'react';
import { Button, Icon, Loading } from '@alifd/next';
import styles from './index.module.scss';
import { history } from '@ice/runtime';
import { getMyMenuList, permissionList, selectShop } from '@/api/permission';
import constant from '@/utils/constant';
import { shopGetMemberLevel } from '@/api/common';

interface ShopItem {
  shopName?: string;
  shopId: string;
  orderVersionName?: string;
  orderTrialVersion?: boolean;
  orderStartTime?: number;
  orderExpireTime?: number;
  shopOpenTime?: number;
  sort?: number;
  [key: string]: any;
}

export default function Shop() {
  const [shopList, setShopList] = useState<ShopItem[]>([]);
  const [loading, setLoading] = useState(false);

  const getShopList = async () => {
    const shopListStr = localStorage.getItem('LZ_SHOP_LIST');
    if (shopListStr) {
      try {
        const parsedList = JSON.parse(shopListStr);
        setShopList(parsedList);
        // 如果仅有一个店铺 自动登录
        if (parsedList.length === 1) {
          await handleSelectShop(parsedList[0]);
        }
      } catch (err) {
        console.error('解析店铺列表失败:', err);
        setShopList([]);
      }
    }
  };

  useEffect(() => {
    getShopList().then();
  }, []);

  const handleSelectShop = async (shop: ShopItem) => {
    setLoading(true);
    try {
      localStorage.setItem(constant.LZ_CURRENT_SHOP, JSON.stringify(shop));
      await selectShop({
        shopId: shop.shopId,
      });
      const menuList = await getMyMenuList();
      const functionList = await permissionList();
      const memberLevelList = await shopGetMemberLevel();
      localStorage.setItem(constant.LZ_FUNCTION_LIST, JSON.stringify(functionList));
      localStorage.setItem(constant.LZ_MENU_LIST, JSON.stringify(menuList));
      localStorage.setItem(constant.LZ_MEMBER_LEVEL_LIST, JSON.stringify(memberLevelList));
    } finally {
      setLoading(false);
    }
    history?.push('/');
  };

  // 格式化时间戳为YYYY-MM-DD
  const formatDate = (timestamp?: number) => {
    if (!timestamp) return '未知';
    const date = new Date(timestamp);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(
      2,
      '0',
    )}`;
  };

  // 计算剩余天数
  const getRemainingDays = (expireTime?: number) => {
    if (!expireTime) return 0;
    const now = new Date().getTime();
    const diff = expireTime - now;
    return Math.max(0, Math.floor(diff / (1000 * 60 * 60 * 24)));
  };

  return (
    <div>
      <div className={styles.header}>
        <div className={styles.title}>超级追单</div>
        <div className={styles.userInfo}>
          <div className={styles.username}>账号名称</div>
          <Button text type="primary" onClick={logout}>
            退出登录
          </Button>
        </div>
      </div>

      <div className={styles.shopContainer}>
        <div className={styles.shopHeader}>
          <h2 className={styles.shopTitle}>店铺列表</h2>
        </div>

        {shopList.length > 0 ? (
          <div className={styles.shopList}>
            {shopList.map((shop, index) => (
              <div
                key={index}
                className={`${styles.shopItem}`}
              >
                {shop.orderVersionName && <div className={styles.versionBadge}>
                  {shop.orderVersionName}
                </div>}

                <div className={styles.shopItemHeader}>
                  <img src={shop.logo} alt="" />
                  <div className={styles.shopName}>{shop.shopName || '未命名店铺'}</div>
                </div>

                <div className={styles.shopItemContent}>
                  <div className={styles.infoItem}>
                    <Icon type="calendar" size="small" className={styles.infoIcon} />
                    <span>订购日期：{formatDate(shop.shopOpenTime)}</span>
                  </div>

                  <div className={styles.expiryInfo}>
                    <div className={styles.progressBar}>
                      <div
                        className={styles.progressFill}
                        style={{
                          width: `${Math.min(100, (getRemainingDays(shop.orderExpireTime) / 365) * 100)}%`,
                        }}
                      />
                    </div>
                    <div className={styles.expiryText}>剩余 {getRemainingDays(shop.orderExpireTime)} 天</div>
                  </div>
                </div>

                <Loading size={'medium'} visible={loading}>
                  <div className={styles.enterButton} onClick={() => handleSelectShop(shop)}>
                    <span>进入店铺</span>
                    <Icon type="arrow-right" size="small" />
                  </div>
                </Loading>
              </div>
            ))}
          </div>
        ) : (
          <div className={styles.emptyShop}>
            <p>暂无店铺，请添加店铺</p>
          </div>
        )}
      </div>
    </div>
  );
}
