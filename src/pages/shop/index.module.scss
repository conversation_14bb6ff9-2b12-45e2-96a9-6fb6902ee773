.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--primary-black);
  padding: 8px 16px;

  .title {
    font-size: 18px;
    font-weight: 600;
    color: white;
  }

  .userInfo {
    display: flex;
    align-items: center;

    .username {
      margin-right: 10px;
      font-size: 13px;
      color: white;
    }
  }
}

.shopContainer {
  padding: 20px;
  box-sizing: border-box;

  .shopHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .shopTitle {
      font-size: 18px;
      color: var(--primary-black);
      margin: 0;
    }

    .addShopBtn {
      background-color: var(--primary-color);
      border-color: var(--primary-color);

      &:hover {
        background-color: var(--primary-color-dark);
        border-color: var(--primary-color-dark);
      }
    }
  }

  .shopList {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 30px;

    .shopItem {
      background-color: white;
      border-radius: 14px;
      height: 200px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      cursor: pointer;
      transition: all 0.3s;
      outline: 2px solid transparent;
      position: relative;
      overflow: hidden;
      box-shadow: 1px 2px 2px rgba(0, 89, 255, 0.15);

      &::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(to bottom right,
            rgba(0, 89, 255, 0.05),
            transparent,
            rgba(0, 89, 255, 0.05));
        transform: rotate(45deg);
        z-index: 0;
        pointer-events: none;
      }

      &:hover {
        outline-color: var(--primary-color);
        box-shadow: 0 0 15px rgba(0, 89, 255, 0.2);
        transform: translateY(-2px);
      }

      .shopItemHeader {
        padding: 20px 15px;
        display: flex;
        align-items: center;
        position: relative;
        z-index: 1;

        margin-top: 10px;

        img {
          // width: 80px;
          height: 30px;
          margin-right: 5px;
        }

        .shopName {
          font-size: 13.5px;
          font-weight: 500;
          color: var(--primary-black);
          text-align: left;
        }

        .trialBadge {
          background-color: var(--primary-color);
          border-radius: 4px;
          padding: 2px 8px;
          font-size: 12px;
          font-weight: 500;
        }
      }

      .versionBadge {
        position: absolute;
        top: 0;
        right: 0;
        background-color: var(--primary-color);
        color: white;
        font-size: 12px;
        font-weight: 500;
        padding: 4px 12px;
        border-radius: 0 12px 0 12px;
        z-index: 2;
      }

      .shopItemContent {
        padding: 0 15px;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        position: relative;
        z-index: 1;

        .infoItem {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          font-size: 13px;
          color: #555;

          .infoIcon {
            margin-right: 8px;
            color: var(--primary-color);
          }
        }

        .expiryInfo {
          margin-top: 10px;

          .progressBar {
            height: 6px;
            background-color: rgba(0, 0, 0, 0.05);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 5px;

            .progressFill {
              height: 100%;
              background: var(--primary-color);
              border-radius: 3px;
              transition: width 0.3s ease;
            }
          }

          .expiryText {
            font-size: 12px;
            text-align: right;
            color: var(--primary-color);
            font-weight: 500;
          }
        }
      }

      .enterButton {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 12px;
        color: var(--primary-color);
        font-weight: 500;
        transition: all 0.2s;
        position: relative;
        z-index: 1;
        border-top: 1px solid rgba(0, 89, 255, 0.1);

        &:hover {
          color: var(--primary-color);
          background-color: rgba(0, 89, 255, 0.05);
        }

        span {
          margin-right: 5px;
          position: relative;
          z-index: 200;
        }
      }
    }
  }

  .emptyShop {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    color: #666;
    font-size: 16px;
  }
}