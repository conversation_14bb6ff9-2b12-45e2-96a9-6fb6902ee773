import React, { useEffect, useState } from 'react';
import { activityDataWarningExportListExport, activityDataWarningPageList } from '@/api/b';
import dayjs from 'dayjs';
import { Box, Button, DatePicker2, Input, Message, Pagination, Table } from '@alifd/next';
import { downloadExcel, maskSensitiveInfo } from '@/utils';
import HelpTooltip from '@/components/HelpTooltip/HelpTooltip';
import Container from '@/components/Container';

export default function Warning() {
  // 查询条件
  const [orderId, setOrderId] = useState<any>('');
  const [ouid, setOuid] = useState<any>('');
  const [activityName, setActivityName] = useState<any>('');
  const [timeRange, setTimeRange] = useState<any>([]);

  // 表格相关
  const [tableData, setTableData] = useState<any>([]);
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState<any>(0);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchData().then();
  }, []);

  const fetchData = async (params?: any) => {
    setLoading(true);
    try {
      const [start, end] = timeRange;
      console.log(start, end);
      // 接口的VO有问题
      const queryParams = {
        openId: '',
        activityName: '',
        receiveStartTime: '',
        receiveEndTime: '',
        tid: '',
        pageNum,
        pageSize,
      };
      queryParams.openId = ouid;
      queryParams.activityName = activityName;
      queryParams.receiveStartTime = start ? dayjs(start).format('YYYY-MM-DD 00:00:00') : '';
      queryParams.receiveEndTime = end ? dayjs(end).format('YYYY-MM-DD 23:59:59') : '';
      queryParams.tid = orderId;
      const { records, total }: any = await activityDataWarningPageList({
        ...queryParams, ...params,
      });
      setTableData(records || []);
      setTotal(total || 0);
    } catch (e) {
      Message.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async () => {
    setLoading(true);
    try {
      const [start, end] = timeRange;
      const data: any = await activityDataWarningExportListExport({
        openId: ouid,
        activityName,
        receiveStartTime: start ? dayjs(start).format('YYYY-MM-DD 00:00:00') : '',
        receiveEndTime: end ? dayjs(end).format('YYYY-MM-DD 23:59:59') : '',
        tid: orderId,
        pageNum,
        pageSize,
      });
      downloadExcel(data, `预警提醒${dayjs().format('YYYY-MM-DD HH:mm')}`);
    } catch (e) {
      Message.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    setPageNum(1);
    await fetchData({
      pageNum: 1,
    });
  };

  const handleReset = async () => {
    const queryParams = {
      openId: '',
      activityName: '',
      receiveStartTime: '',
      receiveEndTime: '',
      tid: '',
      pageNum: 1,
      pageSize: 10,
    };
    setPageNum(1);
    setPageSize(10);
    setOuid('');
    setActivityName('');
    setOrderId('');
    setTimeRange([]);
    await fetchData(queryParams);
  };

  const handlePageChange = async (current) => {
    setPageNum(current);
    await fetchData({
      pageNum: current,
    });
  };

  const handlePageSizeChange = (size) => {
    setPageSize(size);
    setPageNum(1);
    fetchData({
      pageNum: 1,
      pageSize: size,
    });
  };

  return (
    <Container>
      <Box spacing={16}>
        {/* 筛选表单 */}
        <Box direction="row" spacing={10} align="center">
          <Input label="openId" value={ouid} onChange={setOuid} />
          <Input label="订单编号" value={orderId} onChange={setOrderId} />
          <Input label="活动名称" value={activityName} onChange={setActivityName} />
          <DatePicker2.RangePicker label="领取时间" value={timeRange} onChange={setTimeRange} />
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
          <Button type="normal" onClick={handleReset}>
            重置
          </Button>
          <Button type="normal" onClick={handleExport}>
            导出
          </Button>
        </Box>

        <Table dataSource={tableData} hasBorder loading={loading}>
          <Table.Column title="活动名称" width={120} dataIndex="activityName" />
          <Table.Column
            title="用户昵称"
            width={85}
            dataIndex="nickname"
            cell={(value) => {
              return value || '-';
            }}
          />
          <Table.Column
            title="openId"
            width={70}
            dataIndex="openId"
            cell={(value) => {
              return maskSensitiveInfo(value) || '-';
            }}
          />
          <Table.Column
            title="参与活动时间"
            width={150}
            dataIndex="participateTime"
            cell={(value) => {
              return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
            }}
          />
          <Table.Column title="订单编号" dataIndex="tid" />
          <Table.Column
            title="下单时间"
            width={150}
            dataIndex="orderCreateTime"
            cell={(value) => {
              return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
            }}
          />
          <Table.Column title="订单状态" width={85} dataIndex="orderStatusStr" />
          <Table.Column
            title="领奖时间"
            width={150}
            dataIndex="receiveTime"
            cell={(value) => {
              return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
            }}
          />
          <Table.Column title="奖品类型" dataIndex="lotteryTypeStr" />
          <Table.Column title="奖品名称" dataIndex="lotteryName" />
          <Table.Column
            title="领取状态"
            width={85}
            dataIndex="receiveStatusStr"
            cell={(value) => {
              return <div className={value === '领取成功' ? 'status-normal' : 'status-red'}>{value}</div>;
            }}
          />
          <Table.Column title="备注" dataIndex="remark" />
          <Table.Column
            title={
              <Box direction="row" align="center" spacing={5}>
                <span>预警说明</span>
                <HelpTooltip
                  content={
                    '针对【实物（备注到订单）奖品】若满足条件的订单【付款时间】到【奖品发放时间】间隔大于20s则会纳入预警统计中，可能存在订单备注已打而OMS审单已结束无法随单发奖的情况'
                  }
                />
              </Box>
            }
            dataIndex="warningStatement"
            cell={(value) => {
              return <div dangerouslySetInnerHTML={{ __html: value }} style={{ color: 'var(--primary-text-red)' }} />;
            }}
          />
        </Table>

        <Box direction="row" justify="flex-end">
          <Pagination
            current={pageNum}
            pageSize={pageSize}
            total={total}
            shape={'arrow-only'}
            totalRender={(total) => `共 ${total} 条`}
            onChange={(p) => handlePageChange(p)}
            onPageSizeChange={(size) => {
              handlePageSizeChange(size);
            }}
            pageSizeSelector="dropdown"
            pageSizePosition="end"
          />
        </Box>
      </Box>
    </Container>
  );
}
