import { Dialog } from '@alifd/next';
import React from 'react';
import CreateAccount from '@/pages/system/account/components/CreateAccount/index';
import { UserListResponse } from '@/api/types';
import { addDialogRef } from '@/utils/dialogMapper';

export default function openCreateAccountModal(initialValues: UserListResponse) {
  const isEditMode = initialValues && initialValues?.id;

  return new Promise(resolve => {
    const dialogRef = Dialog.show({
      v2: true,
      title: isEditMode ? '分配角色' : '新建账号',
      width: 600,
      centered: true,
      content: (
        <CreateAccount
          initialValues={initialValues}
          onResolve={result => {
            resolve(result);
            dialogRef.hide();
          }}
        />
      ),
      footer: false,
    });
    addDialogRef(dialogRef);
  });
}
