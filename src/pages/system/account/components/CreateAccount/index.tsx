import React, { useEffect, useState } from 'react';
import { Form, Field, Input, Button, Box, Message, Table, Radio, Loading } from '@alifd/next';
import { getRoleList } from '@/api/role';
import { userAdd, userUpdate } from '@/api/user';
import openCreateRoleModal from '@/pages/system/role/components/CreateRole/utils';
import { RoleListResponse, UserListResponse } from '@/api/types'; // 新建角色弹窗入口

export default function CreateAccount({ initialValues, onResolve }) {
  // 使用 Field 管理表单，初始值由传入数据决定
  const createAccountField = Field.useField({ values: initialValues || {} });
  const [loading, setLoading] = useState(false);

  // 角色列表数据（下拉或表格展示）
  const [roles, setRoles] = useState<RoleListResponse[]>([]);
  // 当前选中的 roleId，用 Table 单选选择
  const [selectedRoleId, setSelectedRoleId] = useState(
    initialValues && initialValues.roleId ? String(initialValues.roleId) : null,
  );

  // 判断是否为编辑（分配角色）模式：如果 initialValues 中存在 accountId，则为编辑模式
  const isEditMode = initialValues && initialValues.id;


  // 加载角色列表
  useEffect(() => {
    loadRoles().then();
  }, []);

  // 获取角色列表
  async function loadRoles() {
    setLoading(true);
    try {
      const res = await getRoleList({ pageNum: 1, pageSize: 9999, status: 1 });
      setRoles(res.records || []);
    } catch (err) {
      console.error('loadRoles error:', err);
      Message.error('获取角色列表失败');
    } finally {
      setLoading(false);
    }
  }

  // 点击“保存”
  const handleOk = () => {
    createAccountField.validate(async (errors, values: UserListResponse) => {
      if (errors) return;
      if (!selectedRoleId) {
        Message.error('请选择一个角色');
        return;
      }
      // 如果编辑模式，则调用 saveAccountRole 分配角色
      if (isEditMode) {
        try {
          setLoading(true);
          await userUpdate({
            ...initialValues,
            roleId: selectedRoleId,
          });
          Message.success('分配角色成功');
          onResolve(true);
        } catch (err) {
          Message.error('分配角色失败');
        } finally {
          setLoading(false);
        }
      } else {
        // 新建账号模式，调用 addAccount 接口
        const { account, mobile } = values;
        setLoading(true);
        try {
          await userAdd({
            account,
            mobile,
            roleId: selectedRoleId,
          });
          Message.success('新建账号成功');
          onResolve(true);
        } catch (err) {
          Message.error(err.message);
        } finally {
          setLoading(false);
        }
      }
    });
  };

  // 点击“取消”
  const handleCancel = () => {
    onResolve(null);
  };

  return (
    <Loading visible={loading}>
      <Form field={createAccountField}>
        <Form.Item
          name="account"
          label="账号名称"
          required={!isEditMode}
          disabled={isEditMode}
          requiredMessage="账号名称不能为空"
          style={{ marginBottom: 0 }}
        >
          <Input placeholder="请输入账号名称" maxLength={50} showLimitHint />
        </Form.Item>
        <Form.Item
          name="mobile"
          label="手机号"
          required={!isEditMode}
          pattern={/^1[3456789]\d{9}$/}
          patternMessage="请输入正确的手机号"
          requiredMessage="手机号不能为空"
          disabled={isEditMode}
          style={{ marginBottom: 0 }}
        >
          <Input
            trim
            maxLength={11}
            placeholder="请输入账号手机号"
          />
        </Form.Item>
      </Form>
      <Box
        direction="row"
        align="center"
        padding={[5, 0]}
        justify="space-between"
      >
        <Form.Item
          name="mobile"
          label="选择角色"
          required
          style={{ marginBottom: 0 }}
        />
        <Button
          text
          type="primary"
          onClick={async () => {
            const result = await openCreateRoleModal();
            if (result) {
              await loadRoles();
            }
          }}
        >
          新建角色
        </Button>
      </Box>
      <Table
        dataSource={roles}
        fixedHeader
        maxBodyHeight={300}
        hasBorder
      >
        {/* 单选按钮列，用于选择一个角色 */}
        <Table.Column
          title=""
          align="center"
          width={60}
          cell={(value, index, record) => (
            <div>
              <Radio
                checked={String(record.id) === String(selectedRoleId)}
                onChange={() => setSelectedRoleId(String(record.id))}
              />
            </div>

          )}
        />
        <Table.Column
          title="角色名称"
          dataIndex="title"
        />
        <Table.Column
          title="角色描述"
          dataIndex="remark"
        />
      </Table>
      <Box
        direction="row"
        margin={[10, 0, 0, 0]}
        justify="center"
        spacing={10}
      >
        <Button onClick={handleCancel}>取消</Button>
        <Button
          type="primary"
          onClick={handleOk}
          disabled={loading}
          loading={loading}
        >
          保存
        </Button>
      </Box>
    </Loading>
  );
}
