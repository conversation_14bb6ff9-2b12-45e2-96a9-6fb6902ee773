import { Input, Button, Select, Table, Box, Switch, Pagination, Message, Dialog } from '@alifd/next';
import { useEffect, useState } from 'react';
import createAccount from '@/pages/system/account/components/CreateAccount/util';
import { getUserList, userDel, userUpdate } from '@/api/user';
import { getRoleList } from '@/api/role';
import { RoleListResponse, UserListResponse } from '@/api/types';
import dayjs from 'dayjs';
import { ObjectItem } from '@alifd/next/lib/select/types';
import Container from '@/components/Container';
import { addDialogRef } from '@/utils/dialogMapper';
import { maskSensitiveInfo } from '@/utils';

interface RoleList extends ObjectItem{
  label: string;
  value: string;
}

export default function AccountManagement() {
  const [userList, setUserList] = useState<UserListResponse[]>([]);
  const [roleList, setRoleList] = useState<RoleList[]>([]);
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [account, setAccount] = useState('');
  const [roleId, setRoleId] = useState('');
  const [status, setStatus] = useState<number>(-1);
  const [loading, setLoading] = useState(false);

  const handleSaveAccountRole = async (record: UserListResponse) => {
    const { id, account, mobile, roleId } = record;
    const result = await createAccount({
      id,
      account,
      mobile,
      roleId,
    });
    if (result) {
      await fetchUserList();
    }
  };


  // 获取账号列表
  const fetchUserList = async (params?: any) => {
    setLoading(true);
    try {
      const res = await getUserList({
        account,
        pageNum,
        pageSize,
        roleId,
        status,
        ...params,
      });
      setTotal(+res.total!);
      setUserList(res.records!);
    } catch (e) {
      Message.error(e.message);
    } finally {
      setLoading(false);
    }
  };
  // 获取角色列表
  const fetchRoleList = async () => {
    const { records } = await getRoleList({
      pageNum: 1,
      pageSize: 999,
      status: 1,
    });
    const list = records?.map((e: RoleListResponse) => {
      return {
        label: e.title!,
        value: e.id!,
      };
    });
    setRoleList(list || []);
  };
  // 删除账号
  const handleRemoveAccount = async (record: UserListResponse) => {
    const { id } = record;
    if (record.status) {
      Message.error('已启用账户不可删除');
      return;
    }
    const dialogRef = Dialog.confirm({
      title: '提示',
      content: '确定删除该账号吗？',
      onOk: async () => {
        setLoading(true);
        try {
          await userDel({
        id,
      });
      Message.success('删除成功');
      await fetchUserList({ pageNum: 1 });
    } catch (e) {
      Message.error(e.message);
    } finally {
          setLoading(false);
        }
      },
    });
    addDialogRef(dialogRef);
  };
  // 渲染时间
  const renderDate = (value: Date) => (value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '--');
  // 创建账号
  const handleCreateAccount = async () => {
    const result = await createAccount({ account: '', mobile: '' });
    if (result) {
      // 刷新账号列表
      await fetchUserList();
    }
  };
  // 分页
  const handlePageChange = (current: number) => {
    setPageNum(current);
    fetchUserList({
      pageNum: current,
    });
  };
  // 分页大小
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setPageNum(1);
    fetchUserList({
      pageNum: 1,
      pageSize: 10,
    });
  };

  // 点击查询
  const handleSearch = async () => {
    setPageNum(1);
    await fetchUserList({
      pageNum: 1,
    });
  };

  // 重置
  const handleReset = async () => {
    setAccount('');
    setStatus(-1);
    setRoleId('');
    setPageNum(1);
    setPageSize(10);
    await fetchUserList({
      pageNum: 1,
      roleId: '',
      account: '',
      pageSize: 10,
      status: -1,
    });
  };

  // 切换状态
  const handleSwitchStatus = async (record: UserListResponse, val: boolean) => {
    const status = val ? 1 : 0;

    const doSwitch = async () => {
      try {
        await userUpdate({ ...record, status });
        Message.success('状态切换成功');
        await fetchUserList();
      } catch (err) {
        console.error('switchAccountEnabled error:', err);
        Message.error(err.message);
      }
    };
    if (val) {
      await doSwitch();
    } else {
      const dialogRef = Dialog.confirm({
        title: '提示',
        content: '停用后该账户将无法登录系统，是否继续？',
        onOk: () => {
          doSwitch();
        },
      });
      addDialogRef(dialogRef);
    }
  };

  useEffect(() => {
    fetchUserList().then();
    fetchRoleList().then();
  }, []);

  return (
    <Container>
      <Box spacing={16}>
        <Box direction="row" justify="space-between" spacing={10}>
          <Box direction="row" spacing={15}>
            <Input label="账号名称" placeholder="请输入账号名称" style={{ width: 300 }} value={account} onChange={(val: string) => setAccount(val)} />
            <Select
              label="状态"
              style={{ width: 160 }}
              value={status}
              onChange={(val: number) => setStatus(val)}
              dataSource={[
                { label: '全部', value: -1 },
                { label: '启用', value: 1 },
                { label: '停用', value: 0 },
              ]}
            />
            <Select
              label="角色"
              style={{ width: 160 }}
              value={roleId}
              onChange={(val: string) => setRoleId(val)}
              dataSource={[{ label: '全部', value: '' }, ...roleList]}
            />
            <Button
              type="primary"
              onClick={handleSearch}
            >
              查询
            </Button>
            <Button
              type="normal"
              onClick={handleReset}
            >
              重置
            </Button>
          </Box>
          <Button type="primary" onClick={handleCreateAccount}>新建账号</Button>
        </Box>
        <Box>
          <Table
            dataSource={userList}
            loading={loading}
            hasBorder
          >
            <Table.Column
              title="账号名称"
              dataIndex="account"
            />
            <Table.Column dataIndex={'roleName'} title="角色" />
            <Table.Column
              dataIndex={'mobile'}
              title="手机号"
              cell={(value) => {
                return maskSensitiveInfo(value, 3, 4) || '-';
              }}
            />
            <Table.Column
              title="账号状态"
              dataIndex="status"
              cell={(value, _index, record) => {
                const checked = value == 1;
                return (
                  <Switch
                    checked={checked}
                    onChange={(val) => handleSwitchStatus(record, val)}
                  />
                );
              }}
            />
            <Table.Column title="创建时间" dataIndex="createTime" cell={renderDate} />
            <Table.Column title="更新时间" dataIndex="updateTime" cell={renderDate} />
            <Table.Column
              title="操作"
              cell={(_value, _index, record) => (
                <Box
                  direction="row"
                  spacing={10}
                >
                  <Button type="primary" text onClick={() => handleSaveAccountRole(record)}>分配角色</Button>
                  <Button type="primary" text onClick={() => handleRemoveAccount(record)}>删除</Button>
                </Box>
              )}
            />
          </Table>
        </Box>
        <Box direction="row" justify="flex-end">
          <Pagination
            current={pageNum}
            pageSize={pageSize}
            shape={'arrow-only'}
            total={total}
            totalRender={total => `共${total}条`}
            pageSizeSelector="dropdown"
            pageSizePosition="end"
            onPageSizeChange={handlePageSizeChange}
            onChange={handlePageChange}
          />
        </Box>
      </Box>
    </Container>

  );
}
