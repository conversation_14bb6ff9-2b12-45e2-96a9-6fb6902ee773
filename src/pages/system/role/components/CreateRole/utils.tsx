import { Dialog } from '@alifd/next';
import React from 'react';
import CreateR<PERSON> from './index';
import { addDialogRef } from '@/utils/dialogMapper';

export default function openCreateRoleModal(initialValues = {}) {
  return new Promise(resolve => {
    const dialogRef = Dialog.show({
      v2: true,
      title: '新建角色',
      width: 500,
      centered: true,
      closeMode: ['close'],
      content: (
        <CreateRole
          initialValues={initialValues}
          onResolve={result => {
            resolve(result);
            dialogRef.hide();
          }}
        />
      ),
      footer: false,
    });
    addDialogRef(dialogRef);
  });
}
