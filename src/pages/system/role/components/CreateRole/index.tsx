import React from 'react';
import { Form, Field, Input, Button, Box, Message } from '@alifd/next';
import { roleAdd } from '@/api/role';
import { FieldValues } from '@alifd/next/types/field';

interface FormData extends FieldValues {
  title: string;
  remark: string;
}
export default function CreateRole({ initialValues, onResolve }) {
  const createRoleField = Field.useField({ values: initialValues });
  const [loading, setLoading] = React.useState(false);

  const handleOk = () => {
    createRoleField.validate(async (errors, values: FormData) => {
      if (!errors) {
        const { title, remark } = values;
        setLoading(true);
        try {
          await roleAdd({ title, remark });
          Message.success('新建角色成功');
          onResolve(true);
        } catch (err) {
          console.error('addRole error:', err);
          Message.error('新建角色失败');
        } finally {
          setLoading(false);
        }
      }
    });
  };

  const handleCancel = () => {
    onResolve(false);
  };

  return (
    <Box>
      <Form
        field={createRoleField}
        className="create-role-form"
      >
        <Form.Item
          name="title"
          label="角色名称"
          required
          requiredMessage="角色名称不能为空"
        >
          <Input maxLength={50} showLimitHint placeholder="请输入角色名称" />
        </Form.Item>

        <Form.Item
          name="remark"
          label="角色描述"
        >
          <Input.TextArea
            placeholder="请输入角色描述"
            rows={4}
            maxLength={200}
            showLimitHint
          />
        </Form.Item>

        <Box
          direction="row"
          justify="center"
          spacing={10}
          margin={[20, 0, 0, 0]}
        >
          <Button onClick={handleCancel}>取消</Button>
          <Button
            type="primary"
            disabled={loading}
            loading={loading}
            onClick={handleOk}
          >
            保存
          </Button>
        </Box>
      </Form>
    </Box>
  );
}
