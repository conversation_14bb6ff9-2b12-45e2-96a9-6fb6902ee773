import React, { useState, useEffect } from 'react';
import { Box, Tree, Button, Loading, Message } from '@alifd/next';
import { getMyMenuList, permissionList } from '@/api/permission';
import { roleBindMenu } from '@/api/role';
import { permissionListByRoleId } from '@/api/permission';
import { MenuListResponse, PermissionListResponse } from '@/api/types';

interface MenuItem extends MenuListResponse {
  isRole?: boolean;
  function?: string[];
}

export default function AssignPermissionsModalContent({ roleId, onResolve }) {
  // 转换后给 Tree 使用的 dataSource
  const [treeData, setTreeData] = useState<any>([]);
  // 当前勾选的节点key
  const [checkedKeys, setCheckedKeys] = useState <string[]>([]);
  // 功能list
  const [functionList, setFunctionList] = useState<PermissionListResponse[]>([]);

  const [loading, setLoading] = useState(false);

  const [allCheckedKeys, setAllCheckedKeys] = useState<string[]>([]);

  useEffect(() => {
    loadResourceData().then();
  }, [roleId]);


  /**
   * 根据menuList和roleList生成树形结构
   */
  function generateMenuTree(menuList: MenuItem[], roleList: PermissionListResponse[]) {
    // 深拷贝menuList，避免修改原始数据
    const result = JSON.parse(JSON.stringify(menuList));

    // 递归处理菜单项
    function processMenu(menuItems: MenuItem[]) {
      if (!menuItems || !menuItems.length) return;

      for (const menuItem of menuItems) {
        // 检查当前菜单项的keyName是否在roleList中存在
        if (menuItem.keyName) {
          const matchedRoles = roleList.filter(role =>
            role.key!.startsWith(`${menuItem.keyName}-`) ||
            role.key!.startsWith(`${menuItem.keyName}_`),
          );

          if (matchedRoles.length > 0) {
            // 确保children是数组
            if (!menuItem.children) {
              menuItem.children = [];
            }

            // 将匹配的角色添加到children中
            matchedRoles.forEach(role => {
              !menuItem.children && (menuItem.children = []);
              menuItem.children.push({
                id: role.functionId,
                title: role.name,
                keyName: role.key,
                children: [],
                parentId: menuItem.id?.toString(),
                // 其他默认属性
                checkboxDisabled: false,
                url: '',
                // 标记为角色项，避免递归处理
                isRole: true,
              } as MenuItem);
            });
          }
        }

        // 递归处理子菜单，但不处理角色项
        if (menuItem.children && menuItem.children.length > 0 && !menuItem.isRole) {
          processMenu(menuItem.children);
        }
      }
    }
    processMenu(result);
    return result;
  }

  /**
   * 将树形结构中的字段名称进行转换
   */
  function transformTreeFieldNames(treeData: MenuItem[]) {
    if (!treeData || !Array.isArray(treeData)) {
      return [];
    }

    return treeData.map(item => {
      // 创建新对象并进行字段转换
      const newItem = {
        ...item,
        label: item.title, // title 变为 label
        key: String(item.id), // id 变为 key，并确保转为字符串类型
      };

      // 删除原有字段
      delete newItem.title;
      delete newItem.id;

      // 递归处理子节点
      if (newItem.children && Array.isArray(newItem.children) && newItem.children.length > 0) {
        newItem.children = transformTreeFieldNames(newItem.children);
      }

      return newItem;
    });
  }


  /**
   * 生成符合要求的最终树形结构
   */
  function generateFinalMenuTree(menuList: MenuItem[], roleList: PermissionListResponse[]) {
    // 先生成基本树形结构
    const baseTree = generateMenuTree(menuList, roleList);

    // 然后转换字段名称
    return transformTreeFieldNames(baseTree);
  }


  /**
   * 将选中的ID分组为菜单ID和角色ID
   */
  function groupSelectedIds(selectedIds: string[], roleList: PermissionListResponse[]) {
    if (!selectedIds || !Array.isArray(selectedIds) || selectedIds.length === 0) {
      return { menuIdList: [], functionIdList: [] };
    }

    // 提取roleList中的所有ID作为一个集合，用于快速查找
    const roleIdSet = new Set(roleList.map(role => `${role.functionId}`));

    // 分组结果
    const result = {
      menuIdList: [] as string[],
      functionIdList: [] as string[],
    };

    // 遍历选中的ID进行分组
    selectedIds.forEach(id => {
      if (roleIdSet.has(id.toString())) {
        // 如果ID存在于roleList中，则归类为角色ID
        result.functionIdList.push(id.toString());
      } else {
        // 否则归类为菜单ID
        result.menuIdList.push(id.toString());
      }
    });

    return {
      functionIdList: [...new Set(result.functionIdList)],
      menuIdList: [...new Set(result.menuIdList)],
    };
  }

  const isLeaf = (tree, key) => {
    const keys: string[] = [];
    const getChildKey = (arr) => {
      arr.forEach((child) => {
        if (child?.children && child.children.length > 0) {
          getChildKey(child?.children);
        } else {
          keys.push(child.key);
        }
      });
    };
    getChildKey(tree);
    return keys.includes(key);
  };

  /**
   * 加载数据
   */
  const loadResourceData = async () => {
    setLoading(true);
    try {
      const menuTree = await getMyMenuList();
      const _functionList = await permissionList();
      setFunctionList(_functionList);
      const result = generateFinalMenuTree(menuTree, _functionList);
      setTreeData(result);
      const checkedIdList = await permissionListByRoleId({ roleId });
      setAllCheckedKeys(checkedIdList);
      setCheckedKeys(checkedIdList.map(e => e.toString()));
    } catch (e) {
      Message.error('获取权限失败');
    } finally {
      setLoading(false);
    }
  };


  /**
   * 当 Tree 勾选变化时
   */
  const handleCheck = (keys: string[], extra: any) => {
    const newCheckedKeys = new Set(keys);
    const { node, checked } = extra;
    const key = node.props.eventKey;


    // 构建父子映射
    const parentMap: any = {};
    const childMap: any = {};
    const buildMaps = (nodes, parent = null) => {
      nodes.forEach(item => {
        parentMap[item.key] = parent;
        if (parent) {
          childMap[parent] = childMap[parent] || [];
          childMap[parent].push(item.key);
        }
        if (item.children) {
          buildMaps(item.children, item.key);
        }
      });
    };
    buildMaps(treeData);

    // ✅ 勾选时
    if (checked) {
      // 向上联动：兄弟节点全勾选 → 勾选父节点
      let current = key;
      while (parentMap[current]) {
        const parent = parentMap[current];
        const siblings = childMap[parent] || [];
        const allSiblingsChecked = siblings.every(s => newCheckedKeys.has(s));
        if (allSiblingsChecked) {
          newCheckedKeys.add(parent);
        }
        current = parent;
      }

      // 向下联动：勾选父节点 → 勾选所有子节点
      const collectChildren = k => {
        const children = childMap[k] || [];
        children.forEach(child => {
          newCheckedKeys.add(child);
          collectChildren(child);
        });
      };
      collectChildren(key);
    } else {
      // ❌ 取消勾选时：取消父节点 → 取消所有子节点
      const removeChildren = k => {
        const children = childMap[k] || [];
        children.forEach(child => {
          newCheckedKeys.delete(child);
          removeChildren(child);
        });
      };
      removeChildren(key);
    }

    setCheckedKeys(Array.from(newCheckedKeys));
    setAllCheckedKeys([...Array.from(newCheckedKeys), ...extra.indeterminateKeys]);
    // setCheckedKeys(keys);
  };

  /**
   * 快速开启/关闭全部权限
   */
  const handleEnableAll = () => {
    // 递归收集resourceId
    const allKeys: any = [];
    // 深度优先遍历树形结构
    const dfs = (nodes: any[]) => {
      nodes.forEach((n: any) => {
        allKeys.push(n.key);
        if (n.children && n.children.length > 0) {
          dfs(n.children);
        }
      });
    };
    dfs(treeData);
    setAllCheckedKeys(allKeys);
    setCheckedKeys(allKeys);
  };
  const handleDisableAll = () => {
    setCheckedKeys([]);
    setAllCheckedKeys([]);
  };

  // ====== 点击"确定"保存 ======
  const handleSave = async () => {
    const groupedIds = groupSelectedIds(allCheckedKeys, functionList);
    const { menuIdList, functionIdList } = groupedIds;

    try {
      setLoading(true);
      await roleBindMenu({
        menuList: [...new Set(menuIdList)],
        functionList: [...new Set(functionIdList)],
        roleId,
      });
      Message.success('分配权限成功');
      onResolve(true);
    } catch (e) {
      Message.error(e.message);
    } finally {
      setLoading(false);
    }
    console.log('分组结果:', groupedIds, roleId);
  };

  // ====== 点击"取消" ======
  const handleCancel = () => {
    onResolve(null);
  };

  // 递归收集所有节点的 key
  const getAllKeys = (nodes = []) => {
    const keys: any[] = [];
    const traverse = list => {
      list.forEach(node => {
        keys.push(node.key);
        if (node.children && node.children.length > 0) {
          traverse(node.children);
        }
      });
    };
    traverse(nodes);
    return keys;
  };

  return (
    <Box spacing={15}>
      <Box
        direction="row"
        justify="center"
        spacing={16}
      >
        <Button
          type="primary"
          onClick={handleEnableAll}
        >
          开启全部权限
        </Button>
        <Button
          type="normal"
          onClick={handleDisableAll}
        >
          关闭全部权限
        </Button>
      </Box>
      <Loading visible={loading} style={{ minHeight: '400px', width: '100%' }} >
        <Tree
          checkable
          showLine
          checkedKeys={checkedKeys}
          onCheck={handleCheck}
          dataSource={treeData}
          checkStrictly
          expandedKeys={getAllKeys(treeData)}
        />
      </Loading>
      <Box
        direction="row"
        justify="center"
        spacing={16}
      >
        <Button onClick={handleCancel}>取消</Button>
        <Button
          type="primary"
          onClick={handleSave}
        >
          保存
        </Button>
      </Box>
    </Box>
  );
}
