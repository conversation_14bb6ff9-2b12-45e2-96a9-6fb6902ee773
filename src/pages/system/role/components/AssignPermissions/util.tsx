import { Dialog } from '@alifd/next';
import React from 'react';
import AssignPermissionsModalContent from './index';
import { addDialogRef } from '@/utils/dialogMapper';

export default function openAssignPermissionsModal(roleId) {
  return new Promise(resolve => {
    const dialogRef = Dialog.show({
      v2: true,
      title: '分配权限',
      width: 500,
      centered: true,
      closeMode: ['close'],
      content: (
        <AssignPermissionsModalContent
          roleId={roleId}
          onResolve={result => {
            resolve(result);
            dialogRef.hide();
          }}
        />
      ),
      footer: false,
    });
    addDialogRef(dialogRef);
  });
}
