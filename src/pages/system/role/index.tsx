import React, { useState, useEffect } from 'react';
import { Box, Input, Select, Button, Table, Switch, Pagination, Message, Dialog } from '@alifd/next';
import dayjs from 'dayjs';
import openCreateRoleModal from '@/pages/system/role/components/CreateRole/utils';
import openAssignPermissionsModal from '@/pages/system/role/components/AssignPermissions/util';
import { getRoleList, roleDel, roleUpdate } from '@/api/role';
import { RoleListResponse } from '@/api/types';
import Container from '@/components/Container';
import { addDialogRef } from '@/utils/dialogMapper';

export default function AccountManagementIndexPage() {
  // 查询条件状态
  const [title, setTitle] = useState('');
  const [status, setStatus] = useState(-1);
  // 表格数据及分页相关状态
  const [roleList, setRoleList] = useState<RoleListResponse[]>([]);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchRoleList().then();
  }, []);

  // 获取角色列表
  const fetchRoleList = async (params?: any) => {
    setLoading(true);
    try {
      const res = await getRoleList({
        pageNum: pageNo,
        pageSize,
        title,
        status,
        ...params,
      });
      setTotal(+res.total!);
      setRoleList(res.records || []);
    } catch (err) {
      console.error('fetchRoleList error:', err);
      Message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  // 查询按钮点击
  const handleSearch = async () => {
    setPageNo(1);
    await fetchRoleList({
      pageNum: 1,
    });
  };

  // 重置按钮点击逻辑：清空查询条件，并刷新列表
  const handleReset = async () => {
    setTitle('');
    setStatus(-1);
    setPageNo(1);
    setPageSize(10);
    await fetchRoleList({
      pageNum: 1,
      pageSize: 10,
      title: '',
      status: -1,
    });
  };

  // 切换角色状态的开关
  const handleSwitchChange = async (record, val) => {
    const status = val ? 1 : 0;
    try {
      await roleUpdate({ ...record, status });
      Message.success('状态切换成功');
      await fetchRoleList();
    } catch (err) {
      Message.error(err.message);
    }
  };

  // 分页切换回调
  const handlePageChange = current => {
    setPageNo(current);
    fetchRoleList({
      pageNum: current,
    });
  };
  const handlePageSizeChange = size => {
    setPageSize(size);
    setPageNo(1);
    fetchRoleList({
      pageSize: size,
    });
  };

  // 日期格式化函数
  const renderDate = value => (value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '--');

  // 创建角色
  const handleCreateRole = async () => {
    const result = await openCreateRoleModal();
    if (result) {
      // 刷新列表
      await fetchRoleList();
    }
  };

  // 删除角色
  const handleDeleteRole = async (record: any) => {
    const { id, status } = record;
    if (status) {
      Message.error('已启用角色不可删除');
      return;
    }
    const dialogRef = Dialog.confirm({
      title: '提示',
      content: '确定删除该角色吗？',
      onOk: async () => {
        try {
          await roleDel({
            id,
          });
          await fetchRoleList({ pageNum: 1 });
          Message.success('删除角色成功');
        } catch (e) {
          Message.error(e.message);
        }
      },
    });
    addDialogRef(dialogRef);
  };

  return (
    <Container>
      <Box
        spacing={16}
        style={{ height: '100%' }}
      >
        {/* 查询区域 */}
        <Box
          direction="row"
          justify="space-between"
          spacing={10}
        >
          <Box
            direction="row"
            spacing={10}
          >
            <Input
              label="角色名称"
              placeholder="请输入角色名称"
              value={title}
              onChange={(val: string) => setTitle(val)}
              style={{ width: 300 }}
            />
            <Select
              label="状态"
              style={{ width: 120 }}
              dataSource={[
                { label: '全部', value: -1 },
                { label: '启用', value: 1 },
                { label: '停用', value: 0 },
              ]}
              placeholder="状态"
              value={status}
              onChange={(val: number) => setStatus(val)}
            />
            <Button
              type="primary"
              onClick={handleSearch}
            >
              查询
            </Button>
            <Button
              type="normal"
              onClick={handleReset}
            >
              重置
            </Button>
          </Box>
          <Button
            type="primary"
            onClick={handleCreateRole}
          >
            新建角色
          </Button>
        </Box>
        {/* 表格 */}
        <Box style={{ flex: 1, overflow: 'auto' }}>
          <Table
            dataSource={roleList}
            primaryKey="id"
            loading={loading}
            hasBorder
          >
            <Table.Column
              title="角色名称"
              dataIndex="title"
            />
            <Table.Column
              title="角色描述"
              dataIndex="remark"
            />
            <Table.Column
              title="角色状态"
              dataIndex="status"
              cell={(value, index, record) => {
                const checked = value == 1;
                return (
                  <Switch
                    checked={checked}
                    onChange={val => handleSwitchChange(record, val)}
                  />
                );
              }}
            />
            <Table.Column
              title="创建时间"
              dataIndex="createTime"
              cell={renderDate}
            />
            <Table.Column
              title="更新时间"
              dataIndex="updateTime"
              cell={renderDate}
            />
            <Table.Column
              title="操作"
              cell={(value, index, record) => {
                if (record.title === '超级管理员') {
                  return '--';
                }
                return (
                  <Box
                    direction="row"
                    spacing={10}
                  >
                    <Button
                      type="primary"
                      text
                      onClick={async () => {
                        await openAssignPermissionsModal(record.id);
                      }}
                    >
                      分配权限
                    </Button>
                    <Button
                      type="primary"
                      text
                      onClick={() => handleDeleteRole(record)}
                    >
                      删除
                    </Button>
                  </Box>
                );
              }}
            />
          </Table>
        </Box>
        <Box direction="row" justify="flex-end">
          <Pagination
            current={pageNo}
            shape={'arrow-only'}
            pageSize={pageSize}
            total={total}
            totalRender={total => `共${total}条`}
            pageSizeSelector="dropdown"
            pageSizePosition="end"
            onPageSizeChange={handlePageSizeChange}
            onChange={handlePageChange}
          />
        </Box>

      </Box>
    </Container>
  );
}
