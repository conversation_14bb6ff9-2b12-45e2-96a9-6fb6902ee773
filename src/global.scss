$GAP: 15px;

:root {

  --primary-color: rgba(22, 119, 255, 1);
  --primary-color-light: rgba(22, 119, 255, 0.5);
  --primary-color-lighter: rgba(22, 119, 255, 0.1);

  --primary-cyan: rgba(22, 119, 255, 1);
  --primary-cyan-light: rgba(22, 119, 255, 0.5);
  --primary-cyan-lighter: rgba(22, 119, 255, 0.1);

  --primary-black: rgba(17, 17, 17);
  --primary-black-light: rgba(17, 17, 17, 0.5);
  --primary-black-lighter: rgba(17, 17, 17, 0.1);

  --primary-color-dark: rgb(22, 119, 255, .2);
  --primary-color-darker: rgb(22, 119, 255, .1);

  --primary-text-red: rgb(251, 59, 80);

  --color-background: #f7f8fa;
  --color-background-light: #fcfcfc;
  --color-background-widget: #f5f8ff;
  --color-border: #e6e9ed;
  --color-border-light: #f0f2f5;
  --color-text-deep: #111;
  --color-text-primary: #333;
  --color-text-regular: #666;
  --color-text-secondary: #999;
  --color-text-placeholder: #ccc;
  --color-text-reverse: #fff;
  --color-text-background: #eff5ff;
  --font-size-extra-large: 20px;
  --font-size-large: 18px;
  --font-size-medium: 16px;
  --font-size-base: 14px;
  --font-size-small: 12px;
  --activity-form-item-width: 400px;
  --card-body-hide-divider-padding-top: 12px;
  --table-normal-border-color: var(--color-border);
}

body {
  margin: 0;
}

/* 覆盖Chrome浏览器自动填充样式 */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px white inset !important;
  -webkit-text-fill-color: inherit !important;
  transition: background-color 5000s ease-in-out 0s;
  background-clip: content-box !important;
}

.next-dialog-body {
  padding: 0;
}

.next-balloon-medium {
  padding: 8px;
}

.activity-setting-form {
  flex: 1;
  padding-bottom: 50px;

  .next-form-item {
    display: flex;
  }

  .next-form-item-label {
    flex-shrink: 0;
    width: 155px;
  }

  .next-form-item-control {
    flex: 1;
  }

  .container-block {
    &:last-child {
      margin-bottom: 0 !important;
    }
  }

  .form-item-text {
    line-height: 42px;
  }
}

.activity-setting {
  flex: 1;

  .next-form-item {
    display: flex;
  }

  .next-form-item-label {
    flex-shrink: 0;
    width: 155px;
  }

  .next-form-item-control {
    flex: 1;
  }

  .container-block {
    &:last-child {
      margin-bottom: 0 !important;
    }
  }

  .form-item-text {
    line-height: 42px;
  }
}

.step {
  .next-step-item-body {
    overflow: visible !important;
  }

  .next-step-item-title {
    word-break: keep-all !important;
  }
}

/* 选择奖品-编辑奖品弹窗-表单 */
.edit-prize-form {
  flex: 1;

  .next-form-item {
    display: flex;
  }

  .next-form-item-label {
    flex-shrink: 0;
    width: 135px;
  }

  .next-form-item-control {
    flex: 1;
  }

  .form-item-text {
    line-height: 42px;
  }

  .next-input.next-disabled {
    background-image: none;
  }
}

.form-extra {
  padding: 12px 0;
  color: #999;
  font-size: 12px;
  line-height: 1 !important;
  display: flex;
  flex-direction: row;
}

.form-extra-red {
  padding: 12px 0;
  color: var(--primary-color);
  font-size: 12px;
  line-height: 1 !important;
}

.next-form.next-large {
  .next-form-item-control {
    font-size: 14px;
  }
}

.next-message-content {
  white-space: break-spaces;
}

.error-message {
  display: flex;
  flex-direction: column;
  gap: 8px;

  p {
    margin: 0;
    font-size: 16px;
    line-height: 1.3;
  }
}

/* 状态 */
.status-text {
  display: block;
  width: fit-content;
  height: 24px;
  padding: 0 10px;
  line-height: 24px;
  white-space: nowrap;
  text-align: center;
  word-break: keep-all;
  border-radius: 5px;
}

.status-normal {
  @extend .status-text;

  color: #078d07;
  background-color: #e6ffe6;
}

.status-blue {
  @extend .status-text;

  color: #3d7fff;
  background-color: #f0f5ff;
}

.status-before {
  @extend .status-text;

  color: #111;
  background-color: #f7f8fa;
}

.status-disabled {
  @extend .status-text;

  color: #a6a9ae;
  background-color: #f7f8fa;
}

.status-yellow {
  @extend .status-text;

  color: #ff9419;
  background-color: #ffece4;
}

.status-red {
  @extend .status-text;

  color: #f56c6c;
  background-color: #fef0f0;
}

.text-primary {
  color: var(--primary-color) !important;
}

.text-red {
  color: var(--primary-text-red) !important;
}

.text-overflow-line {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.text-overflow-2-line {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal;
  text-overflow: ellipsis;
  word-wrap: break-word;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.text-overflow-3-line {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal;
  text-overflow: ellipsis;
  word-wrap: break-word;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}