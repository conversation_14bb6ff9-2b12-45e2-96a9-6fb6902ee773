/**
 * The page's HTML template structure, using JSX.
 */
import { Meta, Title, Links, Main, Scripts } from 'ice';
import { description } from '../package.json';

export default function Document() {
  return (
    <html>
      <head>
        <meta charSet="utf-8" />
        <meta name="description" content={description} />
        <link rel="icon" href="/favicon.ico" />
        <link rel="stylesheet" type="text/css" href="https://lzcdn.dianpusoft.cn/fonts/alifont/lates.css" />
        {/* <link rel="stylesheet" href="https://lzcdn.dianpusoft.cn/fusion/theme-vf/1.27.32/next.min.css?a=10087" /> */}
        <link rel="stylesheet" href="https://lzcdn.dianpusoft.cn/fusion/theme-vf/1.27.32/next-main.min.css?a=0529" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover"
        />
        <Meta />
        <Title />
        <Links />
      </head>
      <body>
        <Main onError={(e) => {
          console.log(e);
        }}
        />
        <Scripts />
      </body>
    </html>
  );
}
